package com.wexl.saisenior.reportcard.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import com.wexl.retail.term.repository.TermRepository;
import com.wexl.saisenior.SaiSeniorBaseReportCardDefinition;
import com.wexl.saisenior.reportcard.dto.ReportCardDto;
import com.wexl.saisenior.reportcard.dto.SaiSenior9to12ReportDto;
import com.wexl.saisenior.reportcard.repository.SaiSeniorRepository;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SaiSenior9to12GradeTerm1Report extends SaiSeniorBaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final SaiSeniorRepository saiSeniorRepository;
  private final TermAssessmentRepository termAssessmentRepository;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;
  private final TermRepository termRepository;

  @Override
  public Map<String, Object> build(
      User user, Organization org, com.wexl.retail.offlinetest.dto.ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("sai-senior-9th-12th-term1-report.xml");
  }

  public SaiSenior9to12ReportDto.Body buildBody(User user) {
    var student = user.getStudentInfo();
    var guardians = student.getGuardians();
    var father =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.FATHER)).findAny();
    var mother =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.MOTHER)).findAny();
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
    String formattedDateOfBirth =
        dateOfBirth
            .map(StudentAttributeValueModel::getValue)
            .map(dateString -> LocalDate.parse(dateString))
            .map(date -> date.format(outputFormatter))
            .orElse(null);
    Optional<StudentAttributeValueModel> admissionNo =
        reportCardService.getStudentAttributeValue(student, "admission_no");
    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            Collections.singletonList("t1"), student.getSection().getGradeSlug());
    var gradeSlug = student.getSection().getGradeSlug();
    var studentAttendance = getAttendance(student.getId());
    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();
    var marks =
        saiSeniorRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(marks) || marks.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }
    var firstTable = buildFirstTable(marks);
    return SaiSenior9to12ReportDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(formattedDateOfBirth)
        .className(student.getSection().getName())
        .rollNumber(student.getRollNumber())
        .admissionNo(admissionNo.map(StudentAttributeValueModel::getValue).orElse(null))
        .fathersName(
            father
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .mothersName(
            mother
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .firstTable(firstTable)
        .secondTable(secondTable(firstTable, gradeSlug, studentAttendance))
        .generalRemark(studentAttendance.remarks())
        .build();
  }

  private SaiSenior9to12ReportDto.SecondTable secondTable(
      SaiSenior9to12ReportDto.FirstTable firstTable,
      String gradeSlug,
      ReportCardDto.Attendance studentAttendance) {
    var percentage = calculatePercentage(firstTable, gradeSlug);
    return SaiSenior9to12ReportDto.SecondTable.builder()
        .result(calculateOverAllGrade(firstTable, gradeSlug))
        .percentage(percentage)
        .attendance(studentAttendance.attendancePercentage())
        .build();
  }

  private SaiSenior9to12ReportDto.FirstTable buildFirstTable(List<LowerGradeReportCardData> marks) {

    return SaiSenior9to12ReportDto.FirstTable.builder()
        .subject(buildFirstTableMarks(marks))
        .build();
  }

  private List<SaiSenior9to12ReportDto.Marks> buildFirstTableMarks(
      List<LowerGradeReportCardData> marks) {
    List<SaiSenior9to12ReportDto.Marks> marksList = new ArrayList<>();

    var data =
        marks.stream()
            .filter(x -> x.getCategory().equals(SubjectsCategoryEnum.SCHOLASTIC.name()))
            .toList();

    var subjectsList =
        data.stream()
            .sorted(Comparator.comparing(LowerGradeReportCardData::getSeqNo))
            .map(LowerGradeReportCardData::getSubjectName)
            .distinct()
            .toList();
    subjectsList.forEach(
        subject -> {
          var subjectData = data.stream().filter(x -> x.getSubjectName().equals(subject)).toList();
          var fa = buildFa(subjectData, subject);
          var faTotalMarks = getTotalMarks(subjectData, subject, "i.a");
          var sa = buildMarks(subjectData, "s.a", subject);
          var saTotalMarks = getTotalMarks(subjectData, subject, "s.a");

          marksList.add(
              SaiSenior9to12ReportDto.Marks.builder()
                  .name(subject)
                  .saTotalMarks(saTotalMarks)
                  .faTotalMarks(faTotalMarks)
                  .internalAssessment(fa)
                  .summativeAssessment(sa)
                  .build());
        });

    var sortedMarks = sortMarks(marksList);
    return updatedMarks(marksList, sortedMarks);
  }

  private List<SaiSenior9to12ReportDto.Marks> updatedMarks(
      List<SaiSenior9to12ReportDto.Marks> marksList,
      List<SaiSenior9to12ReportDto.Marks> sortedMarks) {
    List<SaiSenior9to12ReportDto.Marks> response = new ArrayList<>();

    marksList.forEach(
        marks -> {
          var subjectName =
              sortedMarks.stream().filter(x -> x.name().equals(marks.name())).findAny();

          if (subjectName.isPresent()) {
            var data = subjectName.get();
            boolean isValidDataInternalAssessment =
                !"AB".equals(data.internalAssessment())
                    && !"PA".equals(data.internalAssessment())
                    && !"ML".equals(data.internalAssessment());

            boolean isValidDataSummativeAssessment =
                !"AB".equals(data.summativeAssessment())
                    && !"PA".equals(data.summativeAssessment())
                    && !"ML".equals(data.summativeAssessment());

            response.add(
                SaiSenior9to12ReportDto.Marks.builder()
                    .seqNo(marks.seqNo())
                    .name(marks.name())
                    .colSpan(data.colSpan() == null ? 0 : data.colSpan())
                    .internalAssessment(
                        isValidDataInternalAssessment
                            ? formatScore(
                                Double.parseDouble(data.internalAssessment()), marks.faTotalMarks())
                            : marks.internalAssessment())
                    .summativeAssessment(
                        isValidDataSummativeAssessment
                            ? formatScore(
                                Double.parseDouble(data.summativeAssessment()),
                                marks.saTotalMarks())
                            : marks.summativeAssessment())
                    .percentage(data.percentage() == null ? null : formatMarks(data.percentage()))
                    .build());
          } else {

            boolean isValidInternalAssessment =
                !"AB".equals(marks.internalAssessment())
                    && !"PA".equals(marks.internalAssessment())
                    && !"ML".equals(marks.internalAssessment());
            boolean isValidSummativeAssessment =
                !"AB".equals(marks.summativeAssessment())
                    && !"PA".equals(marks.summativeAssessment())
                    && !"ML".equals(marks.summativeAssessment());

            var totalMarks = marks.faTotalMarks() + marks.faTotalMarks();
            var total = totalMarks == 200 ? 2 : 1;
            Double percentage = null;
            if (isValidInternalAssessment && isValidSummativeAssessment) {
              percentage =
                  (Double.parseDouble(marks.internalAssessment())
                          + Double.parseDouble(marks.summativeAssessment()))
                      / total;
            } else if (isValidInternalAssessment) {
              percentage = Double.parseDouble(marks.internalAssessment()) / total;
            } else if (isValidSummativeAssessment) {
              percentage = Double.parseDouble(marks.summativeAssessment()) / total;
            }

            response.add(
                SaiSenior9to12ReportDto.Marks.builder()
                    .seqNo(marks.seqNo())
                    .name(marks.name())
                    .colSpan(marks.colSpan() == null ? 1 : marks.colSpan())
                    .internalAssessment(
                        isValidInternalAssessment
                            ? formatScore(
                                Double.parseDouble(marks.internalAssessment()),
                                marks.faTotalMarks())
                            : marks.internalAssessment())
                    .summativeAssessment(
                        isValidSummativeAssessment
                            ? formatScore(
                                Double.parseDouble(marks.summativeAssessment()),
                                marks.saTotalMarks())
                            : marks.summativeAssessment())
                    .percentage(percentage)
                    .build());
          }
        });

    return response;
  }

  private List<SaiSenior9to12ReportDto.Marks> sortMarks(
      List<SaiSenior9to12ReportDto.Marks> subjectData) {
    List<String> commonSubjects = Arrays.asList("english", "social", "science -");
    List<SaiSenior9to12ReportDto.Marks> sortedMarks = new ArrayList<>();

    commonSubjects.forEach(
        subject -> {
          var filteredSubjects =
              subjectData.stream().filter(x -> x.name().toLowerCase().contains(subject)).toList();

          if (!filteredSubjects.isEmpty()) {
            double totalSummative = getTotalMarksForAssessment(filteredSubjects, "summative");
            double totalInternal = getTotalMarksForAssessment(filteredSubjects, "internal");
            double percentage =
                formatMarks(totalInternal + totalSummative) / filteredSubjects.size();

            for (int i = 0; i < filteredSubjects.size(); i++) {
              SaiSenior9to12ReportDto.Marks mark = filteredSubjects.get(i);
              long colSpan = 0L;

              if (i == filteredSubjects.size() - 1) {
                colSpan = filteredSubjects.size();
              }

              sortedMarks.add(
                  SaiSenior9to12ReportDto.Marks.builder()
                      .name(mark.name())
                      .internalAssessment(mark.internalAssessment())
                      .faTotalMarks(mark.faTotalMarks())
                      .saTotalMarks(mark.saTotalMarks())
                      .summativeAssessment(String.valueOf(mark.summativeAssessment()))
                      .percentage(i == 0 ? percentage : null)
                      .seqNo(mark.seqNo())
                      .colSpan(colSpan)
                      .build());
            }
          }
        });

    return sortedMarks;
  }

  private double getTotalMarksForAssessment(
      List<SaiSenior9to12ReportDto.Marks> marks, String type) {
    return marks.stream()
        .map(
            mark ->
                type.equals("summative") ? mark.summativeAssessment() : mark.internalAssessment())
        .filter(
            assessment ->
                !assessment.equals("AB") && !assessment.equals("PA") && !assessment.equals("ML"))
        .mapToDouble(Double::parseDouble)
        .sum();
  }

  private String formatScore(double score, double total) {
    if (score == (long) score) {
      return String.format("%d/%d", (long) score, (long) total);
    } else {
      return String.format("%.1f/%.1f", score, total);
    }
  }

  private Double getTotalMarks(
      List<LowerGradeReportCardData> subjectData, String subject, String assessmentSlug) {
    return subjectData.stream()
        .filter(
            x -> x.getSubjectName().equals(subject) && x.getAssessmentSlug().equals(assessmentSlug))
        .map(
            x ->
                x.getIsAttended() == null || x.getIsAttended().equals("False")
                    ? "A"
                    : String.valueOf(x.getTotalMarks()))
        .reduce(
            (mark1, mark2) -> {
              if ("A".equals(mark1) || "A".equals(mark2)) return "A";
              return String.valueOf(Math.max(Double.parseDouble(mark1), Double.parseDouble(mark2)));
            })
        .map(mark -> "A".equals(mark) ? 0.0 : Double.parseDouble(mark))
        .orElse(0.0);
  }

  private String buildMarks(
      List<LowerGradeReportCardData> subjectData, String assessmentSlug, String subjectName) {
    return subjectData.stream()
        .filter(
            x ->
                x.getSubjectName().equals(subjectName)
                    && x.getAssessmentSlug().equals(assessmentSlug))
        .findAny()
        .map(
            fa -> {
              if (fa.getIsAttended() == null || "false".equals(fa.getIsAttended())) {
                if (fa.getRemarks() != null) {
                  if (fa.getRemarks().contains("ML")) {
                    return "ML";
                  } else if (fa.getRemarks().contains("PA")) {
                    return "PA";
                  } else if (fa.getRemarks().contains("Absent")) {
                    return "AB";
                  }
                }
                return "AB";
              } else {
                return fa.getMarks() != null ? fa.getMarks().toString() : "-";
              }
            })
        .orElse("-");
  }

  private String buildFa(List<LowerGradeReportCardData> subjectData, String subject) {
    boolean anyValidMarks =
        subjectData.stream()
            .filter(
                x ->
                    x.getSubjectName().equals(subject)
                        && x.getAssessmentSlug().equals("i.a")
                        && x.getMarks() != null
                        && x.getRemarks() == null)
            .anyMatch(x -> true);

    if (anyValidMarks) {
      double highestMark =
          subjectData.stream()
              .filter(
                  x ->
                      x.getSubjectName().equals(subject)
                          && x.getAssessmentSlug().equals("i.a")
                          && x.getMarks() != null)
              .map(LowerGradeReportCardData::getMarks)
              .max(Double::compareTo)
              .orElse(0.0);

      return String.valueOf(highestMark);
    } else {
      return subjectData.stream()
          .filter(x -> x.getSubjectName().equals(subject) && x.getAssessmentSlug().equals("i.a"))
          .map(
              x -> {
                if (x.getRemarks() != null) {
                  if (x.getRemarks().contains("ML")) {
                    return "ML";
                  } else if (x.getRemarks().contains("PA")) {
                    return "PA";
                  } else if (x.getRemarks().contains("Absent")) {
                    return "AB";
                  }
                }
                return null;
              })
          .filter(Objects::nonNull)
          .findFirst()
          .orElse("0");
    }
  }

  private Map<String, Double> calculateTotalPercentage(
      SaiSenior9to12ReportDto.FirstTable firstTable, String gradeSlug) {
    int topSubjectsCount = 0;
    if ("x".equals(gradeSlug) || "ix".equals(gradeSlug)) {
      topSubjectsCount = 4;
    } else if ("xi".equals(gradeSlug) || "xii".equals(gradeSlug)) {
      topSubjectsCount = 3;
    }

    Optional<Double> englishPercentage =
        firstTable.subject().stream()
            .filter(
                mark -> mark.name().toLowerCase().contains("english") && mark.percentage() != null)
            .map(SaiSenior9to12ReportDto.Marks::percentage)
            .findFirst();

    List<Double> otherPercentages =
        firstTable.subject().stream()
            .filter(
                mark -> !mark.name().toLowerCase().contains("english") && mark.percentage() != null)
            .map(SaiSenior9to12ReportDto.Marks::percentage)
            .filter(Objects::nonNull)
            .sorted(Comparator.reverseOrder())
            .limit(topSubjectsCount)
            .toList();

    double totalPercentage =
        englishPercentage.orElse(0.0)
            + otherPercentages.stream().mapToDouble(Double::doubleValue).sum();

    int subjectCount =
        englishPercentage.isPresent() ? otherPercentages.size() + 1 : otherPercentages.size();

    Map<String, Double> result = new HashMap<>();
    result.put("totalPercentage", totalPercentage);
    result.put("subjectCount", (double) subjectCount);

    return result;
  }

  private double calculatePercentage(
      SaiSenior9to12ReportDto.FirstTable firstTable, String gradeSlug) {
    Map<String, Double> result = calculateTotalPercentage(firstTable, gradeSlug);
    double totalPercentage = result.get("totalPercentage");
    int subjectCount = result.get("subjectCount").intValue();

    return subjectCount == 0 ? 0.0 : formatMarks((totalPercentage / subjectCount) * 100.0 / 100.0);
  }

  private String calculateOverAllGrade(
      SaiSenior9to12ReportDto.FirstTable firstTable, String gradeSlug) {
    boolean isEnglishFailing =
        firstTable.subject().stream()
            .filter(
                mark -> mark.name().toLowerCase().contains("english") && mark.percentage() != null)
            .anyMatch(mark -> determinePassOrFail(mark.percentage()).equals("Fail"));

    if (isEnglishFailing) {
      return "Not Clear";
    }
    long failingSubjectsCount =
        firstTable.subject().stream()
            .filter(
                mark -> !mark.name().toLowerCase().contains("english") && mark.percentage() != null)
            .filter(mark -> determinePassOrFail(mark.percentage()).equals("Fail"))
            .count();

    if (failingSubjectsCount == 1) {
      return "Pass";
    } else if (failingSubjectsCount > 1) {
      return "Not Clear";
    }

    double calculatedPercentage = calculatePercentage(firstTable, gradeSlug);
    return String.format("Pass", calculatedPercentage);
  }

  private ReportCardDto.Attendance getAttendance(long studentId) {
    var term = termRepository.findBySlug("t1").orElseThrow();
    var termAssessment = termAssessmentRepository.findBySlugAndTerm("s.a", term);
    if (termAssessment.isEmpty()) {
      return ReportCardDto.Attendance.builder().build();
    }
    var studentAttendance =
        offlineTestScheduleStudentAttendanceRepository.getOfflineTestStudentAttendance(
            studentId, termAssessment.get().getId());
    return buildAttendance(studentId, studentAttendance);
  }
}
