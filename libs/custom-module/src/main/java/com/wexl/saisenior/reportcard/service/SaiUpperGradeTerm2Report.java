package com.wexl.saisenior.reportcard.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import com.wexl.retail.term.repository.TermRepository;
import com.wexl.saisenior.SaiSeniorBaseReportCardDefinition;
import com.wexl.saisenior.reportcard.dto.ReportCardDto;
import com.wexl.saisenior.reportcard.dto.SaiSeniorUpperGradeTerm2Dto;
import com.wexl.saisenior.reportcard.repository.SaiSeniorRepository;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SaiUpperGradeTerm2Report extends SaiSeniorBaseReportCardDefinition {

  private final List<String> terms = List.of("t1", "t2");
  private final ReportCardService reportCardService;
  private final SaiSeniorRepository saiSeniorRepository;
  private final TermAssessmentRepository termAssessmentRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;
  private final TermRepository termRepository;

  @Override
  public Map<String, Object> build(
      User user, Organization org, com.wexl.retail.offlinetest.dto.ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("sai-senior-6th-8th-final-progress-report.xml");
  }

  public SaiSeniorUpperGradeTerm2Dto.Body buildBody(User user) {
    var student = user.getStudentInfo();
    var guardians = student.getGuardians();

    var father =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.FATHER)).findAny();
    var mother =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.MOTHER)).findAny();
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
    String formattedDateOfBirth =
        dateOfBirth
            .map(StudentAttributeValueModel::getValue)
            .map(LocalDate::parse)
            .map(date -> date.format(outputFormatter))
            .orElse(null);
    Optional<StudentAttributeValueModel> admissionNo =
        reportCardService.getStudentAttributeValue(student, "admission_no");
    var studentAttendance = getAttendance(student.getId());
    var marks =
        saiSeniorRepository.getStudentT2ReportByStudentAndAssessments(
            student.getId(), student.getSection().getGradeSlug(), terms);
    if (Objects.isNull(marks) || marks.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }
    var firstTable = buildFirstTable(marks);
    return SaiSeniorUpperGradeTerm2Dto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(formattedDateOfBirth)
        .className(student.getSection().getName())
        .rollNumber(student.getRollNumber())
        .admissionNumber(admissionNo.map(StudentAttributeValueModel::getValue).orElse(null))
        .fathersName(
            father
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .mothersName(
            mother
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .firstTable(firstTable)
        .secondTable(buildSecondTable(marks))
        .thirdTable(buildThirdTable(marks))
        .fourthTable(buildFourthTable(firstTable, studentAttendance))
        .generalRemark(studentAttendance.remarks())
        .build();
  }

  private SaiSeniorUpperGradeTerm2Dto.FourthTable buildFourthTable(
      SaiSeniorUpperGradeTerm2Dto.FirstTable firstTable,
      ReportCardDto.Attendance studentAttendance) {
    return SaiSeniorUpperGradeTerm2Dto.FourthTable.builder()
        .result(calculateOverAllGrade(firstTable))
        .academic(buildPercentage(firstTable))
        .overall(calculateOverAllPercentage(firstTable))
        .attendance(studentAttendance.attendancePercentage())
        .build();
  }

  private SaiSeniorUpperGradeTerm2Dto.ThirdTable buildThirdTable(
      List<LowerGradeReportCardData> marks) {
    List<LowerGradeReportCardData> data;
    data =
        marks.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.CURRICULAR_ACTIVITIES.name()))
            .sorted(
                Comparator.comparing(LowerGradeReportCardData::getSeqNo, Comparator.naturalOrder()))
            .toList();
    if (data.isEmpty()) {
      return SaiSeniorUpperGradeTerm2Dto.ThirdTable.builder().build();
    }
    return SaiSeniorUpperGradeTerm2Dto.ThirdTable.builder()
        .marks(buildThirdTableMarks(data))
        .build();
  }

  private List<SaiSeniorUpperGradeTerm2Dto.ThirdTableMarks> buildThirdTableMarks(
      List<LowerGradeReportCardData> data) {
    List<SaiSeniorUpperGradeTerm2Dto.ThirdTableMarks> marksList = new ArrayList<>();
    List<String> subjects;
    subjects = data.stream().map(LowerGradeReportCardData::getSubjectName).distinct().toList();
    subjects.forEach(
        subject -> {
          var subjectData = data.stream().filter(x -> x.getSubjectName().equals(subject)).toList();

          var optionalTerm2 =
              subjectData.stream().filter(x -> x.getAssessmentSlug().equals("s.a")).findAny();

          String term2Grade =
              optionalTerm2
                  .map(
                      term2 -> {
                        if ("false".equalsIgnoreCase(term2.getIsAttended())
                            && term2.getRemarks() == null) {
                          return "AB";
                        }
                        return calculateGrade(
                            term2.getMarks(), term2.getTotalMarks(), term2.getRemarks());
                      })
                  .orElse("");
          marksList.add(
              SaiSeniorUpperGradeTerm2Dto.ThirdTableMarks.builder()
                  .name(subject)
                  .grade(term2Grade)
                  .build());
        });
    return marksList;
  }

  private String calculateGrade(Double marks, Double totalMarks, String remarks) {
    return marks == null || marks == 0
        ? (remarks == null ? null : remarks.substring(0, 2).toUpperCase())
        : pointScaleEvaluator.evaluate("5point", BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private SaiSeniorUpperGradeTerm2Dto.FirstTable buildFirstTable(
      List<LowerGradeReportCardData> marks) {
    return SaiSeniorUpperGradeTerm2Dto.FirstTable.builder()
        .marks(buildFirstTableMarks(marks))
        .build();
  }

  private List<SaiSeniorUpperGradeTerm2Dto.Marks> buildFirstTableMarks(
      List<LowerGradeReportCardData> marks) {
    List<SaiSeniorUpperGradeTerm2Dto.Marks> marksList = new ArrayList<>();
    var data =
        marks.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.MANDATORY.name()))
            .toList();

    var subjects = data.stream().map(LowerGradeReportCardData::getSubjectName).distinct().toList();

    subjects.forEach(
        subject -> {
          var subjectData = data.stream().filter(x -> x.getSubjectName().equals(subject)).toList();

          var fa = buildFa(subjectData, subject, "t1");
          var fa2 = buildFa(subjectData, subject, "t2");
          var sa = buildMarks(subjectData, "s.a", subject, "t1");
          var sa2 = buildMarks(subjectData, "s.a", subject, "t2");
          var ia = buildMarks(subjectData, "i.a", subject, "t1");
          var ia2 = buildMarks(subjectData, "i.a", subject, "t2");

          double faValue = parseOrZero(fa);
          double fa2Value = parseOrZero(fa2);
          double saValue = parseOrZero(sa);
          double sa2Value = parseOrZero(sa2);
          double iaValue = parseOrZero(ia);
          double ia2Value = parseOrZero(ia2);
          double academics = faValue + saValue + fa2Value + sa2Value;
          double percentage = faValue + saValue + iaValue + ia2Value + fa2Value + sa2Value;

          marksList.add(
              SaiSeniorUpperGradeTerm2Dto.Marks.builder()
                  .subjectName(subject)
                  .seqNo(subjectData.get(0).getSeqNo())
                  .term1SA(sa)
                  .term1IA(ia)
                  .term1FA(fa)
                  .term2IA(ia2)
                  .term2FA(fa2)
                  .term2SA(sa2)
                  .academics(String.valueOf(academics))
                  .overAll(String.valueOf(percentage))
                  .build());
        });
    return updatedMarks(marksList, sortMarks(marksList));
  }

  private List<SaiSeniorUpperGradeTerm2Dto.Marks> updatedMarks(
      List<SaiSeniorUpperGradeTerm2Dto.Marks> marksList,
      List<SaiSeniorUpperGradeTerm2Dto.Marks> sortedMarks) {
    List<SaiSeniorUpperGradeTerm2Dto.Marks> response = new ArrayList<>();

    marksList.forEach(
        marks -> {
          var subjectName =
              sortedMarks.stream()
                  .filter(x -> x.subjectName().equals(marks.subjectName()))
                  .findAny();
          if (subjectName.isPresent()) {
            var data = subjectName.get();
            response.add(
                SaiSeniorUpperGradeTerm2Dto.Marks.builder()
                    .subjectName(data.subjectName())
                    .term1SA(data.term1SA())
                    .term1IA(data.term1IA())
                    .term1FA(data.term1FA())
                    .term2FA(data.term2FA())
                    .term2SA(data.term2SA())
                    .term2IA(data.term2IA())
                    .overAll(data.overAll())
                    .academics(data.academics())
                    .seqNo(data.seqNo())
                    .colSpan(data.colSpan())
                    .build());
          } else {

            response.add(
                SaiSeniorUpperGradeTerm2Dto.Marks.builder()
                    .subjectName(marks.subjectName())
                    .term1SA(marks.term1SA())
                    .term1IA(marks.term1IA())
                    .term1FA(marks.term1FA())
                    .term2FA(marks.term2FA())
                    .term2SA(marks.term2SA())
                    .term2IA(marks.term2IA())
                    .overAll(
                        String.format("%.2f", ((Double.parseDouble(marks.overAll()) / 300) * 100)))
                    .academics(
                        String.format(
                            "%.2f", ((Double.parseDouble(marks.academics()) / 200) * 100)))
                    .seqNo(marks.seqNo())
                    .colSpan(1L)
                    .build());
          }
        });

    return response.stream()
        .sorted(Comparator.comparing(SaiSeniorUpperGradeTerm2Dto.Marks::seqNo))
        .toList();
  }

  private List<SaiSeniorUpperGradeTerm2Dto.Marks> sortMarks(
      List<SaiSeniorUpperGradeTerm2Dto.Marks> subjectData) {
    List<String> commonSubjects = Arrays.asList("english", "social", "science -");
    List<SaiSeniorUpperGradeTerm2Dto.Marks> sortedMarks = new ArrayList<>();

    commonSubjects.forEach(
        subject -> {
          var filteredSubjects =
              subjectData.stream()
                  .filter(x -> x.subjectName().toLowerCase().contains(subject))
                  .sorted(Comparator.comparing(SaiSeniorUpperGradeTerm2Dto.Marks::seqNo))
                  .toList();

          if (!filteredSubjects.isEmpty()) {
            double academicsTotal = getAcademicsTotal(filteredSubjects);
            double overallTotal = getOverallTotal(filteredSubjects);
            var academicsSubjectsTotal = filteredSubjects.size() * 200;
            var overallSubjectsTotal = filteredSubjects.size() * 300;

            String academicsPercentage =
                String.format("%.2f", (academicsTotal / academicsSubjectsTotal) * 100);

            String overallPercentage =
                String.format("%.2f", (overallTotal / overallSubjectsTotal) * 100);

            for (int i = 0; i < filteredSubjects.size(); i++) {
              SaiSeniorUpperGradeTerm2Dto.Marks marks = filteredSubjects.get(i);
              long colSpan = 0L;

              if (i == filteredSubjects.size() - 1) {
                colSpan = filteredSubjects.size();
              }

              sortedMarks.add(
                  SaiSeniorUpperGradeTerm2Dto.Marks.builder()
                      .subjectName(marks.subjectName())
                      .term1SA(marks.term1SA())
                      .term1IA(marks.term1IA())
                      .term1FA(marks.term1FA())
                      .term2FA(marks.term2FA())
                      .term2SA(marks.term2SA())
                      .term2IA(marks.term2IA())
                      .overAll(i == 0 ? overallPercentage : null)
                      .academics(i == 0 ? academicsPercentage : null)
                      .seqNo(marks.seqNo())
                      .colSpan(colSpan)
                      .build());
            }
          }
        });

    return sortedMarks;
  }

  private double getOverallTotal(List<SaiSeniorUpperGradeTerm2Dto.Marks> marks) {
    return marks.stream()
        .filter(
            mark ->
                !Objects.equals(mark.overAll(), "AB")
                    && !Objects.equals(mark.overAll(), "PL")
                    && !Objects.equals(mark.overAll(), "ML"))
        .mapToDouble(mark -> Double.parseDouble(String.valueOf(mark.overAll())))
        .sum();
  }

  private double getAcademicsTotal(List<SaiSeniorUpperGradeTerm2Dto.Marks> marks) {
    return marks.stream()
        .filter(
            mark ->
                !Objects.equals(mark.academics(), "AB")
                    && !Objects.equals(mark.academics(), "PL")
                    && !Objects.equals(mark.academics(), "ML"))
        .mapToDouble(mark -> Double.parseDouble(String.valueOf(mark.academics())))
        .sum();
  }

  private String buildMarks(
      List<LowerGradeReportCardData> subjectData,
      String assessmentSlug,
      String subjectName,
      String termSlug) {
    return subjectData.stream()
        .filter(
            x ->
                x.getSubjectName().equals(subjectName)
                    && x.getAssessmentSlug().equals(assessmentSlug)
                    && x.getTermSlug().equals(termSlug))
        .findAny()
        .map(
            fa -> {
              if (fa.getIsAttended() == null || "false".equals(fa.getIsAttended())) {
                if (fa.getRemarks() != null) {
                  if (fa.getRemarks().contains("ML")) {
                    return "ML";
                  } else if (fa.getRemarks().contains("PL")) {
                    return "PL";
                  } else if (fa.getRemarks().contains("Absent")) {
                    return "AB";
                  }
                }
                return "AB";
              } else {
                return fa.getMarks() != null ? fa.getMarks().toString() : "-";
              }
            })
        .orElse("-");
  }

  private String buildFa(
      List<LowerGradeReportCardData> subjectData, String subject, String termSlug) {
    return subjectData.stream()
        .filter(
            x ->
                x.getSubjectName().equals(subject)
                    && x.getAssessmentSlug().equals("f.a")
                    && x.getTermSlug().equals(termSlug))
        .map(this::determineMarkOrRemark)
        .filter(mark -> !mark.equals("AB") && !mark.equals("PL") && !mark.equals("ML"))
        .map(Double::parseDouble)
        .max(Double::compareTo)
        .map(String::valueOf)
        .orElseGet(() -> getDefaultRemark(subjectData, subject));
  }

  private String determineMarkOrRemark(LowerGradeReportCardData data) {
    if (data.getIsAttended() == null || Boolean.FALSE.toString().equals(data.getIsAttended())) {
      return getRemark(data);
    }
    return Optional.ofNullable(data.getMarks()).map(String::valueOf).orElse("0");
  }

  private String getRemark(LowerGradeReportCardData data) {
    if (data.getRemarks() == null) {
      return "AB";
    }

    String remarks = data.getRemarks();
    if (remarks.contains("ML")) {
      return "ML";
    } else if (remarks.contains("PL")) {
      return "PL";
    } else if (remarks.contains("Absent")) {
      return "AB";
    }
    return "AB";
  }

  private String getDefaultRemark(List<LowerGradeReportCardData> subjectData, String subject) {
    return subjectData.stream()
        .filter(x -> x.getSubjectName().equals(subject) && x.getAssessmentSlug().equals("f.a"))
        .map(this::getRemark)
        .findFirst()
        .orElse("AB");
  }

  private SaiSeniorUpperGradeTerm2Dto.SecondTable buildSecondTable(
      List<LowerGradeReportCardData> marks) {
    var data =
        marks.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();
    if (data.isEmpty()) {
      return SaiSeniorUpperGradeTerm2Dto.SecondTable.builder().build();
    }
    return SaiSeniorUpperGradeTerm2Dto.SecondTable.builder()
        .marks(buildSecondTableMarks(data))
        .build();
  }

  private List<SaiSeniorUpperGradeTerm2Dto.SecondTableMarks> buildSecondTableMarks(
      List<LowerGradeReportCardData> data) {
    List<SaiSeniorUpperGradeTerm2Dto.SecondTableMarks> marksList = new ArrayList<>();
    List<String> subjects;
    subjects = data.stream().map(LowerGradeReportCardData::getSubjectName).distinct().toList();
    subjects.forEach(
        subject -> {
          var subjectData = data.stream().filter(x -> x.getSubjectName().equals(subject)).toList();
          marksList.add(
              SaiSeniorUpperGradeTerm2Dto.SecondTableMarks.builder()
                  .subject(subject)
                  .term1Grade(calculateGrade(subjectData, "t1"))
                  .term2Grade(calculateGrade(subjectData, "t2"))
                  .build());
        });
    return marksList;
  }

  private String calculateOverAllGrade(SaiSeniorUpperGradeTerm2Dto.FirstTable firstTable) {
    boolean hasFailingMark =
        firstTable.marks().stream()
            .filter(
                marks ->
                    marks != null
                        && marks.overAll() != null
                        && !Objects.equals(marks.overAll(), "0.0"))
            .anyMatch(
                marks -> "Fail".equals(determinePassOrFail(Double.parseDouble(marks.academics()))));

    return hasFailingMark ? "Not Clear" : "Pass";
  }

  private double calculateOverAllPercentage(SaiSeniorUpperGradeTerm2Dto.FirstTable firstTable) {
    double totalMarks =
        firstTable.marks().stream()
            .filter(x -> x.academics() != null)
            .map(x -> Double.parseDouble(x.overAll()))
            .mapToDouble(Double::doubleValue)
            .sum();

    return totalMarks > 0 ? formatMarks(totalMarks / 5) : 0.00;
  }

  private double buildPercentage(SaiSeniorUpperGradeTerm2Dto.FirstTable firstTable) {
    double totalMarks =
        firstTable.marks().stream()
            .filter(x -> x.academics() != null)
            .map(x -> Double.parseDouble(x.academics()))
            .mapToDouble(Double::doubleValue)
            .sum();

    return totalMarks > 0 ? formatMarks(totalMarks / 5) : 0.00;
  }

  private String calculateGrade(List<LowerGradeReportCardData> marks, String termSlug) {
    return marks.stream()
        .filter(x -> termSlug.equals(x.getTermSlug()))
        .filter(x -> x.getMarks() != null && x.getTotalMarks() != null && x.getTotalMarks() != 0)
        .findAny()
        .filter(Objects::nonNull)
        .map(
            x -> {
              if ("false".equalsIgnoreCase(x.getIsAttended())) {
                return x.getRemarks() == null ? "AB" : x.getRemarks();
              }
              double percentage = x.getMarks() > 0 ? (x.getMarks() / x.getTotalMarks()) * 100 : 0;
              return pointScaleEvaluator.evaluate("5point", BigDecimal.valueOf(percentage));
            })
        .orElse(null);
  }

  private ReportCardDto.Attendance getAttendance(long studentId) {
    var term = termRepository.findBySlug("t2").orElseThrow();
    var termAssessment = termAssessmentRepository.findBySlugAndTerm("s.a", term);
    if (termAssessment.isEmpty()) {
      return ReportCardDto.Attendance.builder().build();
    }
    var studentAttendance =
        offlineTestScheduleStudentAttendanceRepository.getOfflineTestStudentAttendance(
            studentId, termAssessment.get().getId());
    return buildAttendance(studentId, studentAttendance);
  }
}
