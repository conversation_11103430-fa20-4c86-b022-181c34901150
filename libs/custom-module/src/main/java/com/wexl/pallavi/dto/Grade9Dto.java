package com.wexl.pallavi.dto;

import java.util.List;
import lombok.Builder;

public record Grade9Dto() {

  @Builder
  public record Response(Grade9Dto.Header header, Grade9Dto.Body body) {}

  @Builder
  public record Header(
      String schoolName,
      String academicYear,
      String admissionNumber,
      String address,
      String isoData,
      Long studentId) {}

  @Builder
  public record Body(
      String name,
      String rollNumber,
      String className,
      String mothersName,
      String fathersName,
      String dateOfBirth,
      String section,
      String gradingScale,
      String classTeacher,
      String orgSlug,
      String gradeSlug,
      FirstTable firstTable,
      SecondTable secondTable,
      ThirdTable thirdTable,
      FourthTable fourthTable,
      Attendance attendance,
      GradeTable gradeTable) {}

  @Builder
  public record FirstTable(
      String title,
      String overallPercentage,
      String totalGrade,
      String overallInternalSubjectGrade,
      String totalInternalSubjectMarks,
      String totalInternalSubjectGrade,
      String overAllMarksOfAllSubjects,
      String isPercentage,
      String overAllGradeOfAllSubjectsForPallavi,
      String overAllMarksOfAllSubjectsForPallavi,
      String overAllPercentageOfPallavi,
      List<Marks> marks) {}

  @Builder
  public record SecondTable(String title, List<SecondTableValues> secondTableValues) {}

  @Builder
  public record ThirdTable(String title, List<ThirdTableValues> thirdTableValues) {}

  @Builder
  public record FourthTable(String title, List<ThirdTableValues> fourthTableValues) {}

  @Builder
  public record Attendance(
      String title,
      String remarks,
      Long totalWorkingDays,
      Long daysPresent,
      String attendancePercentage) {}

  @Builder
  public record GradeTable(String title) {}

  @Builder
  public record Marks(
      int sno,
      String subject,
      String bestOfPt,
      String fe,
      String ma,
      String se,
      String po,
      String ae,
      String internalAssessmentMarks,
      String internalAssessmentGrade,
      String annualMarks,
      String totalMarksScored,
      String total,
      String marksForPallavi,
      String gradeForPallavi,
      String grade) {}

  @Builder
  public record SecondTableValues(String subject, String marks, String grade) {}

  @Builder
  public record ThirdTableValues(String subject, String grade) {}
}
