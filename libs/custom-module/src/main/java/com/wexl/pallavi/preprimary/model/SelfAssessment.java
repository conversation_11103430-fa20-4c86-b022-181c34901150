package com.wexl.pallavi.preprimary.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "pallavi_pre_primary_selfAssessment")
public class SelfAssessment extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "grade_slug")
  private String gradeSlug;

  private String name;

  @Column(name = "org_slug")
  private String orgSlug;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinColumn(name = "pallavi_pre_primary_selfAssessment_id")
  private List<SelfAssessmentStudents> students;
}
