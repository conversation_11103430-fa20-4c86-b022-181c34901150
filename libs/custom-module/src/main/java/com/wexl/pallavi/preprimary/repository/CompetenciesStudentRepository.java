package com.wexl.pallavi.preprimary.repository;

import com.wexl.pallavi.preprimary.model.CompetenciesStudents;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CompetenciesStudentRepository extends JpaRepository<CompetenciesStudents, Long> {
  @Query(
      value =
          """
                    select pppcs.*
                    from pallavi_pre_primary_competency_students pppcs
                    join pallavi_pre_primary_competencies pppc on pppc.id = pppcs.pallavi_pre_primary_competencies_id
                    where pppc.org_slug = :orgSlug and pppc.grade_slug = :gradeSlug and pppcs.student_id  = :studentId and pppc.subject_slug = :subjectSlug
                    """,
      nativeQuery = true)
  List<CompetenciesStudents> getCompetenciesByStudentAndGradeAndOrgAndSubject(
      Long studentId, String gradeSlug, String orgSlug, String subjectSlug);
}
