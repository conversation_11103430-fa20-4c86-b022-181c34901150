package com.wexl.pallavi.preprimary.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "pallavi_primary_progress_report")
public class ProgressCard extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private Long studentId;
  private String orgSlug;
  private String gradeSlug;
  private String gradeName;

  @Column(name = "thing_i_like", columnDefinition = "TEXT")
  private String thingsILike;

  @Column(name = "i_live_in", columnDefinition = "TEXT")
  private String iLiveIn;

  @Column(name = "my_friends_are", columnDefinition = "TEXT")
  private String myFriendsAre;

  @Column(name = "my_favourite_colours_are", columnDefinition = "TEXT")
  private String myFavouriteColoursAre;

  @Column(name = "my_favourite_foods", columnDefinition = "TEXT")
  private String myFavouriteFoods;

  @Column(name = "my_favourite_games", columnDefinition = "TEXT")
  private String myFavouriteGames;

  @Column(name = "my_favourite_animals", columnDefinition = "TEXT")
  private String myFavouriteAnimals;

  @Column(name = "a_glimpse_of_myself", columnDefinition = "TEXT")
  private String aGlimpseOfMySelf;

  @Column(name = "a_glimpse_of_myfamily", columnDefinition = "TEXT")
  private String aGlimpseOfMyFamily;

  @Column(name = "learners_portfolio", columnDefinition = "TEXT")
  private String learnersPortFolio;

  @Column(name = "term1_height")
  private String term1Height;

  @Column(name = "term1_weight")
  private String term1Weight;

  @Column(name = "term2_height")
  private String term2Height;

  @Column(name = "term2_weight")
  private String term2Weight;

  @Column(name = "term1_remarks")
  private String term1Remarks;

  @Column(name = "term2_remarks")
  private String term2Remarks;

  @Column(name = "age")
  private Long age;

  @Column(name = "flower")
  private String flower;

  @Column(name = "aim")
  private String aim;

  @Column(name = "subject")
  private String subject;

  @Column(name = "house")
  private String house;

  @Column(name = "blood_group")
  private String bloodGroup;

  @Column(name = "dental")
  private String dental;

  @Column(name = "eye_sight_r")
  private String eyeSightR;

  @Column(name = "eye_sight_l")
  private String eyeSightL;
}
