package com.wexl.pallavi.reports;

import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.pallavi.dto.FirstTerm6To8ReportDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestDefinitionRepository;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class FirstTerm6To8Report extends PallaviBaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final OfflineTestDefinitionRepository offlineTestDefinitionRepository;
  private final TeacherRepository teacherRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = getPallavi6To8Header(user.getStudentInfo(), org);
    var body = buildBody(user, org);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("pallavi-6th-8th-term1-report.xml");
  }

  private FirstTerm6To8ReportDto.Body buildBody(User user, Organization org) {
    var student = user.getStudentInfo();
    var guardians = student.getGuardians();
    var mother =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.MOTHER)).findAny();
    var father =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.FATHER)).findAny();
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            Collections.singletonList("t1"), student.getSection().getGradeSlug());
    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();
    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    var sectionUuid = student.getSection().getUuid();
    var offlineTestDefinition =
        offlineTestDefinitionRepository.findBySectionUuidAndOrgSlug(
            sectionUuid.toString(), org.getSlug().toString());
    var teacherDetails =
        teacherRepository.findById(offlineTestDefinition.getFirst().getTeacherId());
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }
    List<LowerGradeReportCardData> scholasticDataList =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var optionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();

    var coScholasticMandatoryData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.MANDATORY.name()))
            .toList();
    var coScholasticOptionalData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.OPTIONAL.name()))
            .toList();
    return FirstTerm6To8ReportDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .className(
            student.getSection().getGradeSlug().toUpperCase()
                + " - "
                + student.getSection().getName())
        .rollNumber(student.getClassRollNumber())
        .dateOfBirth(dateOfBirth.map(StudentAttributeValueModel::getValue).orElse(null))
        .mothersName(
            mother
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .fathersName(
            father
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .teacherName(
            teacherDetails
                .map(
                    teacher ->
                        teacher.getUserInfo().getFirstName()
                            + " "
                            + teacher.getUserInfo().getLastName())
                .orElse(null))
        .sectionName(user.getStudentInfo().getSection().getName())
        .firstTable(buildFirstTable(scholasticDataList))
        .secondTable(buildSecondTable(optionalData))
        .thirdTable(buildThirdTable(coScholasticMandatoryData, coScholasticOptionalData))
        .gradeTable(buildGradeTable())
        .attendance(buildAttendence(student.getId()))
        .build();
  }

  private FirstTerm6To8ReportDto.FirstTable buildFirstTable(
      List<LowerGradeReportCardData> scholasticDataList) {
    return FirstTerm6To8ReportDto.FirstTable.builder()
        .title("PART I : SCHOLASTIC AREAS")
        .column(buildColumn(scholasticDataList))
        .firstTableMarks(buildFirstTableMarks(scholasticDataList))
        .percentageGrade(buildPercentageGrade(buildFirstTableMarks(scholasticDataList)))
        .build();
  }

  private FirstTerm6To8ReportDto.Column buildColumn(
      List<LowerGradeReportCardData> scholasticDataList) {

    String pa1 = "";
    String nb1 = "";
    String se1 = "";
    String hye = "";

    int pa1Total = 10;
    int nb1Total = 5;
    int se1Total = 5;
    int hyeTotal = 80;

    for (LowerGradeReportCardData data : scholasticDataList) {
      String assessmentSlug = data.getAssessmentSlug();
      pa1 = pa1.isEmpty() ? getColumn("pa1", assessmentSlug) : pa1;
      nb1 = nb1.isEmpty() ? getColumn("nb1", assessmentSlug) : nb1;
      se1 = se1.isEmpty() ? getColumn("se1", assessmentSlug) : se1;
      hye = hye.isEmpty() ? getColumn("hye", assessmentSlug) : hye;
    }

    return FirstTerm6To8ReportDto.Column.builder()
        .pt1(pa1.toUpperCase() + "(" + pa1Total + ")")
        .nb1(nb1.toUpperCase() + "(" + nb1Total + ")")
        .se1(se1.toUpperCase() + "(" + se1Total + ")")
        .hye(hye.toUpperCase() + "(" + hyeTotal + ")")
        .build();
  }

  private String getColumn(String assessmentSlug, String data) {
    return data.equalsIgnoreCase(assessmentSlug) ? data : "";
  }

  private List<FirstTerm6To8ReportDto.firstTableMarks> buildFirstTableMarks(
      List<LowerGradeReportCardData> scholasticDataList) {
    List<FirstTerm6To8ReportDto.firstTableMarks> buildFirstTable = new ArrayList<>();
    var scholasticDataMap =
        scholasticDataList.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var pa1 = getMarks("pa1", scholasticData);
          var nb1 = getMarks("nb1", scholasticData);
          var se1 = getMarks("se1", scholasticData);
          var hye = getMarks("hye", scholasticData);

          var term1Total = sumMarks(pa1, nb1, se1, hye);
          var termMaxMarks = 100d;
          long term1TotalMarks = Math.round(term1Total);
          String grade = calculateGrade((double) term1TotalMarks, termMaxMarks);
          var overallMarksDouble = decimalFormat((double) term1TotalMarks);
          var pa1Marks = calculateMarks("pa1", scholasticData);
          var nb1Marks = calculateMarks("nb1", scholasticData);
          var se1Marks = calculateMarks("se1", scholasticData);
          var hyeMarks = calculateMarks("hye", scholasticData);

          buildFirstTable.add(
              FirstTerm6To8ReportDto.firstTableMarks
                  .builder()
                  .subject(subject)
                  .pt1(pa1Marks)
                  .nb1(nb1Marks)
                  .se1(se1Marks)
                  .hye(hyeMarks)
                  .term1total(String.valueOf(term1Total))
                  .term1Grade(grade)
                  .overallMarks(overallMarksDouble)
                  .overallGrade(grade)
                  .build());
        });
    return buildFirstTable;
  }

  private String calculateMarks(
      String assessmentSlug, List<LowerGradeReportCardData> scholasticData) {
    return scholasticData.stream()
        .filter(data -> assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug()))
        .findFirst()
        .map(
            data -> {
              if (data.getIsAttended() == null) {
                return null;
              } else if (Boolean.FALSE.equals(Boolean.valueOf(data.getIsAttended()))) {
                return Objects.isNull(data.getRemarks())
                    ? "AB"
                    : data.getRemarks().substring(0, 2).toUpperCase().toString();
              }
              double roundedMarks = Math.ceil(data.getMarks() * 10) / 10.0;
              return String.valueOf(roundedMarks);
            })
        .orElse(null);
  }

  private String decimalFormat(Double value) {
    return String.format("%.2f", value);
  }

  private Double sumMarks(Double... marks) {
    return Arrays.stream(marks).filter(Objects::nonNull).mapToDouble(Double::doubleValue).sum();
  }

  private String calculateGrade(Double marks, Double totalMarks) {
    return marks == null || Objects.isNull(totalMarks)
        ? null
        : pointScaleEvaluator.evaluate("8point", BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private String calculate3PointGrade(Double marks, Double totalMarks) {
    return marks == null || Objects.isNull(totalMarks)
        ? null
        : pointScaleEvaluator.evaluate("3point", BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private Double getMarks(String assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    double average =
        subjectData.stream()
            .filter(data -> assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug()))
            .map(LowerGradeReportCardData::getMarks)
            .mapToDouble(Double::doubleValue)
            .average()
            .orElse(0.0);

    return Double.parseDouble(String.format("%.1f", average));
  }

  private FirstTerm6To8ReportDto.PercentageGrade buildPercentageGrade(
      List<FirstTerm6To8ReportDto.firstTableMarks> firstTableMarks) {

    var overallTerm1Total =
        firstTableMarks.stream().mapToDouble(marks -> Double.parseDouble(marks.term1total())).sum();
    var term1Total = overallTerm1Total / firstTableMarks.size();
    var term1TotalDecimal = decimalFormat(term1Total);
    var term1TotalGrade = calculateGrade(Double.parseDouble(term1TotalDecimal), 100d);
    var overallMarksTotal =
        firstTableMarks.stream()
            .mapToDouble(marks -> Double.parseDouble(marks.overallMarks()))
            .sum();
    var overallMarks = overallMarksTotal / firstTableMarks.size();
    var overallMarksDecimal = decimalFormat(overallMarks);
    var overallGrade = calculateGrade(Double.parseDouble(overallMarksDecimal), 100d);

    return FirstTerm6To8ReportDto.PercentageGrade.builder()
        .term1total(term1TotalDecimal)
        .term1Grade(term1TotalGrade)
        .overallMarks(overallMarksDecimal)
        .overallGrade(overallGrade)
        .build();
  }

  private FirstTerm6To8ReportDto.SecondTable buildSecondTable(
      List<LowerGradeReportCardData> optionalData) {
    if (!optionalData.isEmpty()) {
      return FirstTerm6To8ReportDto.SecondTable.builder()
          .secondTableMarks(buildSecondTableMarks(optionalData))
          .build();
    }
    return FirstTerm6To8ReportDto.SecondTable.builder()
        .secondTableMarks(buildSecondTableOptionalMarks())
        .build();
  }

  private List<FirstTerm6To8ReportDto.SecondTableMarks> buildSecondTableOptionalMarks() {
    return Collections.singletonList(
        FirstTerm6To8ReportDto.SecondTableMarks.builder()
            .subjectName("")
            .term1Marks("")
            .term1Grade("")
            .overallMarks("")
            .overallGrade("")
            .build());
  }

  private List<FirstTerm6To8ReportDto.SecondTableMarks> buildSecondTableMarks(
      List<LowerGradeReportCardData> optionalData) {

    List<FirstTerm6To8ReportDto.SecondTableMarks> buildSecondTable = new ArrayList<>();
    var optionalDataMap =
        optionalData.stream()
            .collect(Collectors.groupingBy(LowerGradeReportCardData::getSubjectName));
    optionalDataMap.forEach(
        (subject, optional) -> {
          var pa1 = getMarks("pa1", optional);
          var nb1 = getMarks("nb1", optional);
          var se1 = getMarks("se1", optional);
          var hye = getMarks("hye", optional);

          nb1 = Double.valueOf(decimalFormat(nb1));
          se1 = Double.valueOf(decimalFormat(se1));
          hye = Double.valueOf(decimalFormat(hye));
          var term1Total = sumMarks(pa1, nb1, se1, hye);
          var termMaxMarks = 100d;
          long term1TotalMarks = Math.round(term1Total);
          String grade = calculateGrade((double) term1TotalMarks, termMaxMarks);
          var overallMarksDouble = decimalFormat((double) term1TotalMarks);

          buildSecondTable.add(
              FirstTerm6To8ReportDto.SecondTableMarks.builder()
                  .subjectName(subject)
                  .term1Marks(String.valueOf(term1TotalMarks))
                  .term1Grade(grade)
                  .overallMarks(overallMarksDouble)
                  .overallGrade(grade)
                  .build());
        });
    return buildSecondTable;
  }

  private FirstTerm6To8ReportDto.ThirdTable buildThirdTable(
      List<LowerGradeReportCardData> coScholasticMandatoryData,
      List<LowerGradeReportCardData> coScholasticOptionalData) {
    if (!coScholasticMandatoryData.isEmpty() || !coScholasticOptionalData.isEmpty()) {
      return FirstTerm6To8ReportDto.ThirdTable.builder()
          .title("PART II : Co Scholastic Areas[on a 3-point (A-C) grading scale]")
          .thirdTableMarks(
              buildThirdTableMarks(coScholasticMandatoryData, coScholasticOptionalData))
          .build();
    }
    return FirstTerm6To8ReportDto.ThirdTable.builder()
        .title("")
        .thirdTableMarks(buildThirdEmptyTable())
        .build();
  }

  private List<FirstTerm6To8ReportDto.ThirdTableMarks> buildThirdEmptyTable() {
    return Collections.singletonList(
        FirstTerm6To8ReportDto.ThirdTableMarks.builder().subjectName("").term1Grade("").build());
  }

  private List<FirstTerm6To8ReportDto.ThirdTableMarks> buildThirdTableMarks(
      List<LowerGradeReportCardData> coScholasticMandatoryData,
      List<LowerGradeReportCardData> coScholasticOptionalData) {
    List<FirstTerm6To8ReportDto.ThirdTableMarks> buildThirdTable = new ArrayList<>();
    List<LowerGradeReportCardData> combinedData = new ArrayList<>();
    combinedData.addAll(coScholasticMandatoryData);
    combinedData.addAll(coScholasticOptionalData);
    var coScholasticDataMap =
        combinedData.stream()
            .collect(Collectors.groupingBy(LowerGradeReportCardData::getSubjectName));
    coScholasticDataMap.forEach(
        (subject, optional) -> {
          var pa1 = getMarks("pa1", optional);
          var nb1 = getMarks("nb1", optional);
          var se1 = getMarks("se1", optional);
          var hye = getMarks("hye", optional);

          nb1 = (nb1 / 100) * 100;
          se1 = (se1 / 100) * 100;
          hye = (hye / 100) * 100;
          nb1 = Double.valueOf(decimalFormat(nb1));
          se1 = Double.valueOf(decimalFormat(se1));
          hye = Double.valueOf(decimalFormat(hye));
          var term1Total = sumMarks(pa1, nb1, se1, hye);
          var termMaxMarks = 100d;
          long term1TotalMarks = Math.round(term1Total);
          String grade = calculate3PointGrade((double) term1TotalMarks, termMaxMarks);
          buildThirdTable.add(
              FirstTerm6To8ReportDto.ThirdTableMarks.builder()
                  .subjectName(subject)
                  .term1Grade(grade)
                  .build());
        });
    return buildThirdTable;
  }

  private FirstTerm6To8ReportDto.GradeTable buildGradeTable() {
    return FirstTerm6To8ReportDto.GradeTable.builder().title("8point").build();
  }

  private FirstTerm6To8ReportDto.Attendance buildAttendence(long studentId) {
    var termAssessment = termAssessmentRepository.findBySlug("hye");
    if (termAssessment.isEmpty()) {
      return FirstTerm6To8ReportDto.Attendance.builder().build();
    }
    var studentAttendance =
        offlineTestScheduleStudentAttendanceRepository.getOfflineTestStudentAttendance(
            studentId, termAssessment.get().getId());
    if (studentAttendance.isEmpty()) {
      return FirstTerm6To8ReportDto.Attendance.builder().build();
    }
    var testDefinition = studentAttendance.get().getOfflineTestDefinition();
    if (testDefinition == null) {
      throw new IllegalArgumentException("Test definition not found for studentId: " + studentId);
    }
    String totalAttendanceDays = testDefinition.getTotalAttendanceDays();
    Long daysPresent = studentAttendance.get().getPresentDays();
    if (totalAttendanceDays == null || daysPresent == null) {
      return FirstTerm6To8ReportDto.Attendance.builder().build();
    }
    Double attendancePercentage = null;
    double totalDays = Double.parseDouble(totalAttendanceDays);
    if (totalDays > 0.0) {
      attendancePercentage = (double) Math.round((daysPresent.doubleValue() / totalDays) * 100);
    }
    return FirstTerm6To8ReportDto.Attendance.builder()
        .workingDays(Long.valueOf(totalAttendanceDays))
        .daysPresent(daysPresent)
        .attendancePer(attendancePercentage)
        .remarks(studentAttendance.get().getRemarks())
        .build();
  }
}
