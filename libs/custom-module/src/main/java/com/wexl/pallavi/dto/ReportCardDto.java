package com.wexl.pallavi.dto;

import java.util.List;
import lombok.Builder;

public record ReportCardDto() {

  @Builder
  public record Response(Header header, Body body) {}

  @Builder
  public record Header(
      String schoolName,
      String academicYear,
      String admissionNumber,
      String address,
      String isoData,
      Long studentId) {}

  @Builder
  public record Body(
      String name,
      String rollNumber,
      String className,
      String mothersName,
      String fathersName,
      String teacherName,
      String dateOfBirth,
      FirstTable firstTable,
      SecondTable secondTable,
      ThirdTableMarks thirdTable,
      Attendance attendance,
      GradeTable gradeTable) {}

  @Builder
  public record GradeTable(String title) {}

  @Builder
  public record Attendance(
      Long workingDays, Long daysPresent, Double attendancePercentage, String remarks) {}

  @Builder
  public record FirstTable(
      String title,
      String column1,
      String column2,
      String column3,
      String column4,
      String column5,
      List<Marks> marks,
      PercentageGrade percentageGrade,
      Totals totals) {}

  @Builder
  public record SecondTable(List<ThirdTableMarks> marks) {}

  @Builder
  public record Marks(
      Long sno,
      String subject,
      String pt1,
      String ma1,
      String cwHw1,
      String se1,
      String hye,
      Long term1total,
      Double totalMarks,
      String term1Grade,
      Long seqNo) {}

  @Builder
  public record Totals(Long total, String grade, String overallPercentage) {}

  @Builder
  public record ThirdTableMarks(
      String title,
      List<ThirdTableMark> marks,
      String subjectName,
      String term1Grade,
      Long term1Marks) {}

  @Builder
  public record ThirdTableMark(String subjectName, String term1Grade) {}

  @Builder
  public record PercentageGrade(Double term1total, String term1Grade) {}

  @Builder
  public record TableMarks(
      List<Marks> firstTableMarks,
      List<ThirdTableMarks> secondTableMarks,
      List<ThirdTableMarks> thirdTableMarks) {}
}
