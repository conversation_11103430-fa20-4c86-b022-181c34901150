package com.wexl.reportcardhistory.model;

import com.wexl.retail.offlinetest.dto.LowerGradeReportDto;
import java.util.List;
import lombok.Builder;

@Builder
public class HistoricalStudentDataReportDto {
  @Builder
  public record Model(LowerGradeReportDto.Header header, LowerGradeReportDto.Body body) {}

  @Builder
  public record Header(
      String testName,
      String studentName,
      String fatherName,
      String dob,
      String admissionNo,
      String gradeName,
      String academicYear,
      Long studentId) {}

  @Builder
  public record Body(Long total, Long average, List<FirstTable> firstTable) {}

  @Builder
  public record FirstTable(String subject, Long marks, Long totalMarks) {}
}
