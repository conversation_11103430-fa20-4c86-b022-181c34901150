package com.wexl.reportcardhistory.controller;

import com.wexl.reportcardhistory.model.StudentHistoryReportDto;
import com.wexl.reportcardhistory.model.StudentHistoryResponseDto;
import com.wexl.reportcardhistory.service.HistoricalExamMigrationService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/orgs/{orgId}/historical-exam-data")
@RequiredArgsConstructor
public class HistoricalExamDataController {
  private final HistoricalExamMigrationService historicalExamMigrationService;

  @PostMapping()
  public void migrateHistoricalExams(
      @PathVariable String orgId, @RequestBody StudentHistoryReportDto.MigrationDto migrationDto) {
    historicalExamMigrationService.migrateHistoricalExams(orgId, migrationDto.academicYear());
    log.info(
        "Migrating historical exams for org: {} and academic year: {}",
        orgId,
        migrationDto.academicYear());
  }

  @GetMapping()
  public List<StudentHistoryResponseDto.StudentHistoryResponse> getHistoricalExamStudents(
      @RequestParam String academicYear, @RequestParam String gradeSlug) {
    return historicalExamMigrationService.getHistoricalExamStudents(academicYear, gradeSlug);
  }
}
