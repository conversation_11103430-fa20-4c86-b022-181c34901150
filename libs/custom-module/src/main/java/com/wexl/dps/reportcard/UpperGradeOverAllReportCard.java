package com.wexl.dps.reportcard;

import static java.lang.String.format;

import com.wexl.dps.dto.UpperGradeReportDto;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.dps.managereportcard.repository.ReportCardConfigRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.OfflineTestScheduleStudentAttendance;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.*;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UpperGradeOverAllReportCard extends BaseReportCardDefinition {
  private final ReportCardService reportCardService;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final ReportCardConfigRepository reportCardConfigRepository;
  private final OfflineTestScheduleService offlineTestScheduleService;
  private final LowerGradeFirstTermReportCard lowerGradeFirstTermReportCard;
  private final StudentAttributeService studentAttributeService;
  private static final String FALSE = "false";
  private final OfflineTestDefinitionRepository offlineTestDefinitionRepository;
  private final StudentRepository studentRepository;
  private static final String BOWENAPALLY_ORG = "pal233196";
  private static final List<String> ABSENT_REASON = List.of("AB", "PA", "PL", "ML");

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, org.getSlug(), request.offlineTestDefinitionId());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("upper-grade-overall-report.xml");
  }

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, "admission_no");
  }

  public UpperGradeReportDto.Body buildBody(
      User user, String orgSlug, Long offlineTestDefinitionId) {
    var student = user.getStudentInfo();

    Optional<StudentAttributeValueModel> mother =
        reportCardService.getStudentAttributeValue(student, "mother_name");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var tableMarks = buildTableMarks(student, orgSlug);
    var testDefinition =
        offlineTestScheduleService.validateOfflineTestDefinition(
            tableMarks.firstTableMarks().getFirst().otdId());
    return UpperGradeReportDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(studentAttributeService.getDateOfBirthFormat(dateOfBirth))
        .className(student.getSection().getName())
        .rollNumber(student.getClassRollNumber())
        .orgSlug(orgSlug)
        .boardSlug(student.getSection().getBoardSlug())
        .gradeSlug(student.getSection().getGradeSlug())
        .mothersName(mother.map(StudentAttributeValueModel::getValue).orElse(null))
        .fathersName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .firstTable(
            buildFirstTable(tableMarks.firstTableMarks(), tableMarks.externalMarks(), student))
        .secondTable(buildSecondTable(tableMarks.secondTableMarks()))
        .graphTableMarks(buildGraphTable(tableMarks.graphTableMarks()))
        .attendance(buildAttendance(student.getId(), offlineTestDefinitionId))
        .gradeTable(UpperGradeReportDto.GradeTable.builder().title("Grade Scale").build())
        .gradingScale(
            testDefinition.getGradeScaleSlug() != null
                ? testDefinition.getGradeScaleSlug()
                : "8point")
        .build();
  }

  private UpperGradeReportDto.FirstTable buildGraphTable(List<UpperGradeReportDto.Marks> marks) {
    return UpperGradeReportDto.FirstTable.builder().marks(marks).build();
  }

  private UpperGradeReportDto.TableMarks buildTableMarks(Student student, String orgSlug) {

    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            Arrays.asList("t1", "t2"), student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }

    var scholasticDataList =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var optionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();

    var coScholasticData =
        data.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.MANDATORY.name()))
            .toList();

    var sortedData =
        sortTable(
            buildTableMarks(scholasticDataList, orgSlug),
            buildTableMarks(optionalData, orgSlug),
            buildTableMarks(coScholasticData, orgSlug));
    return UpperGradeReportDto.TableMarks.builder()
        .firstTableMarks(sortedData.firstTableMarks())
        .externalMarks(sortedData.externalMarks())
        .secondTableMarks(sortedData.secondTableMarks())
        .graphTableMarks(sortedData.graphTableMarks())
        .build();
  }

  public List<UpperGradeReportDto.Marks> buildTableMarks(
      List<LowerGradeReportCardData> reportCardData, String orgSlug) {
    List<UpperGradeReportDto.Marks> marksList = new ArrayList<>();

    var scholasticDataMap =
        reportCardData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var pt = getMarks(List.of("pt(pt1+pt2)", "pa1+cra1"), scholasticData);
          var nb1 = getMarks(List.of("nb1"), scholasticData);
          var se1 = getMarks(List.of("se1"), scholasticData);
          var hye = getMarks(List.of("hye"), scholasticData);
          var hye1 =
              orgSlug.equals(BOWENAPALLY_ORG) ? getMarks1(List.of("hye"), scholasticData) : null;

          var pt2 = getMarks(List.of("pt(pt3+pt4)", "pa2+cra2", "pt2-cra2"), scholasticData);
          var nb2 = getMarks(List.of("nb2"), scholasticData);
          var se2 = getMarks(List.of("se2"), scholasticData);
          var ye = getMarks(List.of("ye"), scholasticData);
          var ye1 =
              orgSlug.equals(BOWENAPALLY_ORG) ? getMarks1(List.of("ye"), scholasticData) : null;
          var hye1Ye1 = sumMarks(hye1, ye1);
          var term1Total = sumMarks(pt, nb1, se1, hye);
          var term2Total = sumMarks(pt2, nb2, se2, ye);
          var termMaxMarks = 100d;
          var offlineTestDefinition =
              offlineTestScheduleService.validateOfflineTestDefinition(
                  scholasticData.getFirst().getOtdId());
          String grade =
              calculateGrade(term1Total, termMaxMarks, offlineTestDefinition.getGradeScaleSlug());
          double term1TotalDouble = Double.parseDouble(String.format("%.2f", term1Total));
          String grade2 =
              calculateGrade(term2Total, termMaxMarks, offlineTestDefinition.getGradeScaleSlug());
          double term2TotalDouble = Double.parseDouble(String.format("%.2f", term2Total));

          double totalTerm1AndTerm2 = term1TotalDouble + term2TotalDouble;
          double totalTerm1AndTerm2Format =
              Double.parseDouble(String.format("%.2f", totalTerm1AndTerm2));
          double averageTerm1AndTerm2 = totalTerm1AndTerm2 / 2;
          double averageTerm1AndTerm2Format =
              Double.parseDouble(String.format("%.2f", averageTerm1AndTerm2));
          String totalGrade =
              calculateGrade(
                  averageTerm1AndTerm2, termMaxMarks, offlineTestDefinition.getGradeScaleSlug());

          marksList.add(
              UpperGradeReportDto.Marks.builder()
                  .pt(buildMarks(scholasticData, pt))
                  .nb1(buildMarks(scholasticData, se1))
                  .se1(buildMarks(scholasticData, nb1))
                  .hye(buildMarks(scholasticData, hye))
                  .hye1(hye1)
                  .hyeGrade(calculateHyeAnYeGrade(hye1, 50.0))
                  .pa2(buildMarks(scholasticData, pt2))
                  .nb2(buildMarks(scholasticData, nb2))
                  .se2(buildMarks(scholasticData, se2))
                  .ye(buildMarks(scholasticData, ye))
                  .ye1(ye1)
                  .yeGrade(calculateHyeAnYeGrade(ye1, 50.0))
                  .hyeGradeAndYeMarks(hye1Ye1 == null ? null : formatMarks(hye1Ye1))
                  .hyeGradeAndYeGrade(
                      calculateHyeAnYeGrade(hye1Ye1 == null ? null : hye1Ye1.toString(), 100.0))
                  .grade1(grade)
                  .grade2(grade2)
                  .term1TotalMarks(getTotalTermPercentage(term1TotalDouble))
                  .term2TotalMarks(getTotalTermPercentage(term2TotalDouble))
                  .marksObtained1(term1TotalDouble)
                  .marksObtained2(term2TotalDouble)
                  .subject(subject)
                  .t1t2Total(totalTerm1AndTerm2Format)
                  .overAllMarks(averageTerm1AndTerm2Format)
                  .overAllGrade(totalGrade)
                  .seqNo(scholasticData.get(0).getSeqNo())
                  .otdId(scholasticData.getFirst().getOtdId())
                  .considerPercentage(scholasticData.getFirst().getConsiderPercentage())
                  .build());
        });
    return marksList;
  }

  public double formatMarks(double percentage) {
    DecimalFormat decimalFormat = new DecimalFormat("0.00");
    return Double.parseDouble(decimalFormat.format(percentage));
  }

  public String getMarks1(List<String> assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    if (subjectData == null
        || subjectData.isEmpty()
        || assessmentSlug == null
        || assessmentSlug.isEmpty()) {
      return null;
    }
    var filteredData =
        subjectData.stream().filter(d -> assessmentSlug.contains(d.getAssessmentSlug())).toList();

    if (filteredData.isEmpty()) {
      return null;
    }

    var attendedData =
        filteredData.stream()
            .filter(x -> Objects.nonNull(x.getIsAttended()) && x.getIsAttended().equals("true"))
            .toList();

    var nonAttendedData =
        filteredData.stream()
            .filter(x -> x.getIsAttended() == null || x.getIsAttended().equals("false"))
            .toList();

    if (!nonAttendedData.isEmpty() && attendedData.isEmpty()) {
      return filteredData.getFirst().getRemarks() == null
          ? "AB"
          : filteredData.getFirst().getRemarks().substring(0, 2).toUpperCase();
    }

    double average =
        attendedData.stream()
            .filter(d -> d.getMarks() != null && d.getTotalMarks() != null && d.getTotalMarks() > 0)
            .mapToDouble(d -> (d.getMarks() / d.getTotalMarks()) * 50)
            .average()
            .orElse(0.0);

    return String.format("%.2f", average);
  }

  private double getTotalTermPercentage(Double marks) {
    if (marks == null) {
      return 0;
    }
    double percentage = (marks / 100) * 100;
    return Math.round(percentage * 100.0) / 100.0; // Round to 2 decimal places
  }

  private String buildMarks(List<LowerGradeReportCardData> scholasticData, String value) {
    if (value == null) {
      return null;
    }
    return value.equals("0.0")
        ? (Boolean.TRUE.equals(Boolean.valueOf(scholasticData.getFirst().getIsAttended()))
            ? null
            : "AB")
        : value;
  }

  private String calculateGrade(Double marks, Double totalMarks, String gradeScaleSlug) {
    return marks == null || Objects.isNull(totalMarks)
        ? null
        : pointScaleEvaluator.evaluate(
            gradeScaleSlug == null ? "8point" : gradeScaleSlug,
            BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private String calculateHyeAnYeGrade(String marks, Double totalMarks) {
    return marks == null
        ? null
        : ABSENT_REASON.contains(marks)
            ? marks
            : pointScaleEvaluator.evaluate(
                "8point", BigDecimal.valueOf(((Double.parseDouble(marks) / totalMarks) * 100)));
  }

  public String getMarks(List<String> assessmentSlug, List<LowerGradeReportCardData> subjectData) {
    Double average;
    var data =
        subjectData.stream().filter(d -> assessmentSlug.contains(d.getAssessmentSlug())).toList();
    if (data.isEmpty()) {
      return null;
    }

    var isAttended =
        data.stream()
            .filter(
                d ->
                    d.getIsAttended() == null
                        || Boolean.FALSE.equals(Boolean.valueOf(d.getIsAttended())))
            .toList();
    if (!isAttended.isEmpty()) {
      var attendedData = data.stream().filter(d -> d.getIsAttended() != null).toList();
      if (attendedData.isEmpty()) {
        return null;
      }
      var data1 =
          data.stream()
              .filter(d -> Boolean.TRUE.equals(Boolean.valueOf(d.getIsAttended())))
              .toList();
      if (data1.isEmpty()) {
        return data.getFirst().getRemarks() == null
            ? "AB"
            : data.getFirst().getRemarks().substring(0, 2).toUpperCase();
      }
      average =
          data1.stream()
              .map(LowerGradeReportCardData::getMarks)
              .filter(Objects::nonNull)
              .mapToDouble(Double::doubleValue)
              .average()
              .orElse(0.0);

      return String.format("%.2f", average);
    }

    var data1 =
        data.stream().filter(d -> Boolean.TRUE.equals(Boolean.valueOf(d.getIsAttended()))).toList();

    if (data1.isEmpty()) {
      return data.getFirst().getRemarks() == null
          ? "AB"
          : data.getFirst().getRemarks().substring(0, 2).toUpperCase();
    }

    average =
        data1.stream()
            .map(LowerGradeReportCardData::getMarks)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .average()
            .orElse(0.0);

    return String.format("%.2f", average);
  }

  private Double sumMarks(String... marks) {
    return Arrays.stream(marks)
        .filter(mark -> mark != null && !mark.isEmpty() && mark.matches("\\d+(\\.\\d+)?"))
        .mapToDouble(Double::parseDouble)
        .sum();
  }

  private UpperGradeReportDto.FirstTable buildFirstTable(
      List<UpperGradeReportDto.Marks> firstTableMarks,
      List<UpperGradeReportDto.Marks> externalMarks,
      Student student) {
    var section = student.getSection();

    var reportCardConfigs =
        reportCardConfigRepository.findAllByOrgSlugAndBoardSlugAndGradeSlug(
            section.getOrganization(), section.getBoardSlug(), section.getGradeSlug());

    var configDetails =
        reportCardConfigs.stream()
            .flatMap(config -> config.getReportCardConfigDetails().stream())
            .sorted(Comparator.comparing(ReportCardConfigDetail::getSeqNo))
            .toList();

    return UpperGradeReportDto.FirstTable.builder()
        .title("PART-I: SCHOLASTIC AREAS")
        .column1(columnFormat(constructColumn(configDetails.getFirst())))
        .column2(constructColumn(configDetails.get(1)))
        .column3(constructColumn(configDetails.get(2)))
        .column4(constructColumn(configDetails.get(3)))
        .column5(columnFormat(constructColumn(configDetails.get(4))))
        .column6(constructColumn(configDetails.get(5)))
        .column7(constructColumn(configDetails.get(6)))
        .column8(constructColumn(configDetails.get(7)))
        .marks(firstTableMarks)
        .external(externalMarks)
        .totals(buildTotals(firstTableMarks))
        .build();
  }

  private String columnFormat(String configData) {
    return configData.replace("+", "+ ");
  }

  private String constructColumn(ReportCardConfigDetail configDetail) {
    return Objects.isNull(configDetail)
        ? null
        : format(
            "%s (%s)", configDetail.getTermAssessment().getName(), configDetail.getWeightage());
  }

  private UpperGradeReportDto.Totals buildTotals(List<UpperGradeReportDto.Marks> firstTableMarks) {
    var totalMarksScored =
        firstTableMarks.stream()
            .filter(marks -> marks.considerPercentage())
            .map(UpperGradeReportDto.Marks::marksObtained1)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .average();
    var term2TotalMarksScored =
        firstTableMarks.stream()
            .filter(marks -> marks.considerPercentage())
            .map(UpperGradeReportDto.Marks::marksObtained2)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .average();
    var totalMarks =
        Double.parseDouble(
            String.format(
                "%.1f",
                firstTableMarks.stream()
                    .map(UpperGradeReportDto.Marks::marksObtained1)
                    .filter(Objects::nonNull)
                    .mapToDouble(Double::doubleValue)
                    .sum()));

    var totalMarksScoredFormat = String.format("%.2f", totalMarksScored.orElse(0.0));

    var term2TotalMarksScoredFormat = String.format("%.2f", term2TotalMarksScored.orElse(0.0));

    String grade =
        totalMarksScored.isEmpty()
            ? "N/A"
            : pointScaleEvaluator.evaluate(
                "8point", BigDecimal.valueOf(Double.parseDouble(totalMarksScoredFormat)));

    String term2Grade =
        term2TotalMarksScored.isEmpty()
            ? "N/A"
            : pointScaleEvaluator.evaluate(
                "8point", BigDecimal.valueOf(Double.parseDouble(term2TotalMarksScoredFormat)));

    String overallPercentageGrade = "N/A".equals(grade) ? "0.0" : grade;
    String term2OverallPercentageGrade = "N/A".equals(term2Grade) ? "0.0" : term2Grade;

    var sumOfTerm1AndTerm2 =
        Double.parseDouble(totalMarksScoredFormat)
            + Double.parseDouble(term2TotalMarksScoredFormat);
    String sumOfTerm1AndTerm2Format = String.format("%.2f", sumOfTerm1AndTerm2);
    var averageTerm1AndTerm2 = sumOfTerm1AndTerm2 / 2;
    BigDecimal bd = new BigDecimal(averageTerm1AndTerm2).setScale(2, RoundingMode.FLOOR);
    String formattedAverage = bd.toString();

    String term1AndTerm2Grade =
        totalMarksScored.isEmpty()
            ? "N/A"
            : pointScaleEvaluator.evaluate("8point", BigDecimal.valueOf(averageTerm1AndTerm2));
    return UpperGradeReportDto.Totals.builder()
        .marksTotal(totalMarks)
        .overallPercentage(totalMarksScoredFormat)
        .grade(overallPercentageGrade)
        .term2OverallPercentage(term2TotalMarksScoredFormat)
        .term2Grade(term2OverallPercentageGrade)
        .totalT1AndT2(sumOfTerm1AndTerm2Format)
        .averageT1AndT2(formattedAverage)
        .totalGrade(term1AndTerm2Grade)
        .build();
  }

  private UpperGradeReportDto.SecondTable buildSecondTable(
      List<UpperGradeReportDto.SecondTableMarks> marks) {
    return UpperGradeReportDto.SecondTable.builder()
        .title("PART-II: Co-Scholastic Areas[on a 3-point(A to C)grading scale]")
        .marks(marks)
        .build();
  }

  public UpperGradeReportDto.Attendance buildAttendance(
      Long studentId, Long offlineTestDefinitionId) {
    List<String> slugs = Arrays.asList("hye", "ye");
    var termAssessments = termAssessmentRepository.findAllBySlugIn(slugs);

    if (termAssessments.isEmpty()) {
      return UpperGradeReportDto.Attendance.builder().build();
    }
    var termAssessmentHye =
        termAssessments.stream()
            .filter(termAssessment -> "hye".equalsIgnoreCase(termAssessment.getSlug()))
            .findFirst()
            .orElseThrow();
    var termAssessmentYe =
        termAssessments.stream()
            .filter(termAssessment -> "ye".equalsIgnoreCase(termAssessment.getSlug()))
            .findFirst()
            .orElseThrow();
    var studentAttendancesTerm1 =
        offlineTestScheduleStudentAttendanceRepository.getOfflineTestStudentAttendance(
            studentId, termAssessmentHye.getId());
    var studentAttendancesTerm2 =
        offlineTestScheduleStudentAttendanceRepository.getOfflineTestStudentAttendance(
            studentId, termAssessmentYe.getId());

    if (studentAttendancesTerm1.isEmpty() || studentAttendancesTerm2.isEmpty()) {
      return UpperGradeReportDto.Attendance.builder().build();
    }

    var student = studentRepository.findById(studentId).orElseThrow();
    var orgSlug = student.getUserInfo().getOrganization();
    String assessmentSlug = "ye";
    var offlineTestDefinition =
        Objects.nonNull(offlineTestDefinitionId)
            ? offlineTestDefinitionRepository.findById(offlineTestDefinitionId)
            : termAssessmentRepository
                .findBySlug(assessmentSlug)
                .map(
                    term ->
                        offlineTestDefinitionRepository
                            .findByAssessmentIdAndGradeSlugAndOrgSlugAndBoardSlugAndSectionUuid(
                                term.getId(),
                                student.getSection().getGradeSlug(),
                                orgSlug,
                                student.getSection().getBoardSlug(),
                                student.getSection().getUuid().toString()))
                .orElse(null);

    var testScheduleStudentAttendance =
        offlineTestDefinition.get().getOfflineTestScheduleStudentAttendanceAndRemarks();

    Long totalDaysPresent =
        testScheduleStudentAttendance.stream()
            .filter(ts -> ts.getStudentId().equals(studentId))
            .map(OfflineTestScheduleStudentAttendance::getPresentDays)
            .filter(Objects::nonNull)
            .findFirst()
            .orElse(0L);

    var totalWorkingDays =
        offlineTestDefinition.get().getTotalAttendanceDays() == null
            ? null
            : Long.parseLong(offlineTestDefinition.get().getTotalAttendanceDays());

    double percentageOfAttendance =
        totalWorkingDays != null ? ((double) totalDaysPresent / totalWorkingDays) * 100 : 0;
    return UpperGradeReportDto.Attendance.builder()
        .workingDays(totalWorkingDays)
        .daysPresent(totalDaysPresent)
        .attendancePercentage(Double.parseDouble(String.format("%.2f", percentageOfAttendance)))
        .remarks(studentAttendancesTerm2.get().getRemarks())
        .build();
  }

  private UpperGradeReportDto.TableMarks sortTable(
      List<UpperGradeReportDto.Marks> firstTableMarks,
      List<UpperGradeReportDto.Marks> externalMarks,
      List<UpperGradeReportDto.Marks> secondTableMarks) {
    List<UpperGradeReportDto.Marks> firstTable = new ArrayList<>();
    List<UpperGradeReportDto.SecondTableMarks> secondTable = new ArrayList<>();
    List<UpperGradeReportDto.Marks> externalTable = new ArrayList<>();
    List<UpperGradeReportDto.Marks> graphTable = new ArrayList<>();
    var sortedFirstTable =
        firstTableMarks.stream()
            .sorted(Comparator.comparingLong(UpperGradeReportDto.Marks::seqNo))
            .toList();
    s: // learn.academyteacher.com
    for (int i = 0; i < sortedFirstTable.size(); i++) {
      UpperGradeReportDto.Marks mark = sortedFirstTable.get(i);
      firstTable.add(
          UpperGradeReportDto.Marks.builder()
              .sno(i + 1L)
              .pt(mark.pt())
              .nb1(mark.nb1())
              .se1(mark.se1())
              .hye(mark.hye())
              .pa2(mark.pa2())
              .nb2(mark.nb2())
              .se2(mark.se2())
              .ye(mark.ye())
              .term1TotalMarks(mark.term1TotalMarks())
              .term2TotalMarks(mark.term2TotalMarks())
              .marksObtained1(mark.marksObtained1())
              .marksObtained2(mark.marksObtained2())
              .grade1(mark.grade1())
              .grade2(mark.grade2())
              .subject(mark.subject())
              .overAllGrade(mark.overAllGrade())
              .overAllScored(mark.overAllScored())
              .t1t2Total(mark.t1t2Total())
              .overAllMarks(mark.overAllMarks())
              .overAllGrade(mark.overAllGrade())
              .otdId(mark.otdId())
              .considerPercentage(mark.considerPercentage())
              .build());
    }

    for (int i = 0; i < sortedFirstTable.size(); i++) {
      UpperGradeReportDto.Marks mark = sortedFirstTable.get(i);

      double term1Rounded =
          new BigDecimal(mark.term1TotalMarks()).setScale(0, RoundingMode.HALF_UP).intValue();

      double term2Rounded =
          new BigDecimal(mark.term2TotalMarks()).setScale(0, RoundingMode.HALF_UP).intValue();

      graphTable.add(
          UpperGradeReportDto.Marks.builder()
              .term1TotalMarks(term1Rounded)
              .term2TotalMarks(term2Rounded)
              .subject(mark.subject())
              .build());
    }

    var sortedExternalTable =
        externalMarks.stream()
            .sorted(Comparator.comparingLong(UpperGradeReportDto.Marks::seqNo))
            .toList();
    for (int i = 0; i < sortedExternalTable.size(); i++) {
      UpperGradeReportDto.Marks mark = sortedExternalTable.get(i);
      externalTable.add(
          UpperGradeReportDto.Marks.builder()
              .sno(sortedFirstTable.size() + i + 1L)
              .pt(mark.pt())
              .nb1(mark.nb1())
              .se1(mark.se1())
              .hye(mark.hye())
              .pa2(mark.pa2())
              .nb2(mark.nb2())
              .se2(mark.se2())
              .ye(mark.ye())
              .yeGrade(mark.yeGrade())
              .hyeGrade(mark.hyeGrade())
              .hyeGradeAndYeGrade(mark.hyeGradeAndYeGrade())
              .hyeGradeAndYeMarks(mark.hyeGradeAndYeMarks())
              .hye1(mark.hye1())
              .ye1(mark.ye1())
              .term1TotalMarks(mark.term1TotalMarks())
              .term2TotalMarks(mark.term2TotalMarks())
              .marksObtained1(mark.marksObtained1())
              .marksObtained2(mark.marksObtained2())
              .grade1(mark.grade1())
              .grade2(mark.grade2())
              .subject(mark.subject())
              .overAllGrade(mark.overAllGrade())
              .overAllScored(mark.overAllScored())
              .t1t2Total(mark.t1t2Total())
              .overAllMarks(mark.overAllMarks())
              .overAllGrade(mark.overAllGrade())
              .build());
    }
    var sortedSecondTable =
        secondTableMarks.stream()
            .sorted(Comparator.comparingLong(UpperGradeReportDto.Marks::seqNo))
            .toList();
    for (int i = 0; i < sortedSecondTable.size(); i++) {
      UpperGradeReportDto.Marks mark = sortedSecondTable.get(i);
      var hye = mark.hye();
      var ye = mark.ye();
      String grade = null;
      String term2Grade = null;
      if (hye != null && !hye.equals("AB") && !hye.equals("PA") && !hye.equals("ML")) {
        grade = offlineTestScheduleService.getGrade(BigDecimal.valueOf(Double.parseDouble(hye)));
      }
      if (ye != null && !ye.equals("AB") && !ye.equals("PA") && !ye.equals("ML")) {
        term2Grade =
            offlineTestScheduleService.getGrade(BigDecimal.valueOf(Double.parseDouble(ye)));
      }

      secondTable.add(
          UpperGradeReportDto.SecondTableMarks.builder()
              .term1Grade(Objects.isNull(grade) ? hye : grade)
              .term2Grade(Objects.isNull(term2Grade) ? ye : term2Grade)
              .subjectName(mark.subject())
              .build());
    }
    return UpperGradeReportDto.TableMarks.builder()
        .firstTableMarks(firstTable)
        .externalMarks(externalTable)
        .secondTableMarks(secondTable)
        .graphTableMarks(graphTable)
        .build();
  }

  private static Long getOverAllAttendance(
      Optional<OfflineTestScheduleStudentAttendance> studentAttendancesTerm1,
      Optional<OfflineTestScheduleStudentAttendance> studentAttendancesTerm2) {
    var testDefinitionTerm1 =
        studentAttendancesTerm1
            .map(OfflineTestScheduleStudentAttendance::getOfflineTestDefinition)
            .orElse(null);
    var testDefinitionTerm2 =
        studentAttendancesTerm2
            .map(OfflineTestScheduleStudentAttendance::getOfflineTestDefinition)
            .orElse(null);

    Long attendanceDaysTerm1 =
        testDefinitionTerm1 != null && testDefinitionTerm1.getTotalAttendanceDays() != null
            ? Long.parseLong(testDefinitionTerm1.getTotalAttendanceDays())
            : 0L;

    Long attendanceDaysTerm2 =
        testDefinitionTerm2 != null && testDefinitionTerm2.getTotalAttendanceDays() != null
            ? Long.parseLong(testDefinitionTerm2.getTotalAttendanceDays())
            : 0L;

    return attendanceDaysTerm1 + attendanceDaysTerm2;
  }

  public List<ReportCardConfigDto.GradeAndPercentage> getGradeAndPercentage(
      List<Student> students) {
    List<ReportCardConfigDto.GradeAndPercentage> responseList = new ArrayList<>();
    students.forEach(
        student -> {
          try {
            var tableMarks = buildTableMarks(student, student.getUserInfo().getOrganization());
            var firstTable = tableMarks.firstTableMarks();
            var totals = buildTotals(firstTable);
            responseList.add(
                ReportCardConfigDto.GradeAndPercentage.builder()
                    .grade(String.valueOf(totals.totalGrade()))
                    .percentage(
                        totals.averageT1AndT2().equals("NaN") ? "-" : totals.averageT1AndT2())
                    .student(student)
                    .build());
          } catch (Exception ex) {
            responseList.add(
                ReportCardConfigDto.GradeAndPercentage.builder()
                    .grade("NA")
                    .percentage("-")
                    .student(student)
                    .build());
          }
        });
    return responseList;
  }

  public List<Map<String, Integer>> getGradeCounts(
      List<Student> students, String subjectSlug, String orgSlug) {
    Map<String, Integer> gradeCounts = new LinkedHashMap<>();
    int t1TotalStudents = 0;
    int t2TotalStudents = 0;

    for (Student student : students) {
      var termAssessments =
          termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
              Arrays.asList("t1", "t2"), student.getSection().getGradeSlug());

      if (termAssessments.isEmpty()) continue;

      var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();
      var data =
          reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
              student.getId(), termAssessmentIds);

      if (Objects.isNull(data) || data.isEmpty()) continue;

      var scholasticDataList =
          data.stream()
              .filter(
                  x ->
                      SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                          && SubjectsTypeEnum.MANDATORY.name().equals(x.getType())
                          && x.getSubjectSlug().equals(subjectSlug))
              .toList();

      var marksList = buildTableMarks(scholasticDataList, orgSlug);
      if (marksList.isEmpty()) continue;

      var marks = marksList.get(0);
      gradeCounts.merge(marks.grade1() + "(T1)", 1, Integer::sum);
      gradeCounts.merge(marks.grade2() + "(T2)", 1, Integer::sum);

      t1TotalStudents++;
      t2TotalStudents++;
    }

    gradeCounts.put("T1TOTALSTUDENTS", t1TotalStudents);
    gradeCounts.put("T2TOTALSTUDENTS", t2TotalStudents);

    Map<String, Integer> sortedMap = new TreeMap<>(gradeCounts);
    return List.of(sortedMap);
  }
}
