package com.wexl.dps.assesmentobjectives.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.dps.assesmentobjectives.model.AreaRemark;
import com.wexl.retail.model.Subject;
import java.util.List;
import lombok.Builder;

public record AssessmentObjectiveDto() {
  public record AssessmentObjectiveRequest(
      @JsonProperty("term_id") long termId,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("seq_no") Long seqNo,
      @JsonProperty("student_auth_id") String studentAuthId) {}

  @Builder
  public record AssessmentObjectiveResponse(
      Long termId,
      @JsonProperty("term_name") String termName,
      Long id,
      @JsonProperty("seq_no") Long seqNo,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("board_name") String boardName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("subject_name") String subjectName) {}

  public record AssessmentObjectiveDetailRequest(
      String name, String gradeSlug, String subjectSlug) {}

  @Builder
  public record AssessmentObjectiveDetailResponse(
      Long id, String text, @JsonProperty("published_at") Long publishedAt) {}

  public record AoDetailRequest(String text) {}

  @Builder
  public record AoStudentCreationResponse(
      Long assessmentObjectiveId,
      String grade,
      String aoName,
      Long termId,
      List<AssessmentObjectiveDetailResponse> aoDetails) {}

  @Builder
  public record AssessmentObjectiveStudentRequest(
      Long studentId,
      Long aoId,
      String areaOfStrength,
      String areaOfFocus,
      String remarks,
      Long termAssessmentId,
      List<AssessmentObjectiveStudent> assessmentObjectiveStudents) {}

  @Builder
  public record AssessmentObjectiveStudent(Long AoDetailId, String color) {}

  @Builder
  public record ValidAOSubjectResponse(List<Subject> subjects) {}

  @Builder
  public record AoRemarkResponse(Long id, String remark, AreaRemark areaRemark) {}
}
