package com.wexl.dps.managereportcard.repository;

import com.wexl.dps.managereportcard.model.OfflineTestScheduleStudentDetails;
import com.wexl.retail.offlinetest.repository.OfflineTestDefinitionRepository;
import java.util.List;
import java.util.Set;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CustomOfflineTestDefinitionRepository extends OfflineTestDefinitionRepository {

  @Query(
      value =
          """
                  SELECT
                      ta."name" AS assessmentName,
                      otss.id AS otssId,
                      sm.category as  category,
                      sm."name" AS subjectName,
                      otss.student_id AS studentId,
                      otss.marks AS studentMarks,
                      ots.marks AS totalMarks,
                     ots.published_at as publishedAt
                  FROM
                      offline_test_definition otd
                  INNER JOIN
                      offline_test_schedule ots ON ots.offline_test_definition_id = otd.id
                  INNER JOIN
                      offline_test_schedule_student otss ON otss.offline_test_schedule_id = ots.id
                  INNER JOIN
                      subject_metadata sm ON sm.id = ots.subject_metadata_id
                  INNER JOIN
                      term_assessments ta ON ta.id = otd.assessment_id
                  WHERE
                      otd.org_slug = :orgSlug
                      AND otd.board_slug = :boardSlug
                      AND otd.grade_slug = :gradeSlug
                      AND otd.assessment_id = :assessmentId and ots.deleted_at is null
                      AND (cast((:categoryIds) as varChar) is null or otd.assessment_category_id in (:categoryIds))

                  """,
      nativeQuery = true)
  List<OfflineTestScheduleStudentDetails> getOfflineTestDefinitionsByAssessment(
      String orgSlug, String boardSlug, String gradeSlug, Long assessmentId, Set<Long> categoryIds);
}
