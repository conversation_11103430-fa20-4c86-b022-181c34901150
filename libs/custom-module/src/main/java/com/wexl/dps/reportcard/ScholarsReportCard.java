package com.wexl.dps.reportcard;

import com.wexl.dps.dto.ScholarsReportCardDto;
import com.wexl.dps.learningmilestones.model.LmrCategoryAttributeDefinition;
import com.wexl.dps.learningmilestones.model.LmrCategoryGrade;
import com.wexl.dps.learningmilestones.model.LmrCategoryGradeAttribute;
import com.wexl.dps.learningmilestones.model.LmrStudentDetail;
import com.wexl.dps.learningmilestones.repository.*;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.repository.SubjectsMetaDataRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class ScholarsReportCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final SubjectsMetaDataRepository subjectsMetaDataRepository;
  private final LmrCategoryGradeRepository categoryGradeRepository;
  private final CategoryGradeAttributeRepository lmrCategoryGradeAttributeRepository;
  private final LmrCategoryRepository lmrCategoryRepository;
  private final LmrCategoryAttributeDefinitionRepository lmrCategoryAttributeDefinitionRepository;
  private final LmrStudentDetailRepository lmrStudentDetailRepository;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var student = user.getStudentInfo();
    var header = buildScholarsHeader(student, org);
    var body = buildScholarsBody(student, org, request);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("scholors-1st-2nd-report-card.xml");
  }

  private ScholarsReportCardDto.Header buildScholarsHeader(Student student, Organization org) {
    Optional<StudentAttributeValueModel> admissionNo =
        getStudentAttributeValue(student, "admission_no");

    return ScholarsReportCardDto.Header.builder()
        .schoolName(org.getName())
        .logo(org.getLogo())
        .address("JAMNIWALA ROAD, BADRIPUR, PAONTA SAHIB DISTT. SIRMOUR (H.P.) - 173025")
        .build();
  }

  private ScholarsReportCardDto.Body buildScholarsBody(
      Student student, Organization org, ReportCardDto.Request request) {
    var subjectMetadata =
        subjectsMetaDataRepository.findById(request.offlineTestDefinitionId()).orElseThrow();
    List<LmrCategoryGrade> lmrCategoryGrades =
        categoryGradeRepository.getAllByGradeSlugAndSubjectMetadataIdAndTerm(
            student.getSection().getGradeSlug(), subjectMetadata.getId(), request.termId());
    List<Long> categoryGradeIds =
        lmrCategoryGrades.stream().map(LmrCategoryGrade::getLmrCategoryId).toList();
    List<LmrCategoryGradeAttribute> lmrCategoryGradeAttributes =
        lmrCategoryGradeAttributeRepository.findByLmrCategoryGradeIdIn(categoryGradeIds);
    List<ScholarsReportCardDto.Subject> subjects = new ArrayList<>();
    for (LmrCategoryGradeAttribute lmrCategoryGradeAttribute : lmrCategoryGradeAttributes) {
      List<LmrStudentDetail> lmrStudentDetail =
          lmrStudentDetailRepository
              .findAllByOrgSlugAndStudentIdInAndTermIdAndLmrCategoryAttributeIdIn(
                  org.getSlug(),
                  List.of(student.getId()),
                  request.termId(),
                  List.of(lmrCategoryGradeAttribute.getId()));
      var lmrCategoryAttributeDefinition =
          lmrCategoryAttributeDefinitionRepository.findAllByLmrCategoryAttributeId(
              lmrCategoryGradeAttribute.getId());
      subjects.add(
          buildSubjects(
              request.termId(),
              lmrCategoryGradeAttribute,
              lmrCategoryAttributeDefinition,
              lmrStudentDetail));
    }

    Optional<StudentAttributeValueModel> dateOfBirth =
        getStudentAttributeValue(student, "date_of_birth");
    Optional<StudentAttributeValueModel> bloodGroup =
        getStudentAttributeValue(student, "blood_group");
    Optional<StudentAttributeValueModel> mobileNumber =
        getStudentAttributeValue(student, "mobile_number");
    Optional<StudentAttributeValueModel> address = getStudentAttributeValue(student, "address");

    return ScholarsReportCardDto.Body.builder()
        .name(student.getUserInfo().getFirstName() + " " + student.getUserInfo().getLastName())
        .sectionName(student.getSection() != null ? student.getSection().getName() : "")
        .gradeName(student.getSection().getGradeName())
        .admissionNumber(student.getRollNumber() != null ? student.getRollNumber() : "")
        .fatherName(
            student.getGuardians().stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.FATHER))
                .findAny()
                .map(x -> x.getFirstName() + " " + x.getLastName())
                .orElse(""))
        .motherName(
            student.getGuardians().stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.MOTHER))
                .findAny()
                .map(x -> x.getFirstName() + " " + x.getLastName())
                .orElse(""))
        .dateOfBirth(dateOfBirth.map(StudentAttributeValueModel::getValue).orElse(""))
        .rollNo(student.getClassRollNumber() != null ? student.getClassRollNumber() : "")
        .bloodGroup(bloodGroup.map(StudentAttributeValueModel::getValue).orElse(""))
        .mobileNumber(mobileNumber.map(StudentAttributeValueModel::getValue).orElse(""))
        .address(address.map(StudentAttributeValueModel::getValue).orElse(""))
        .attendance("")
        .remarks("")
        .issueDate("")
        .subjects(subjects)
        .build();
  }

  private ScholarsReportCardDto.Subject buildSubjects(
      Long termId,
      LmrCategoryGradeAttribute lmrCategoryGradeAttribute,
      List<LmrCategoryAttributeDefinition> lmrCategoryAttributeDefinition,
      List<LmrStudentDetail> lmrStudentDetail) {
    return ScholarsReportCardDto.Subject.builder()
        .name(lmrCategoryGradeAttribute.getAttributeName())
        .term("term " + termId)
        .skillGroups(buildSkillGroups(lmrCategoryAttributeDefinition, lmrStudentDetail))
        .build();
  }

  private List<ScholarsReportCardDto.SkillGroup> buildSkillGroups(
      List<LmrCategoryAttributeDefinition> lmrCategoryAttributeDefinition,
      List<LmrStudentDetail> lmrStudentDetail) {
    List<ScholarsReportCardDto.SkillGroup> skillGroups = new ArrayList<>();
    List<ScholarsReportCardDto.Skill> skills = new ArrayList<>();
    for (LmrCategoryAttributeDefinition attributeDefinition : lmrCategoryAttributeDefinition) {
      skills.add(
          ScholarsReportCardDto.Skill.builder()
              .name(attributeDefinition.getDefinition())
              .grade(attributeDefinition.getStandard())
              .build());

      skillGroups.add(
          ScholarsReportCardDto.SkillGroup.builder()
              .groupName(lmrStudentDetail.getFirst().getSkillValue())
              .skills(skills)
              .build());
    }
    return skillGroups;
  }
}
