package com.wexl.dps.mlp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record MlpDto() {

  @Builder
  public record Subject(
      @JsonProperty("name") String name,
      @JsonProperty("slug") String slug,
      List<Grades> grades,
      List<Sections> sections,
      List<OrgResponse> orgResponses) {}

  @Builder
  public record Grades(
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("order_id") Integer orderId,
      @JsonProperty("knowledge_percentage") Double knowledgePercentage) {}

  @Builder
  public record Sections(
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("section_uuid") String sectionUuid,
      @JsonProperty("knowledge_percentage") Double knowledgePercentage) {}

  @Builder
  public record OrgGrades(
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("orgs") List<OrgResponse> orgResponses) {}

  @Builder
  public record OrgResponse(
      @JsonProperty("org_name") String orgName,
      @JsonProperty("org_slug") String orgSlug,
      @JsonProperty("knowledge_percentage") Double knowledgePercentage) {}
}
