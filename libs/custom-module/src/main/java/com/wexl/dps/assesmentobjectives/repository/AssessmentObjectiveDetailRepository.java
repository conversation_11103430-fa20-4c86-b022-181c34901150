package com.wexl.dps.assesmentobjectives.repository;

import com.wexl.dps.assesmentobjectives.model.AssessmentObjective;
import com.wexl.dps.assesmentobjectives.model.AssessmentObjectiveDetail;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface AssessmentObjectiveDetailRepository
    extends JpaRepository<AssessmentObjectiveDetail, Long> {

  boolean existsByTextAndAssessmentObjective(String text, AssessmentObjective assessmentObjective);

  List<AssessmentObjectiveDetail>
      findAllByAssessmentObjectiveAndDeletedAtIsNullOrderByCreatedAtDesc(
          AssessmentObjective assessmentObjective);
}
