package com.wexl.dps.preprimary.model;

import com.wexl.dps.dto.LearningLevel;
import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "dps_preprimary_apr")
public class AcademicPerformanceRecord extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "student_id")
  private Long studentId;

  @Column(name = "attendance")
  private Long attendance;

  @Column(name = "remarks", columnDefinition = "TEXT")
  private String remarks;

  @Column(name = "term_id")
  private Long termId;

  @Column(name = "term_assessments")
  private String termAssessments;

  @Column(name = "teacher_id")
  private Long teacherId;

  @Column(name = "teacher_name")
  private String teacherName;

  @Column(name = "attendance_present")
  private Long attendancePresent;

  @Column(name = "attendance_total")
  private Long attendanceTotal;

  @Column(name = "grade_slug")
  private String gradeSlug;

  @Column(name = "cll_listening")
  @Enumerated(EnumType.STRING)
  private LearningLevel listening;

  @Column(name = "cll_speaking")
  @Enumerated(EnumType.STRING)
  private LearningLevel speaking;

  @Column(name = "cll_reading")
  @Enumerated(EnumType.STRING)
  private LearningLevel reading;

  @Column(name = "cll_writing")
  @Enumerated(EnumType.STRING)
  private LearningLevel writing;

  @Column(name = "math")
  @Enumerated(EnumType.STRING)
  private LearningLevel math;

  @Column(name = "untw")
  @Enumerated(EnumType.STRING)
  private LearningLevel untw;

  @Column(name = "psew")
  @Enumerated(EnumType.STRING)
  private LearningLevel psew;

  @Column(name = "phy_dev")
  @Enumerated(EnumType.STRING)
  private LearningLevel phyDev;

  @Column(name = "tel_hindi")
  @Enumerated(EnumType.STRING)
  private LearningLevel teluguOrHindi;
}
