package com.wexl.dps.config;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@Data
@ConfigurationProperties(prefix = "app.dps")
public class DpsClassTeacherPreference {
  private List<ClassTeacherPreference> classTeacherPreference;

  @Data
  public static class ClassTeacherPreference {
    private String sectionName;
    private String boardSlug;
    private List<String> subjectSlugs;
  }
}
