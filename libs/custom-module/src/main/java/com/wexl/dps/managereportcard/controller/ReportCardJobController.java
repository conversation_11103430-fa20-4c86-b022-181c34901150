package com.wexl.dps.managereportcard.controller;

import com.wexl.dps.managereportcard.service.ReportCardJobService;
import com.wexl.dps.managereportcard.service.ReportCardJobWorkerService;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto.ReportCardJobRequest;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto.ReportCardJobResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/report-card-jobs")
public class ReportCardJobController {
  private final ReportCardJobService reportCardJobService;
  private final ReportCardJobWorkerService workerService;

  @PostMapping
  public ReportCardJobResponse generateReportCardData(
      @PathVariable String orgSlug, @RequestBody ReportCardJobRequest reportCardJobRequest) {
    return reportCardJobService.generateReportCardData(orgSlug, reportCardJobRequest);
  }

  @PostMapping("/{JobId}")
  public void generateReportByJob(@PathVariable("JobId") long jobId) {
    reportCardJobService.generateReportByJob(jobId);
  }
}
