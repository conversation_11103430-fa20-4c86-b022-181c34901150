package com.wexl.dps.dto;

import java.util.List;
import lombok.Builder;

public record Scholar11th12thReportDto() {
  @Builder
  public record Header(String schoolName, String address) {}

  @Builder
  public record Body(
      String name,
      String admissionNumber,
      String rollNo,
      String fatherName,
      String sectionName,
      String height,
      String motherName,
      String dateOfBirth,
      String weight,
      List<SubjectMark> scholosticMandatory,
      String scholosticTotalPercentage,
      String scholosticTotalGrade,
      String attendance,
      List<SkillGroup> coScholosticMandatory,
      List<SkillGroup> coScholosticOptional,
      String remarks,
      String issueDate) {}

  @Builder
  public record SubjectMark(
      String subject, List<Mark> practical, List<Mark> hyexam, List<Mark> total, String grade) {}

  @Builder
  public record Mark(double mm, double mo) {}

  @Builder
  public record SkillGroup(String skillName, String term, List<Skill> skill) {}

  @Builder
  public record Skill(String subject, String grade) {}
}
