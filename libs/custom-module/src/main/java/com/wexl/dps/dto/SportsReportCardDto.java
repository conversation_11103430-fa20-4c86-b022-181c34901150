package com.wexl.dps.dto;

import com.wexl.retail.offlinetest.dto.LowerGradeReportDto;
import java.util.List;
import lombok.Builder;

public record SportsReportCardDto() {
  @Builder
  public record Response(LowerGradeReportDto.Header header, LowerGradeReportDto.Body body) {}

  @Builder
  public record Header(
      String schoolName,
      String academicYear,
      String admissionNumber,
      String address,
      String isoData,
      Long studentId) {}

  @Builder
  public record Body(
      String orgSlug,
      String name,
      String sectionName,
      Long studentId,
      String rollNumber,
      String overallLevel,
      String overallGrade,
      String gameTitle,
      List<Table> table,
      List<MainTable> mainTable) {}

  @Builder
  public record Overall(String overallLevel, String overallGrade) {}

  @Builder
  public record Table(String tableTitle, Column columns, List<AreaOfLearning> attributes) {}

  @Builder
  public record Column(String column1, String column2, String column3, String column4) {}

  @Builder
  public record AreaOfLearning(String areaOfLearning, Column ratings) {}

  @Builder
  public record MainTable(
      String tableName,
      String AreaOfLearning,
      String LearningLevel,
      String Grade,
      double sumMarks,
      Long skillValueSize) {}
}
