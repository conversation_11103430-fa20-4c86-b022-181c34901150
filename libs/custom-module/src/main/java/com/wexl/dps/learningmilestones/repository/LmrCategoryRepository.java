package com.wexl.dps.learningmilestones.repository;

import com.wexl.dps.learningmilestones.model.LmrCategory;
import com.wexl.dps.learningmilestones.model.LmrCategoryType;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface LmrCategoryRepository extends JpaRepository<LmrCategory, Long> {
  List<LmrCategory> findByIdInAndType(List<Long> categoryIds, LmrCategoryType type);

  @Query(
      value =
          """
                  select distinct lc.* from lmr_categories lc
                  inner join lmr_category_grades lcg on lcg.lmr_category_id = lc.id\s
                  inner join lmr_category_grade_attributes lcga on lcga.lmr_category_grade_id = lcg.id\s
                  inner join lmr_student_details lsd on lsd.lmr_category_attribute_id = lcga.id\s
                  where lsd.org_slug = :orgSlug and lsd.student_id = :studentId and lsd.term_id = :termId and lsd.skill_value != '' and lsd.skill_value is not null order by lc."sequence" asc\s
                  """,
      nativeQuery = true)
  List<LmrCategory> getLmrCategoriesByStudent(String orgSlug, Long studentId, Long termId);

  @Query(
      value =
          """
                             select lc.* from lmr_categories lc
                             join lmr_category_grades lcg on lc.id = lcg.lmr_category_id
                             join lmr_subject_metadata lsm on lsm.lmr_category_grade_id = lcg.id
                            where lsm.subject_metadata_id = :subjectMetadataId and lcg.term_id = :termId
                  """,
      nativeQuery = true)
  List<LmrCategory> findBySubjectMetadataId(Long subjectMetadataId, Long termId);
}
