package com.wexl.dps.learningmilestones.service;

import static com.wexl.dps.learningmilestones.model.LmrCategoryType.COMPREHENSIVE;

import com.wexl.dps.DpsService;
import com.wexl.dps.dto.ComprehensiveReportDto;
import com.wexl.dps.dto.LearningLevel;
import com.wexl.dps.learningmilestones.dto.LmrDto;
import com.wexl.dps.learningmilestones.dto.LmrStudentDto;
import com.wexl.dps.learningmilestones.model.*;
import com.wexl.dps.learningmilestones.repository.*;
import com.wexl.dps.managereportcard.dto.EyReportDto;
import com.wexl.dps.preprimary.service.PrePrimaryAprService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.Student;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.subjects.repository.SubjectsMetaDataRepository;
import com.wexl.retail.util.ValidationUtils;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class LmrStudentService {

  private final LmrStudentDetailRepository lmrStudentDetailRepository;
  private final SectionService sectionService;
  private final StudentService studentService;
  private final CategoryGradeAttributeRepository gradeAttributeRepository;
  private final LmrCategoryGradeRepository categoryGradeRepository;
  private final LmrCategoryRepository categoryRepository;
  private final ValidationUtils validationUtils;
  private final PrePrimaryAprService prePrimaryAprService;
  private final LmrStudentDetailAchievementRepository achievementRepository;
  private final LmrSubjectMetadataRepository lmrSubjectMetadataRepository;
  private final SubjectsMetaDataRepository subjectsMetaDataRepository;
  private final DpsService dpsService;

  public void createLmrStudentDetail(
      String orgSlug, LmrStudentDto.LmrStudentRequest lmrStudentRequest) {

    var lmrStudentDetails =
        lmrStudentDetailRepository.findAllByOrgSlugAndStudentIdAndTermId(
            orgSlug, lmrStudentRequest.studentId(), lmrStudentRequest.termId());

    var student = validationUtils.isStudentValid(lmrStudentRequest.studentId());
    var categoryAttributes =
        gradeAttributeRepository.getCategoryAttribute(
            lmrStudentRequest.categoryId(),
            student.getSection().getGradeSlug(),
            lmrStudentRequest.termId(),
            null);

    var lmrStudentDetailMap =
        lmrStudentDetails.stream()
            .collect(Collectors.toMap(LmrStudentDetail::getLmrCategoryAttributeId, obj -> obj));
    updateLmrStudentAchievement(lmrStudentRequest, categoryAttributes.getFirst());
    if (Objects.isNull(lmrStudentRequest.skillRequests())
        || lmrStudentRequest.skillRequests().isEmpty()
        || categoryAttributes.isEmpty()) {
      return;
    }
    var studentSkillMap =
        lmrStudentRequest.skillRequests().stream()
            .collect(
                Collectors.toMap(
                    LmrStudentDto.StudentSkillRequest::lmrCategoryAttributeId, obj -> obj));

    var lmrStudentDetailList =
        categoryAttributes.stream()
            .map(
                category -> {
                  var lmrStudentDetailByMap = lmrStudentDetailMap.get(category.getId());
                  var lmrStudentDetail =
                      Objects.nonNull(lmrStudentDetailByMap)
                          ? lmrStudentDetailByMap
                          : new LmrStudentDetail();

                  var skillRequest = studentSkillMap.get(category.getId());

                  lmrStudentDetail.setOrgSlug(orgSlug);
                  lmrStudentDetail.setStudentId(lmrStudentRequest.studentId());
                  lmrStudentDetail.setTermId(lmrStudentRequest.termId());
                  lmrStudentDetail.setLmrCategoryAttributeId(category.getId());
                  if (Objects.nonNull(skillRequest)) {
                    lmrStudentDetail.setSkillValue(skillRequest.skillValue());
                  }
                  return lmrStudentDetail;
                })
            .toList();
    lmrStudentDetailRepository.saveAll(new ArrayList<>(lmrStudentDetailList));
  }

  private void updateLmrStudentAchievement(
      LmrStudentDto.LmrStudentRequest lmrStudentRequest,
      LmrCategoryGradeAttribute lmrCategoryGradeAttribute) {
    var lmrCategoryGrade =
        categoryGradeRepository
            .findById(lmrCategoryGradeAttribute.getLmrCategoryGradeId())
            .orElseThrow();
    lmrCategoryGrade.setTotalWorkingDays(lmrStudentRequest.totalAttendance());
    categoryGradeRepository.save(lmrCategoryGrade);
    var lmrCategory = categoryRepository.findById(lmrCategoryGrade.getLmrCategoryId());
    if (lmrCategory.isEmpty()
        || !LmrCategoryType.COMPREHENSIVE.equals(lmrCategory.get().getType())) {
      return;
    }

    var lmrStudentDetailAchievement =
        achievementRepository
            .findByStudentId(lmrStudentRequest.studentId())
            .orElse(new LmrStudentDetailAchievement());
    lmrStudentDetailAchievement.setAchievements(lmrStudentRequest.achievements());
    lmrStudentDetailAchievement.setComments(lmrStudentRequest.comments());
    lmrStudentDetailAchievement.setStudentId(lmrStudentRequest.studentId());
    lmrStudentDetailAchievement.setAttendancePresent(lmrStudentRequest.attendancePresent());
    lmrStudentDetailAchievement.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
    achievementRepository.save(lmrStudentDetailAchievement);
  }

  private LmrCategory validateCategory(long categoryId) {
    return categoryRepository
        .findById(categoryId)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid category"));
  }

  public LmrStudentDto.LmrStudentResponse getLmrStudentDetail(
      String orgSlug, String sectionUuid, Long termId, Long category, Long subjectMetaDataId) {
    var section = sectionService.findByUuid(sectionUuid);

    var lmrCategory = validateCategory(category);

    var students = studentService.getStudentsBySections(new HashSet<>(List.of(section)));
    var studentIds = students.stream().map(Student::getId).toList();

    var categoryGrade =
        categoryGradeRepository.getLmrGradeByOrgSlugAndGradeAndTermAndCategory(
            orgSlug, section.getGradeSlug(), lmrCategory.getId(), termId);

    var gradeAttributes =
        gradeAttributeRepository.getCategoryAttribute(
            category, section.getGradeSlug(), termId, categoryGrade.get().getId());

    var gradeAttributesIds =
        gradeAttributes.stream().map(LmrCategoryGradeAttribute::getId).toList();

    var lmrStudentDetailResponses =
        lmrStudentDetailRepository
            .findAllByOrgSlugAndStudentIdInAndTermIdAndLmrCategoryAttributeIdIn(
                orgSlug, studentIds, termId, gradeAttributesIds);

    return LmrStudentDto.LmrStudentResponse.builder()
        .categoryId(lmrCategory.getId())
        .categoryName(lmrCategory.getName())
        .totalAttendance(categoryGrade.map(LmrCategoryGrade::getTotalWorkingDays).orElse(null))
        .studentResponses(
            buildLmrStudentDetailResponse(lmrStudentDetailResponses, lmrCategory.getType()))
        .build();
  }

  private List<LmrStudentDto.StudentDetailResponse> buildLmrStudentDetailResponse(
      List<LmrStudentDetail> lmrStudentDetails, LmrCategoryType lmrCategoryType) {
    if (Objects.isNull(lmrStudentDetails) || lmrStudentDetails.isEmpty()) {
      return Collections.emptyList();
    }

    Map<Long, List<LmrStudentDetail>> studentDetailMap =
        lmrStudentDetails.stream().collect(Collectors.groupingBy(LmrStudentDetail::getStudentId));

    List<LmrStudentDto.StudentDetailResponse> studentDetailResponses = new ArrayList<>();

    studentDetailMap.forEach(
        (student, studentDetail) -> {
          var studentDetailResponse =
              LmrStudentDto.StudentDetailResponse.builder()
                  .studentId(student)
                  .attributes(buildStudentAttribute(studentDetail));
          if (LmrCategoryType.COMPREHENSIVE.equals(lmrCategoryType)) {
            buildLmrStudentComments(student, studentDetailResponse);
          }
          studentDetailResponses.add(studentDetailResponse.build());
        });
    return studentDetailResponses;
  }

  private void buildLmrStudentComments(
      Long student,
      LmrStudentDto.StudentDetailResponse.StudentDetailResponseBuilder studentDetailResponse) {
    var studentDetailAchievement = achievementRepository.findByStudentId(student);
    if (studentDetailAchievement.isPresent()) {
      studentDetailResponse.comments(studentDetailAchievement.get().getComments());
      studentDetailResponse.achievements(studentDetailAchievement.get().getAchievements());
      studentDetailResponse.attendancePresent(
          studentDetailAchievement.get().getAttendancePresent());
    }
  }

  private List<LmrStudentDto.AttributeResponse> buildStudentAttribute(
      List<LmrStudentDetail> studentDetails) {
    return studentDetails.stream()
        .map(
            studentDetail ->
                LmrStudentDto.AttributeResponse.builder()
                    .studentDetailId(studentDetail.getId())
                    .skillValue(studentDetail.getSkillValue())
                    .attributeId(studentDetail.getLmrCategoryAttributeId())
                    .build())
        .toList();
  }

  public LmrDto.LmrResponse getLearningMileStoneAttributes(
      String gradeSlug,
      LmrCategoryType lmrCategoryType,
      Long subjectMetadataId,
      String orgSlug,
      Long termId) {

    var categoryGrades =
        categoryGradeRepository.getAllByGradeSlugAndSubjectMetadataIdAndTerm(
            gradeSlug, subjectMetadataId, termId);
    var categoryIds = categoryGrades.stream().map(LmrCategoryGrade::getLmrCategoryId).toList();
    var categories = categoryRepository.findByIdInAndType(categoryIds, lmrCategoryType);
    var gradeAttributes =
        gradeAttributeRepository.findByLmrCategoryGradeIdIn(
            categoryGrades.stream().map(LmrCategoryGrade::getId).toList());

    var categoryGradeMap =
        categoryGrades.stream().collect(Collectors.groupingBy(LmrCategoryGrade::getLmrCategoryId));

    var categoryResponses =
        categories.stream()
            .map(
                category -> {
                  var lmrCategoryGrades = categoryGradeMap.get(category.getId());
                  var lmrCategoryGradeAttributes =
                      Objects.isNull(lmrCategoryGrades)
                          ? new ArrayList<LmrCategoryGradeAttribute>()
                          : gradeAttributes.stream()
                              .filter(
                                  ga ->
                                      lmrCategoryGrades.stream()
                                          .anyMatch(
                                              cg -> cg.getId().equals(ga.getLmrCategoryGradeId())))
                              .toList();

                  var grades = categoryGrades.stream().map(LmrCategoryGrade::getGradeSlug).toList();
                  return LmrDto.LmrCategoryResponse.builder()
                      .id(category.getId())
                      .categoryName(category.getName())
                      .grades(grades)
                      .attributes(buildAttributes(lmrCategoryGradeAttributes, orgSlug))
                      .build();
                })
            .toList();

    return LmrDto.LmrResponse.builder().categoryResponses(categoryResponses).build();
  }

  private List<LmrDto.LmrAttributes> buildAttributes(
      List<LmrCategoryGradeAttribute> gradeAttributes, String orgSlug) {
    return gradeAttributes.stream()
        .map(
            attribute ->
                LmrDto.LmrAttributes.builder()
                    .attributeId(attribute.getId())
                    .attributeName(
                        (orgSlug.equals("del189476")
                                && attribute.getAttributeName().equals("DRAMA"))
                            ? "YOGA"
                            : attribute.getAttributeName())
                    .uuid(attribute.getUuid())
                    .build())
        .toList();
  }

  public ReportCardConfigDto.SecondPage getEyLmrReport(
      Student student, Long termId, String orgSlug) {
    Section section = student.getSection();

    var lmrCategories =
        categoryRepository.getLmrCategoriesByStudent(
            student.getUserInfo().getOrganization(), student.getId(), termId);

    var thirdTableBuilder = ReportCardConfigDto.ThirdTable.builder();
    var firstTableBuilder = ReportCardConfigDto.FirstTable.builder();
    var secondTableBuilder = ReportCardConfigDto.SecondTable.builder();

    if (lmrCategories.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.lmrDataNotFound");
    }
    for (int i = 0; i < lmrCategories.size(); i++) {

      var categoryGrade =
          categoryGradeRepository.getLmrGradeByOrgSlugAndGradeAndTermAndCategory(
              orgSlug, section.getGradeSlug(), lmrCategories.get(i).getId(), termId);

      var gradeAttributes =
          gradeAttributeRepository.getCategoryAttribute(
              lmrCategories.get(i).getId(),
              student.getSection().getGradeSlug(),
              termId,
              categoryGrade.get().getId());
      if (orgSlug.equals("del189476")) {
        gradeAttributes.forEach(
            attr -> {
              if ("DRAMA".equalsIgnoreCase(attr.getAttributeName())) {
                attr.setAttributeName("YOGA");
              }
            });
      }
      if (i == 0) {
        buildTable(
            lmrCategories.get(i), gradeAttributes, student, termId, firstTableBuilder, null, null);
      } else if (i == 1) {
        buildTable(
            lmrCategories.get(i), gradeAttributes, student, termId, null, secondTableBuilder, null);
      } else if (i == 2) {
        buildTable(
            lmrCategories.get(i), gradeAttributes, student, termId, null, null, thirdTableBuilder);
      }
    }
    return ReportCardConfigDto.SecondPage.builder()
        .firstTable(firstTableBuilder.build())
        .secondTable(secondTableBuilder.build())
        .thirdTable(thirdTableBuilder.build())
        .build();
  }

  private void buildTable(
      LmrCategory lmrCategory,
      List<LmrCategoryGradeAttribute> gradeAttributes,
      Student student,
      Long termId,
      ReportCardConfigDto.FirstTable.FirstTableBuilder firstTable,
      ReportCardConfigDto.SecondTable.SecondTableBuilder secondTable,
      ReportCardConfigDto.ThirdTable.ThirdTableBuilder thirdTable) {

    var attributeIds = gradeAttributes.stream().map(LmrCategoryGradeAttribute::getId).toList();
    var lmrStudentDetailList =
        lmrStudentDetailRepository.findAllByStudentIdAndTermIdAndLmrCategoryAttributeIdIn(
            student.getId(), termId, attributeIds);

    var tableAreas = ReportCardConfigDto.Table.builder();
    var tableLearningLevel = ReportCardConfigDto.Table.builder();
    var tableGrade = ReportCardConfigDto.Table.builder();

    var lmrStudentDetailMap =
        lmrStudentDetailList.stream()
            .collect(Collectors.toMap(LmrStudentDetail::getLmrCategoryAttributeId, lsd -> lsd));

    for (int i = 0; i < gradeAttributes.size(); i++) {

      var lmrStudentDetail = lmrStudentDetailMap.get(gradeAttributes.get(i).getId());

      var learningLevel =
          Objects.nonNull(lmrStudentDetail) ? lmrStudentDetail.getSkillValue() : null;

      var grade =
          !StringUtils.isEmpty(learningLevel)
              ? prePrimaryAprService.getGradeByLearningLevel(LearningLevel.valueOf(learningLevel))
              : null;
      String transformedLearningLevel =
          Objects.nonNull(learningLevel) ? learningLevel.replace("_", " ") : learningLevel;

      for (int J = 0; J < gradeAttributes.size(); J++) {
        if (i == 0) {
          tableAreas.column1(gradeAttributes.get(i).getAttributeName());
          tableLearningLevel.column1(transformedLearningLevel);
          tableGrade.column1(grade);
        } else if (i == 1) {
          tableAreas.column2(gradeAttributes.get(i).getAttributeName());
          tableLearningLevel.column2(transformedLearningLevel);
          tableGrade.column2(grade);
        } else if (i == 2) {
          tableAreas.column3(gradeAttributes.get(i).getAttributeName());
          tableLearningLevel.column3(transformedLearningLevel);
          tableGrade.column3(grade);
        } else if (i == 3) {
          tableAreas.column4(gradeAttributes.get(i).getAttributeName());
          tableLearningLevel.column4(transformedLearningLevel);
          tableGrade.column4(grade);
        } else if (i == 4) {
          tableAreas.column5(gradeAttributes.get(i).getAttributeName());
          tableLearningLevel.column5(transformedLearningLevel);
          tableGrade.column5(grade);
        }
      }

      if (Objects.nonNull(firstTable)) {
        firstTable
            .firstTableTitle(lmrCategory.getName())
            .firstTableAreas(tableAreas.build())
            .firstTableLearningLevel(tableLearningLevel.build())
            .firstTableGrade(tableGrade.build());
      } else if (Objects.nonNull(secondTable)) {
        secondTable
            .secondTableTitle(lmrCategory.getName())
            .secondTableAreas(tableAreas.build())
            .secondTableLearningLevel(tableLearningLevel.build())
            .secondTableGrade(tableGrade.build());
      } else if (Objects.nonNull(thirdTable)) {
        thirdTable
            .thirdTableTitle(lmrCategory.getName())
            .thirdTableLearningLevel(tableLearningLevel.build())
            .thirdTableAreas(tableAreas.build())
            .thirdTableGrade(tableGrade.build());
      }
    }
  }

  public List<EyReportDto.SecondPage> getEyLmrReportForOverall(
      Student student, ReportCardDto.Request request) {

    List<LmrCategoryGrade> lmrCategoryGrades =
        categoryGradeRepository.getAllByGradeSlugAndSubjectMetadataIdAndTerm(
            student.getSection().getGradeSlug(),
            request.offlineTestDefinitionId(),
            request.termId());

    var lmrCategoryIds =
        lmrCategoryGrades.stream().map(LmrCategoryGrade::getLmrCategoryId).toList();
    var lmrCategories =
        categoryRepository.findByIdInAndType(
            new ArrayList<>(lmrCategoryIds), LmrCategoryType.EY_REPORT);

    List<EyReportDto.SecondPage> secondPages = new ArrayList<>();

    for (var lmrCategory : lmrCategories) {
      var lmrGradeIds =
          lmrCategoryGrades.stream()
              .filter(lcg -> lcg.getLmrCategoryId().equals(lmrCategory.getId()))
              .map(LmrCategoryGrade::getId)
              .toList();
      var gradeAttributes = gradeAttributeRepository.findByLmrCategoryGradeIdIn(lmrGradeIds);

      var attributeIds = gradeAttributes.stream().map(LmrCategoryGradeAttribute::getId).toList();
      var lmrStudentDetailList =
          lmrStudentDetailRepository.findAllByStudentIdAndTermIdAndLmrCategoryAttributeIdIn(
              student.getId(), request.termId(), attributeIds);
      var lmrStudentDetailMap =
          lmrStudentDetailList.stream()
              .collect(Collectors.toMap(LmrStudentDetail::getLmrCategoryAttributeId, lsd -> lsd));

      var columnNames = new ArrayList<String>();
      var learningLevels = new ArrayList<String>();
      var grades = new ArrayList<String>();

      for (var gradeAttribute : gradeAttributes) {
        columnNames.add(gradeAttribute.getAttributeName());
        var lmrStudentDetail = lmrStudentDetailMap.get(gradeAttribute.getId());
        learningLevels.add(
            Objects.nonNull(lmrStudentDetail) ? lmrStudentDetail.getSkillValue() : "");
        grades.add(prePrimaryAprService.getGradeByLearningLevel(getSkillValue(lmrStudentDetail)));
      }

      var skillsOfFutures =
          EyReportDto.SecondTables.builder()
              .column1(columnNames.size() > 0 ? columnNames.get(0) : "")
              .column2(columnNames.size() > 1 ? columnNames.get(1) : "")
              .column3(columnNames.size() > 2 ? columnNames.get(2) : "")
              .column4(columnNames.size() > 3 ? columnNames.get(3) : "")
              .column5(columnNames.size() > 4 ? columnNames.get(4) : "")
              .column6(columnNames.size() > 5 ? columnNames.get(5) : "")
              .column7(columnNames.size() > 6 ? columnNames.get(6) : "")
              .build();

      var performanceRecords =
          EyReportDto.SecondTables.builder()
              .column1(buildLevelLevel(learningLevels.size() > 0 ? learningLevels.get(0) : ""))
              .column2(buildLevelLevel(learningLevels.size() > 1 ? learningLevels.get(1) : ""))
              .column3(buildLevelLevel(learningLevels.size() > 2 ? learningLevels.get(2) : ""))
              .column4(buildLevelLevel(learningLevels.size() > 3 ? learningLevels.get(3) : ""))
              .column5(buildLevelLevel(learningLevels.size() > 4 ? learningLevels.get(4) : ""))
              .column6(buildLevelLevel(learningLevels.size() > 5 ? learningLevels.get(5) : ""))
              .column7(buildLevelLevel(learningLevels.size() > 6 ? learningLevels.get(6) : ""))
              .build();

      var gradesTables =
          EyReportDto.SecondTables.builder()
              .column1(grades.size() > 0 ? grades.get(0) : "")
              .column2(grades.size() > 1 ? grades.get(1) : "")
              .column3(grades.size() > 2 ? grades.get(2) : "")
              .column4(grades.size() > 3 ? grades.get(3) : "")
              .column5(grades.size() > 4 ? grades.get(4) : "")
              .column6(grades.size() > 5 ? grades.get(5) : "")
              .column7(grades.size() > 6 ? grades.get(6) : "")
              .build();

      var secondPage =
          EyReportDto.SecondPage.builder()
              .title(lmrCategory.getName())
              .skillsOfFutures(skillsOfFutures)
              .performanceRecords(performanceRecords)
              .grades(gradesTables)
              .build();

      secondPages.add(secondPage);
    }
    final boolean allColumnsAreEmpty =
        IntStream.range(0, Math.min(secondPages.size(), 6))
            .allMatch(
                index -> {
                  var records = secondPages.get(index).performanceRecords();
                  return isNullOrEmpty(records.column1())
                      && isNullOrEmpty(records.column2())
                      && isNullOrEmpty(records.column3())
                      && isNullOrEmpty(records.column4())
                      && isNullOrEmpty(records.column5());
                });

    if (allColumnsAreEmpty) {
      return Collections.emptyList();
    }

    return secondPages;
  }

  public boolean isNullOrEmpty(String value) {
    return value == null || value.isEmpty();
  }

  private String buildLevelLevel(String learningLevel) {
    return Objects.nonNull(learningLevel) ? learningLevel.replace("_", " ") : null;
  }

  private LearningLevel getSkillValue(LmrStudentDetail lmrStudentDetail) {
    try {
      if (Objects.nonNull(lmrStudentDetail)
          && !StringUtils.isEmpty(lmrStudentDetail.getSkillValue())) {
        if ("A*".equals(lmrStudentDetail.getSkillValue())) {
          return LearningLevel.A_STAR;
        }
        return LearningLevel.valueOf(lmrStudentDetail.getSkillValue());
      }
      return LearningLevel.NA;
    } catch (Exception e) {
      log.error("Enum not found error :{}", e.getMessage(), e);
      return LearningLevel.NA;
    }
  }

  public List<ComprehensiveReportDto.FirstTable> getEyLmrForComprehensive(
      Student student, Long termId) {
    List<LmrCategoryGrade> lmrCategoryGrades =
        categoryGradeRepository.findAllByGradeSlug(student.getSection().getGradeSlug());
    var lmrCategoryIds =
        lmrCategoryGrades.stream().map(LmrCategoryGrade::getLmrCategoryId).toList();

    var lmrCategories =
        categoryRepository.findByIdInAndType(new ArrayList<>(lmrCategoryIds), COMPREHENSIVE);
    List<LmrCategory> categoryData =
        lmrCategories.stream()
            .filter(
                category -> "CONTINUOUS COMPREHENSIVE EVALUATION CARD".equals(category.getName()))
            .collect(Collectors.toList());
    var gradeAttributes =
        gradeAttributeRepository.getCategoryAttributes(
            categoryData.get(0).getId(), student.getSection().getGradeSlug(), termId);
    var gradeIds = gradeAttributes.stream().map(LmrCategoryGradeAttribute::getId).toList();
    var lmrStudentDetailList =
        lmrStudentDetailRepository.findAllByStudentIdAndTermIdAndLmrCategoryAttributeIdIn(
            student.getId(), termId, gradeIds);
    var lmrStudentDetailMap =
        lmrStudentDetailList.stream()
            .collect(Collectors.toMap(LmrStudentDetail::getLmrCategoryAttributeId, lsd -> lsd));

    return gradeAttributes.stream()
        .map(
            attribute -> {
              String attributeName = attribute.getAttributeName();
              String subject = attributeName.substring(attributeName.indexOf('-') + 1).trim();
              LmrStudentDetail lmrStudentDetail = lmrStudentDetailMap.get(attribute.getId());
              String skill =
                  lmrStudentDetail != null
                      ? getColorBySkillValue(lmrStudentDetail.getSkillValue())
                      : " ";

              return ComprehensiveReportDto.FirstTable.builder()
                  .subjects(subject)
                  .color(skill)
                  .build();
            })
        .collect(Collectors.toList());
  }

  public String getColorBySkillValue(String skillValue) {
    if (skillValue == null) {
      return StringUtils.EMPTY;
    }

    return switch (skillValue) {
      case "A" -> "green";
      case "B" -> "yellow";
      case "C" -> "orange";
      case "D" -> "pink";
      case "E" -> "blue";
      case "ABSENT", "Absent" -> "AB";
      default -> StringUtils.EMPTY; // Handle any other unmatched cases
    };
  }

  public void createLmrCategoryGradeTermMigration(LmrStudentDto.TermMigrationRequest request) {
    var lmrSubjectMetadata =
        lmrSubjectMetadataRepository.findAllBySubjectMetadataId(request.subjectMetadataId());
    List<LmrCategoryGrade> lcgList = new ArrayList<>();
    List<LmrSubjectMetadata> lsmList = new ArrayList<>();

    var lmrCategoryGradeIds =
        lmrSubjectMetadata.stream().map(LmrSubjectMetadata::getLmrCategoryGradeId).toList();
    var lmrCategoryGrades = categoryGradeRepository.findByIdInAndTermId(lmrCategoryGradeIds, 1L);
    lmrCategoryGrades.forEach(
        lcg -> {
          var lmrCategoryAttributes =
              gradeAttributeRepository.findByLmrCategoryGradeIdIn(
                  Collections.singletonList(lcg.getId()));
          request
              .termId()
              .forEach(
                  termId -> {
                    LmrCategoryGrade lmrGrade = new LmrCategoryGrade();
                    lmrGrade.setLmrCategoryId(lcg.getLmrCategoryId());
                    lmrGrade.setGradeSlug(lcg.getGradeSlug());
                    lmrGrade.setGradeName(lcg.getGradeName());
                    lmrGrade.setTermId(termId);
                    var newLcg = categoryGradeRepository.save(lmrGrade);
                    lcgList.add(newLcg);
                    List<LmrCategoryGradeAttribute> lcgaList = new ArrayList<>();
                    lmrCategoryAttributes.forEach(
                        attribute -> {
                          LmrCategoryGradeAttribute newAttribute = new LmrCategoryGradeAttribute();
                          newAttribute.setAttributeName(attribute.getAttributeName());
                          newAttribute.setLmrCategoryGradeId(newLcg.getId());
                          lcgaList.add(newAttribute);
                        });
                    gradeAttributeRepository.saveAll(lcgaList);
                  });
        });

    lcgList.forEach(
        lcg -> {
          LmrSubjectMetadata lsm = new LmrSubjectMetadata();
          lsm.setLmrCategoryGradeId(lcg.getId());
          lsm.setSubjectMetadataId(request.subjectMetadataId());
          lsmList.add(lsm);
        });
    lmrSubjectMetadataRepository.saveAll(lsmList);
  }

  public void studentAttributeMigration(
      LmrStudentDto.AttributeMigrationRequest request, String orgSlug) {
    var allLmrStudentDetails =
        lmrStudentDetailRepository.findAllByOrgSlugAndTermId(orgSlug, request.termId());
    List<Long> studentIds =
        allLmrStudentDetails.stream().map(LmrStudentDetail::getStudentId).distinct().toList();
    for (Long studentId : studentIds) {
      var lmrStudentDetails =
          lmrStudentDetailRepository.findAllByOrgSlugAndStudentIdAndTermId(
              orgSlug, studentId, request.termId());
      List<LmrStudentDetail> lcgaList = new ArrayList<>();
      lmrStudentDetails.forEach(
          lmrStudentDetail -> {
            var lmrAttribute =
                gradeAttributeRepository
                    .findById(lmrStudentDetail.getLmrCategoryAttributeId())
                    .orElseThrow();
            LmrCategoryGrade lmrCategoryGrade =
                categoryGradeRepository
                    .findById(lmrAttribute.getLmrCategoryGradeId())
                    .orElseThrow();
            var lmrSubjectMetadata =
                lmrSubjectMetadataRepository.findByLmrCategoryGradeId(lmrCategoryGrade.getId());
            var subjectMetadata =
                subjectsMetaDataRepository
                    .findByIdInAndOrgSlug(
                        lmrSubjectMetadata.stream()
                            .map(LmrSubjectMetadata::getSubjectMetadataId)
                            .toList(),
                        orgSlug)
                    .orElseThrow();
            var lmrCategoryGradeAndTerm =
                categoryGradeRepository
                    .getLmrGradeByGradeSlugAndSubjectMetadataIdAndTermAndCategory(
                        lmrCategoryGrade.getGradeSlug(),
                        subjectMetadata.getId(),
                        request.termId(),
                        lmrCategoryGrade.getLmrCategoryId())
                    .orElseThrow();
            var newAttribute =
                gradeAttributeRepository
                    .findByLmrCategoryGradeIdAndAttributeName(
                        lmrCategoryGradeAndTerm.getId(), lmrAttribute.getAttributeName())
                    .orElseThrow();
            lmrStudentDetail.setLmrCategoryAttributeId(newAttribute.getId());
            lcgaList.add(lmrStudentDetail);
          });
      lmrStudentDetailRepository.saveAll(lcgaList);
    }
  }

  public ComprehensiveReportDto.AttendanceTable buildDpsStudentAttendance(
      ComprehensiveReportDto.AttendanceTable data) {
    return ComprehensiveReportDto.AttendanceTable.builder()
        .nwdMarch(data.nwdMarch())
        .npdMarch(data.npdMarch())
        .npMarchAttendancePercentage(data.npMarchAttendancePercentage())
        .nwdApril(data.nwdApril())
        .npdApril(data.npdApril())
        .npAprilAttendancePercentage(data.npAprilAttendancePercentage())
        .nwdMay(data.nwdMay())
        .npdMay(data.npdMay())
        .npMayAttendancePercentage(data.npMayAttendancePercentage())
        .nwdJune(data.nwdJune())
        .npdJune(data.npdJune())
        .npJuneAttendancePercentage(data.npJuneAttendancePercentage())
        .nwdJuly(data.nwdJuly())
        .npdJuly(data.npdJuly())
        .npJulyAttendancePercentage(data.npJulyAttendancePercentage())
        .nwdAug(data.nwdAug())
        .npdAug(data.npdAug())
        .npAugAttendancePercentage(data.npAugAttendancePercentage())
        .nwdSept(data.nwdSept())
        .npdSept(data.npdSept())
        .npSeptAttendancePercentage(data.npSeptAttendancePercentage())
        .nwdOct(data.nwdOct())
        .npdOct(data.npdOct())
        .npOctAttendancePercentage(data.npOctAttendancePercentage())
        .nwdNov(data.nwdNov())
        .npdNov(data.npdNov())
        .npNovAttendancePercentage(data.npNovAttendancePercentage())
        .nwdDec(data.nwdDec())
        .npdDec(data.npdDec())
        .npDecAttendancePercentage(data.npDecAttendancePercentage())
        .nwdJan(data.nwdJan())
        .npdJan(data.npdJan())
        .npJanAttendancePercentage(data.npJanAttendancePercentage())
        .nwdFeb(data.nwdFeb())
        .npdFeb(data.npdFeb())
        .npFebAttendancePercentage(data.npFebAttendancePercentage())
        .attendancePercentage1(data.attendancePercentage1())
        .attendancePercentage2(data.attendancePercentage2())
        .build();
  }

  public ComprehensiveReportDto.Term1AttendanceTotal buildTerm1AttendanceTotal(
      ComprehensiveReportDto.AttendanceTable data) {
    Long term1TotalWorkingDays =
        sumIfNotNull(
            data.nwdMarch(),
            data.nwdApril(),
            data.nwdMay(),
            data.nwdJune(),
            data.nwdJuly(),
            data.nwdAug(),
            data.nwdSept(),
            data.nwdOct());

    Long term1TotalPresentDays =
        sumIfNotNull(
            data.npdMarch(),
            data.npdApril(),
            data.npdMay(),
            data.npdJune(),
            data.npdJuly(),
            data.npdAug(),
            data.npdSept(),
            data.npdOct());

    return ComprehensiveReportDto.Term1AttendanceTotal.builder()
        .term1TotalWorkingDays(term1TotalWorkingDays)
        .term1TotalPresentDays(term1TotalPresentDays)
        .build();
  }

  public ComprehensiveReportDto.Term2AttendanceTotal buildTerm2AttendanceTotal(
      ComprehensiveReportDto.AttendanceTable data) {
    Long term2TotalWorkingDays =
        sumIfNotNull(
            data.nwdOct(),
            data.nwdNov(),
            data.nwdDec(),
            data.nwdJan(),
            data.nwdFeb(),
            data.nwdMarch());

    Long term2TotalPresentDays =
        sumIfNotNull(
            data.npdOct(),
            data.npdNov(),
            data.npdDec(),
            data.npdJan(),
            data.npdFeb(),
            data.npdMarch());

    return ComprehensiveReportDto.Term2AttendanceTotal.builder()
        .term2TotalWorkingDays(term2TotalWorkingDays)
        .term2TotalPresentDays(term2TotalPresentDays)
        .build();
  }

  private Long sumIfNotNull(Long... values) {
    return Arrays.stream(values).filter(Objects::nonNull).reduce((a, b) -> a + b).orElse(null);
  }

  public void saveShowStudentsStatus(LmrStudentDto.LmrSportsSubjectRequest request) {
    if (!SubjectsTypeEnum.SPORTS.equals(request.subjectType())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.invalidInput");
    }
    List<LmrSubjectMetadata> lmrSubjectMetadataList =
        lmrSubjectMetadataRepository.findAllBySubjectMetadataIdAndTermId(
            request.subjectMetaDataId(), request.termId());

    for (LmrSubjectMetadata subjectMetaData : lmrSubjectMetadataList) {
      subjectMetaData.setShowStudent(request.showStudent());
    }

    lmrSubjectMetadataRepository.saveAll(lmrSubjectMetadataList);
  }

  public LmrDto.ShowStudentsStatus showStudentsStatus(Long subjectMetadataId, Long termId) {
    var showStudentStatus =
        lmrSubjectMetadataRepository.showStudentsStatus(termId, subjectMetadataId);
    return LmrDto.ShowStudentsStatus.builder()
        .showStudentsStatus(Boolean.TRUE.equals(showStudentStatus))
        .build();
  }
}
