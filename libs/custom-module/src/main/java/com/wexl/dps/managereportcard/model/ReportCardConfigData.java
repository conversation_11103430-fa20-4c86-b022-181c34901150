package com.wexl.dps.managereportcard.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import com.wexl.retail.reportcards.model.ReportCardJob;
import com.wexl.retail.reportcards.model.StudentReportCard;
import jakarta.persistence.*;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@RequiredArgsConstructor
@Builder
@Entity
@AllArgsConstructor
@Table(name = "report_card_config_data")
public class ReportCardConfigData extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "term_name")
  private String termName;

  @Column(name = "term_assessment_name")
  private String termAssessmentName;

  @Column(name = "otss_id")
  private Long offlineTestScheduleStudentId;

  @Column(name = "student_id")
  private Long studentId;

  @ManyToOne(fetch = FetchType.LAZY)
  private ReportCardConfigDetail reportCardConfigDetail;

  @Column(name = "actual_marks")
  private BigDecimal actualMarks;

  @Column(name = "calculated_marks")
  private BigDecimal calculatedMarks;

  @ManyToOne(fetch = FetchType.LAZY)
  private ReportCardJob reportCardJob;

  @ManyToOne(fetch = FetchType.LAZY)
  private StudentReportCard studentReportCard;

  private String failureReason;
}
