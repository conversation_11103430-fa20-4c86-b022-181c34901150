package com.wexl.dps.managereportcard.service;

import static jakarta.transaction.Transactional.TxType.REQUIRES_NEW;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.dps.learningmilestones.repository.LmrSubjectMetadataRepository;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDetailRepository;
import com.wexl.dps.managereportcard.repository.ReportCardConfigRepository;
import com.wexl.dps.managereportcard.repository.ReportCardJobRepository;
import com.wexl.dps.reportcard.*;
import com.wexl.pallavi.reports.OverAllHolisticReportCard;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.commons.util.ResourceUtils;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.Entity;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.model.Student;
import com.wexl.retail.offlinetest.dto.ReportCard;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.*;
import com.wexl.retail.offlinetest.repository.*;
import com.wexl.retail.offlinetest.service.OfflineTestReportService;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.offlinetest.service.ReportCardTemplateService;
import com.wexl.retail.offlinetest.service.pointscale.*;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.reportcards.dto.AssessmentEvaluationConfig;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto;
import com.wexl.retail.reportcards.model.ReportCardConfig;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import com.wexl.retail.reportcards.model.ReportCardJob;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.domain.TeacherSubjects;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.repository.TeacherSubjectsRepository;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.subjects.repository.SubjectsMetaDataRepository;
import com.wexl.retail.subjects.repository.SubjectsMetadataStudentsRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.term.dto.TermDto;
import com.wexl.retail.term.model.TermAssessmentCategory;
import com.wexl.retail.term.repository.TermAssessmentCategoryRepository;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import com.wexl.retail.term.repository.TermRepository;
import com.wexl.retail.term.service.TermService;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.util.ValidationUtils;
import jakarta.transaction.Transactional;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.antlr.v4.runtime.misc.MultiMap;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ManageReportCardService {
  private final ReportCardConfigRepository reportCardConfigRepository;
  private final ReportCardConfigDetailRepository reportCardConfigDetailRepository;
  private final ContentService contentService;
  private final StrapiService strapiService;
  private final TermAssessmentRepository termAssessmentRepository;
  private final TermRepository termRepository;
  private final OrganizationRepository organizationRepository;
  private final ReportCardJobRepository reportCardJobRepository;
  private final DateTimeUtil dateTimeUtil;
  private final SectionRepository sectionRepository;
  private final OfflineTestDefinitionRepository offlineTestDefinitionRepository;
  private final OfflineTestScheduleStudentRepository offlineTestScheduleStudentRepository;
  private final UserService userService;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final OfflineTestScheduleService offlineTestScheduleService;
  private final TermAssessmentCategoryRepository termAssessmentCategoryRepository;
  private final TeacherSubjectsRepository teacherSubjectsRepository;
  private final TermService termService;
  private final ReportCardTemplateRepository reportCardTemplateRepository;
  private final OfflineTestReportService offlineTestReportService;
  private final ValidationUtils validationUtils;
  private final AuthService authService;
  private final StudentAuthService studentAuthService;
  private final ReportCardConfigDataRepository configDataRepository;
  private final ReportCardTemplateGradeRepository reportCardTemplateGradeRepository;
  private final LowerGradeOverallReportCard lowerGradeOverallReportCard;
  private final StudentRepository studentRepository;
  private final ReportCardTemplateService reportCardTemplateService;
  private final UpperGradeReportCard upperGradeReportCard;
  private final UpperGradeOverAllReportCard upperGradeOverAllReportCard;
  private final OverAllHolisticReportCard overAllHolisticReportCard;
  private final UpperGradeSecondTermReportCard upperGradeSecondTermReportCard;
  private final InterFirstGradeReportCard interFirstGradeReportCard;
  private final PallaviProgressCard pallaviProgressCard;
  private final SectionService sectionService;
  private final SubjectsMetaDataRepository subjectsMetaDataRepository;
  private final String DPS_PARENT_ORG_SLUG = "dps673625";
  private final SubjectsMetadataStudentsRepository subjectsMetadataStudentsRepository;
  private static final String SPORTS_REPORT_CARD_CONFIG = "sports-report-card.xml";
  private final LmrSubjectMetadataRepository lmrSubjectMetadataRepository;
  private static final List<String> TERM_SLUGS = List.of("t1", "t2");

  @Value("${app.latestAcademicYear}")
  private String latestAcademicYear;

  @Value("classpath:canned-report.json")
  private Resource cannedReportResource;

  @Value("classpath:custom-term1-report.json")
  private Resource customTerm1Resource;

  @Value("classpath:custom-term2-report.json")
  private Resource customTerm2Resource;

  @Value("classpath:sai-senior-canned-report.json")
  private Resource saiSeriorCannedResource;

  @Value("classpath:sai-senior-custom-report.json")
  private Resource saiSeniorCustomResource;

  @Value("classpath:sai-senior-t2-custom-report.json")
  private Resource saiSeniorCustomTerm2Resource;

  public void createReportCard(
      String orgSlug, ReportCardConfigDto.ReportCardRequest reportCardRequest) {
    var board = strapiService.getEduBoardBySlug(reportCardRequest.boardSlug());
    var grade = contentService.getGradeBySlug(reportCardRequest.gradeSlug());
    ReportCardConfig reportCard = new ReportCardConfig();
    reportCard.setOrgSlug(orgSlug);
    reportCard.setTitle(reportCardRequest.title());
    reportCard.setBoardSlug(board.getSlug());
    reportCard.setBoardName(board.getName());
    reportCard.setGradeSlug(grade.getSlug());
    reportCard.setGradeName(grade.getName());
    reportCard.setReportCardConfigDetails(
        createReportCardDetails(reportCard, reportCardRequest.termRequest()));
    reportCard.setStudentViewEnabled(reportCardRequest.studentViewEnabled());
    reportCard.setTemplateId(reportCardRequest.reportCardTemplateId());
    reportCard.setSubjectMetadataId(reportCardRequest.subjectMetadataId());
    reportCard.setWithMarks(reportCardRequest.withMarks());
    reportCardConfigRepository.save(reportCard);
  }

  private List<ReportCardConfigDetail> createReportCardDetails(
      ReportCardConfig reportCard, List<ReportCardConfigDto.TermRequest> termRequests) {
    List<ReportCardConfigDetail> response = new ArrayList<>();
    for (ReportCardConfigDto.TermRequest termRequest : termRequests) {
      var testAssessment =
          termAssessmentRepository
              .findById(termRequest.assessmentId())
              .orElseThrow(
                  () ->
                      new ApiException(
                          InternalErrorCodes.INVALID_REQUEST, "error.AssessmentNotFound"));
      ReportCardConfigDetail reportCardDetails = new ReportCardConfigDetail();
      reportCardDetails.setReportCard(reportCard);
      reportCardDetails.setTermAssessment(testAssessment);
      reportCardDetails.setWeightage(termRequest.weightage());
      reportCardDetails.setSeqNo(termRequest.seqNo());
      response.add(reportCardDetails);
    }
    return response;
  }

  public ReportCardConfigDto.ReportCardJobDetails getReportCardConfigs(String orgSlug) {
    List<ReportCardConfigDto.ReportCardConfigResponse> response = new ArrayList<>();
    var org = organizationRepository.findBySlug(orgSlug);
    List<ReportCardConfig> reportCardConfigs =
        reportCardConfigRepository.findAllByOrgSlugOrderByIdDesc(org.getSlug());
    var reportCardJobs = reportCardJobRepository.getLatestJobsGroupByReportConfig(orgSlug);
    var reportCardJobMap =
        reportCardJobs.stream()
            .collect(Collectors.toMap(ReportCardJob::getReportCardConfig, rj -> rj));
    for (ReportCardConfig reportCardConfig : reportCardConfigs) {
      var reportCardJob = reportCardJobMap.get(reportCardConfig);
      var isJobPresent = Objects.nonNull(reportCardJob);
      response.add(
          ReportCardConfigDto.ReportCardConfigResponse.builder()
              .id(reportCardConfig.getId())
              .title(reportCardConfig.getTitle())
              .boardName(reportCardConfig.getBoardName())
              .boardSlug(reportCardConfig.getBoardSlug())
              .gradeName(reportCardConfig.getGradeName())
              .gradeSlug(reportCardConfig.getGradeSlug())
              .jobStatus(
                  isJobPresent
                      ? reportCardJob.getStatus()
                      : ReportCardConfigDto.ReportCardJobStatus.NOT_STARTED)
              .reportCardJobId(isJobPresent ? reportCardJob.getId() : null)
              .lastProcessedAt(
                  isJobPresent
                      ? dateTimeUtil.convertTimeStampToLong(reportCardJob.getUpdatedAt())
                      : null)
              .studentViewEnabled(reportCardConfig.getStudentViewEnabled())
              .failureReason(isJobPresent ? reportCardJob.getFailureReason() : null)
              .build());
    }
    return ReportCardConfigDto.ReportCardJobDetails.builder()
        .reportCardConfigResponse(response)
        .build();
  }

  public ReportCardConfigDto.ReportCardConfigResponse
      getReportCardConfigDetailsByReportCardConfigId(Long reportCardConfigId) {
    final var reportCardConfig = validateReportCardConfig(reportCardConfigId);

    return ReportCardConfigDto.ReportCardConfigResponse.builder()
        .id(reportCardConfig.getId())
        .title(reportCardConfig.getTitle())
        .boardName(reportCardConfig.getBoardName())
        .boardSlug(reportCardConfig.getBoardSlug())
        .gradeSlug(reportCardConfig.getGradeSlug())
        .gradeName(reportCardConfig.getGradeName())
        .withMarks(reportCardConfig.getWithMarks())
        .studentViewEnabled(Boolean.TRUE.equals(reportCardConfig.getStudentViewEnabled()))
        .reportCardTemplateId(reportCardConfig.getTemplateId())
        .subjectMetadataId(reportCardConfig.getSubjectMetadataId())
        .reportCardTemplateType(
            reportCardConfig.getTemplateId() == null
                ? null
                : reportCardTemplateService
                    .getTemplateById(reportCardConfig.getTemplateId())
                    .getReportCardTemplateType()
                    .name())
        .reportCardConfigDetails(
            buildReportCardConfigDetails(reportCardConfig.getReportCardConfigDetails()))
        .build();
  }

  public ReportCardConfig validateReportCardConfig(Long reportCardConfigId) {
    return reportCardConfigRepository
        .findById(reportCardConfigId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST, "error.ReportCardConfigNotFound"));
  }

  public ReportCardConfigDetail validateReportCardConfigDetail(Long detailId) {
    return reportCardConfigDetailRepository
        .findById(detailId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST, "error.ReportCardConfigDetailNotFound"));
  }

  private List<ReportCardConfigDto.ReportCardConfigDetails> buildReportCardConfigDetails(
      List<ReportCardConfigDetail> reportCardDetails) {
    List<ReportCardConfigDto.ReportCardConfigDetails> response = new ArrayList<>();
    for (ReportCardConfigDetail reportCardConfigDetail : reportCardDetails) {
      response.add(
          ReportCardConfigDto.ReportCardConfigDetails.builder()
              .id(reportCardConfigDetail.getId())
              .termId(reportCardConfigDetail.getTermAssessment().getTerm().getId())
              .termName(reportCardConfigDetail.getTermAssessment().getTerm().getName())
              .assessmentId(reportCardConfigDetail.getTermAssessment().getId())
              .assessmentName(reportCardConfigDetail.getTermAssessment().getName())
              .weightage(reportCardConfigDetail.getWeightage())
              .seqNo(reportCardConfigDetail.getSeqNo())
              .build());
    }
    return response;
  }

  public void updateReportCardConfigDetail(Long id, ReportCardConfigDto.TermRequest request) {
    var reportCardConfigDetail = validateReportCardConfigDetail(id);
    reportCardConfigDetail.setWeightage(request.weightage());
    reportCardConfigDetail.setSeqNo(request.seqNo());
    reportCardConfigDetailRepository.save(reportCardConfigDetail);
  }

  public void deleteReportCardConfigDetailById(Long id) {
    var reportCardConfigDetail = validateReportCardConfigDetail(id);
    var isReportDataExists =
        configDataRepository.existsByReportCardConfigDetail(reportCardConfigDetail);
    if (isReportDataExists) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.reportDataExists");
    }
    reportCardConfigDetailRepository.delete(reportCardConfigDetail);
  }

  private void validateReportDataByReportDetail(ReportCardConfigDetail reportCardConfigDetail) {
    var isReportDataExists =
        configDataRepository.existsByReportCardConfigDetail(reportCardConfigDetail);
    if (isReportDataExists) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.reportDataExists");
    }
  }

  public void deleteReportCardConfigById(Long id) {
    final var reportCardConfig = validateReportCardConfig(id);
    var reportCardConfigDetails = reportCardConfig.getReportCardConfigDetails();
    reportCardConfigDetails.forEach(this::validateReportDataByReportDetail);
    reportCardConfigRepository.delete(reportCardConfig);
  }

  public void updateReportCardConfig(
      Long id, ReportCardConfigDto.ReportCardRequest reportCardRequest) {
    final var reportCardConfig = validateReportCardConfig(id);
    reportCardConfig.setStudentViewEnabled(reportCardRequest.studentViewEnabled());
    reportCardConfig.setTemplateId(reportCardRequest.reportCardTemplateId());
    reportCardConfig.setSubjectMetadataId(reportCardRequest.subjectMetadataId());
    reportCardConfig.setWithMarks(reportCardRequest.withMarks());
    reportCardConfigRepository.save(reportCardConfig);
    reportCardConfigDetailRepository.saveAll(
        createReportCardDetails(reportCardConfig, reportCardRequest.termRequest()));
  }

  public List<GenericMetricResponse> buildSectionReportResponse(
      String orgSlug,
      String board,
      String gradeSlug,
      String subjectSlug,
      Long offlineTestDefinitionId) {
    List<GenericMetricResponse> response = new ArrayList<>();
    Map<String, Object> data = new HashMap<>();

    var org = organizationRepository.findBySlug(orgSlug);
    var subject = strapiService.getSubjectNameBySlug(subjectSlug);

    var assessment =
        offlineTestScheduleService
            .validateOfflineTestDefinition(offlineTestDefinitionId)
            .getAssessment();

    var offlineTestDefinition =
        offlineTestDefinitionRepository
            .findById(offlineTestDefinitionId)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "error.Invalid.OfflineTestDefinition"));

    List<OfflineTestDefinition> offlineTestDefinitions =
        offlineTestDefinitionRepository
            .findAllByOrgSlugAndBoardSlugAndGradeSlugAndAssessmentAndTitleAndGradeScaleSlugNotNull(
                orgSlug, board, gradeSlug, assessment, offlineTestDefinition.getTitle());
    if (offlineTestDefinitions.isEmpty()) {
      return Collections.emptyList();
    }

    List<ReportCardConfigDto.SectionDetail> sectionDetails =
        buildSectionDetail(offlineTestDefinitions, subjectSlug).stream()
            .sorted(Comparator.comparing(ReportCardConfigDto.SectionDetail::section))
            .toList();
    data.put(
        "response",
        ReportCardConfigDto.ReportCardSectionWiseResponse.builder()
            .academicYear(offlineTestDefinitions.getFirst().getAcademicYearSlug())
            .campus(org.getName())
            .curriculum(offlineTestDefinitions.getFirst().getBoardName())
            .classGrade(offlineTestDefinitions.getFirst().getGradeName())
            .examType(assessment.getName())
            .subject(subject)
            .sections(sectionDetails)
            .build());
    response.add(GenericMetricResponse.builder().data(data).build());
    return response;
  }

  public List<ReportCardConfigDto.SectionDetail> buildSectionDetail(
      List<OfflineTestDefinition> offlineTestDefinitions, String subject) {

    List<ReportCardConfigDto.SectionDetail> response = new ArrayList<>();

    for (OfflineTestDefinition otd : offlineTestDefinitions) {

      var section =
          sectionRepository.findByUuid(UUID.fromString(otd.getSectionUuid())).orElseThrow();

      List<TeacherSubjects> teacherSubject =
          teacherSubjectsRepository.findBySubjectAndSectionId(subject, section.getId());

      var teacher =
          teacherSubject.isEmpty()
              ? "-"
              : userService.getNameByUserInfo(teacherSubject.getFirst().getTeacher().getUserInfo());
      var offlineTestScheduleStudents =
          offlineTestScheduleStudentRepository.getScheduleStudentsByDefinitionAndSubject(
              otd.getId(), subject);

      Map<String, Integer> gradeCountMap = new HashMap<>();

      for (OfflineTestScheduleStudent otss : offlineTestScheduleStudents) {

        BigDecimal marks =
            Objects.isNull(otss.getMarks())
                ? null
                : otss.getMarks()
                    .divide(
                        BigDecimal.valueOf(otss.getOfflineTestScheduleDetails().getMarks()),
                        2,
                        RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));

        if (Objects.nonNull(marks)) {
          String grade = pointScaleEvaluator.evaluate(otd.getGradeScaleSlug(), marks);
          gradeCountMap.put(grade, gradeCountMap.getOrDefault(grade, 0) + 1);
        }
      }
      var absentStudentCount =
          offlineTestScheduleStudents.stream()
              .filter(
                  otss ->
                      otss.getIsAttended() == null
                          || !otss.getIsAttended()
                          || otss.getMarks() == null)
              .count();
      var gradesList = pointScaleEvaluator.getGradesByPointScale(otd.getGradeScaleSlug());
      response.add(
          ReportCardConfigDto.SectionDetail.builder()
              .section(section.getName())
              .teacherName(teacher)
              .absents(absentStudentCount)
              .totalStudents(offlineTestScheduleStudents.size())
              .gradeDetails(buildGradeAndCount(gradeCountMap, gradesList))
              .build());
    }
    return response;
  }

  private List<ReportCardConfigDto.GradeDetail> buildGradeAndCount(
      Map<String, Integer> gradeCountMap, List<String> pointScaleGrades) {
    List<ReportCardConfigDto.GradeDetail> response = new ArrayList<>();
    pointScaleGrades.forEach(
        psg -> {
          var gradeCount = gradeCountMap.get(psg);
          if (Objects.nonNull(gradeCount)) {
            response.add(
                ReportCardConfigDto.GradeDetail.builder().grade(psg).count(gradeCount).build());
          } else {
            response.add(ReportCardConfigDto.GradeDetail.builder().grade(psg).count(0).build());
          }
        });
    return response;
  }

  public void reportCardAssessmentEvaluationConfig(
      Long configDetailId, ReportCardConfigDto.MergeAssessmentCategoryRequest request) {
    var configDetail = validateReportCardConfigDetail(configDetailId);
    var assessmentEvaluationConfig =
        Objects.nonNull(configDetail.getAssessmentEvaluationConfig())
            ? configDetail.getAssessmentEvaluationConfig()
            : new AssessmentEvaluationConfig();

    assessmentEvaluationConfig.setAssessmentCategories(validateCategories(request.categoryIds()));
    configDetail.setAssessmentEvaluationConfig(assessmentEvaluationConfig);
    reportCardConfigDetailRepository.save(configDetail);
  }

  private Set<Long> validateCategories(List<Long> categoryIds) {

    var assessmentCategories = termAssessmentCategoryRepository.findByIdIn(categoryIds);
    return assessmentCategories.stream()
        .map(TermAssessmentCategory::getId)
        .collect(Collectors.toSet());
  }

  public List<TermDto.TermAssessmentCategoryDetails> getSelectedAssessmentCategories(
      String orgSlug, Long reportCardConfigId, Long termAssessmentId) {
    List<TermDto.TermAssessmentCategoryDetails> response = new ArrayList<>();
    var termAssessment = termService.validateTermAssessment(termAssessmentId);
    var assessmentCategories =
        termAssessmentCategoryRepository.findAllByOrgSlugAndTermAssessmentOrderBySeqNoDesc(
            orgSlug, termAssessment);
    if (assessmentCategories.isEmpty()) {
      return Collections.emptyList();
    }
    var reportCardConfigDetail =
        reportCardConfigDetailRepository
            .getSelectedCategoriesByReportCardConfigAndTermAssessment(
                reportCardConfigId, termAssessmentId)
            .orElseThrow();
    var selectedAssessmentCategoryIds = new ArrayList<>();
    if (Objects.nonNull(reportCardConfigDetail.getAssessmentEvaluationConfig())) {
      selectedAssessmentCategoryIds.addAll(
          reportCardConfigDetail.getAssessmentEvaluationConfig().getAssessmentCategories());
    }

    for (TermAssessmentCategory category : assessmentCategories) {
      response.add(
          TermDto.TermAssessmentCategoryDetails.builder()
              .id(category.getId())
              .categoryName(category.getName())
              .seqNo(category.getSeqNo())
              .assessmentName(category.getTermAssessment().getName())
              .termName(category.getTermAssessment().getTerm().getName())
              .isSelected(
                  selectedAssessmentCategoryIds.contains(category.getId())
                      ? Boolean.TRUE
                      : Boolean.FALSE)
              .build());
    }
    return response;
  }

  public byte[] getStudentAdmitCard(String org, String studentAuthId, Long testDefinitionId) {
    return getAdmitCard(org, studentAuthId, testDefinitionId, "admit-card.xml");
  }

  public byte[] getPallaviStudentAdmitCard(
      String orgSlug, String studentAuthId, Long testDefinitionId) {
    return getAdmitCard(orgSlug, studentAuthId, testDefinitionId, "pallavi-admit-card.xml");
  }

  public byte[] getDpsStudentEyAdmitCard(
      String orgSlug, String studentAuthId, Long testDefinitionId) {
    return getAdmitCard(orgSlug, studentAuthId, testDefinitionId, "cambridge-admit-card.xml");
  }

  private byte[] getAdmitCard(
      String org, String studentAuthId, Long testDefinitionId, String templateConfig) {
    var reportCardTemplate =
        reportCardTemplateRepository
            .findByOrgSlugAndReportCardTemplateTypeAndConfig(
                org, ReportCardTemplateType.CANNED, templateConfig)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "error.ReportTemplateNotFound"));
    var student = validationUtils.validateStudentByAuthId(studentAuthId, org);
    validateStudentFeeDefaulter(student);
    var request =
        ReportCardDto.Request.builder()
            .offlineTestDefinitionId(testDefinitionId)
            .studentAuthId(studentAuthId)
            .build();
    return offlineTestReportService.getStudentReportByOfflineTestDefinition(
        org, reportCardTemplate.getId(), request);
  }

  private void validateStudentFeeDefaulter(Student student) {
    if (Boolean.TRUE.equals(student.getFeeDefaulter())) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Please contact the administration as your fee payment is due.");
    }
  }

  public byte[] getStudentReportCard(
      String orgSlug,
      List<String> termSlugs,
      Long testDefinitionId,
      ReportCardTemplateType templateType,
      Long reportCardTemplateId) {
    try {
      var student = studentAuthService.validateStudentByUser(authService.getStudentDetails());
      if (termSlugs == null && testDefinitionId != null) {
        termSlugs = new ArrayList<>();
        var offlineTestDefinition =
            offlineTestScheduleService.validateOfflineTestDefinition(testDefinitionId);
        termSlugs.add(offlineTestDefinition.getTermSlug());
      }
      Long templateId = null;
      if (reportCardTemplateId == null) {
        templateId = getReportTemplateId(student, orgSlug, termSlugs, templateType);
      } else {
        templateId = reportCardTemplateId;
      }
      validateStudentFeeDefaulter(student);
      if (ReportCardTemplateType.CANNED.equals(templateType) && testDefinitionId == null) {
        testDefinitionId = getTestDefinitionId(student);
      }
      var term =
          termRepository
              .findBySlug(termSlugs.getFirst())
              .orElseThrow(
                  () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TermNotFound"));
      var reportCardConfig =
          reportCardConfigRepository.findAllByOrgSlugAndBoardSlugAndGradeSlugAndTemplateId(
              orgSlug,
              student.getSection().getBoardSlug(),
              student.getSection().getGradeSlug(),
              templateId);
      Boolean withMarks =
          reportCardConfig.isEmpty() || Objects.isNull(reportCardConfig.getFirst().getWithMarks())
              ? false
              : reportCardConfig.getFirst().getWithMarks();
      var request =
          ReportCardDto.Request.builder()
              .offlineTestDefinitionId(testDefinitionId)
              .studentAuthId(authService.getStudentDetails().getAuthUserId())
              .termId(term.getId())
              .sectionUuid(student.getSection().getUuid().toString())
              .templateId(templateId)
              .withMarks(withMarks)
              .build();

      return offlineTestReportService.getStudentReportByOfflineTestDefinition(
          orgSlug, templateId, request);
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Unable to download the report card. " + e.getMessage(),
          e);
    }
  }

  private Long getTestDefinitionId(Student student) {
    var gradeSlug = student.getSection().getGradeSlug();
    List<Entity> allBoards = new ArrayList<>(strapiService.getAllBoards());
    final Optional<Entity> studentBoard =
        allBoards.stream().filter(board -> board.getId() == student.getBoardId()).findFirst();
    return studentBoard
        .map(
            entity ->
                termAssessmentRepository.getTermIdByBoardAndGrade(entity.getSlug(), gradeSlug))
        .orElse(null);
  }

  private Long getReportTemplateId(
      Student student,
      String orgSlug,
      List<String> termSlugs,
      ReportCardTemplateType templateType) {
    String studentBoard;
    if (Objects.nonNull(student.getSection().getBoardSlug())) {
      studentBoard = student.getSection().getBoardSlug();
    } else {
      List<Entity> allBoards = new ArrayList<>(strapiService.getAllBoards());
      var studentBoardEntity =
          allBoards.stream().filter(board -> board.getId() == student.getBoardId()).findFirst();
      studentBoard = studentBoardEntity.map(Entity::getSlug).orElse(null);
    }
    List<String> dpsChildOrgSlugs = organizationRepository.getAllChildOrgSlugs(DPS_PARENT_ORG_SLUG);
    if (dpsChildOrgSlugs.contains(orgSlug) && "igcse".equals(studentBoard)) {
      var section = student.getSection();
      return reportCardTemplateRepository.getEYTemplateByGradeSlugAndType(
          orgSlug,
          templateType.name(),
          section.getBoardSlug(),
          section.getGradeSlug(),
          "dps-ey-1st-12th-report-card.xml");
    }
    return getReportTemplate(student, orgSlug, termSlugs, templateType);
  }

  public Long getReportTemplate(
      Student student, String orgSlug, List<String> terms, ReportCardTemplateType templateType) {

    var gradeSlug = student.getSection().getGradeSlug();
    var reportCardTemplates =
        reportCardTemplateRepository.getTemplateByGradeSlugAndType(
            orgSlug, templateType.name(), student.getSection().getBoardSlug(), gradeSlug);
    if (reportCardTemplates.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ReportTemplateNotFound");
    }
    if (ReportCardTemplateType.CANNED.equals(templateType)) {
      if (!orgSlug.contains("sai")) {
        return getReportTemplateByResource(gradeSlug, cannedReportResource, reportCardTemplates)
            .getId();
      } else {
        return getReportTemplateByResource(gradeSlug, saiSeriorCannedResource, reportCardTemplates)
            .getId();
      }
    } else if (ReportCardTemplateType.CUSTOM.equals(templateType)) {
      if ("del217242".equals(orgSlug) && List.of("nur", "lkg", "ukg").contains(gradeSlug)) {
        return reportCardTemplateRepository
            .findByOrgSlugAndConfig(orgSlug, "comprehensive-evaluation.xml")
            .map(ReportCardTemplate::getId)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "error.ReportTemplateNotFound"));
      } else if (!orgSlug.contains("sai")) {
        if (terms.contains("t1")) {
          return getReportTemplateByResource(gradeSlug, customTerm1Resource, reportCardTemplates)
              .getId();
        } else if (terms.contains("t2")) {
          return getReportTemplateByResource(gradeSlug, customTerm2Resource, reportCardTemplates)
              .getId();
        }
      } else {
        if (terms.contains("t1")) {
          return getReportTemplateByResource(
                  gradeSlug, saiSeniorCustomResource, reportCardTemplates)
              .getId();
        } else if (terms.contains("t2")) {
          return getReportTemplateByResource(
                  gradeSlug, saiSeniorCustomTerm2Resource, reportCardTemplates)
              .getId();
        }
      }
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Overall report not configured");
    }
    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid report template type");
  }

  public ReportCardTemplate getReportTemplateByResource(
      String grade, Resource resource, List<ReportCardTemplate> reportCardTemplates) {
    var config = findKeyByValue(getReportResource(resource), grade);
    if (reportCardTemplates.getFirst().getOrgSlug().equals("pro162316")) {
      var reportCardTemplate =
          reportCardTemplates.stream()
              .filter(rct -> rct.getConfig().equals("canned-report.xml"))
              .findAny();
      return reportCardTemplate.orElseThrow(
          () ->
              new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ReportTemplateNotFound"));
    } else {
      var reportCardTemplate =
          reportCardTemplates.stream().filter(rct -> rct.getConfig().equals(config)).findAny();
      return reportCardTemplate.orElseThrow(
          () ->
              new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ReportTemplateNotFound"));
    }
  }

  public static String findKeyByValue(MultiMap<String, String> map, String value) {
    for (var entry : map.entrySet()) {
      if (entry.getValue().contains(value)) {
        return entry.getKey();
      }
    }
    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report config not found");
  }

  private MultiMap<String, String> getReportResource(Resource resource) {
    try {
      var objectMapper = new ObjectMapper();
      return objectMapper.readValue(ResourceUtils.asString(resource), new TypeReference<>() {});
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report config not found", e);
    }
  }

  public ReportCard.StudentReportCards getAllStudentReportCards(String orgSlug, String userName) {

    var user = validationUtils.isValidUser(userName);
    var student = user.getStudentInfo();
    var section = student.getSection();

    List<ReportCardTemplate> reportCardTemplates =
        reportCardTemplateRepository.findByOrgSlug(orgSlug);

    List<ReportCardTemplateGrade> reportCardTemplateGrades =
        reportCardTemplateGradeRepository.findByBoardSlugAndGradeSlugAndReportCardTemplateIn(
            section.getBoardSlug(), section.getGradeSlug(), reportCardTemplates);

    if (reportCardTemplateGrades.isEmpty()) {
      return new ReportCard.StudentReportCards(new ArrayList<>());
    }
    List<ReportCard.ReportCardResponse> reportCardResponses = new ArrayList<>();
    reportCardResponses.addAll(getStudentCustomReports(reportCardTemplateGrades, student));
    reportCardResponses.addAll(getStudentCannedReports(student));
    var isSportsReportPresent =
        reportCardTemplateGrades.stream()
            .map(ReportCardTemplateGrade::getReportCardTemplate)
            .filter(rcTemplate -> rcTemplate.getConfig().equals(SPORTS_REPORT_CARD_CONFIG))
            .findFirst();
    isSportsReportPresent.ifPresent(
        reportCardTemplate ->
            reportCardResponses.addAll(getStudentSportsReports(student, reportCardTemplate)));
    return ReportCard.StudentReportCards.builder().reportCardResponses(reportCardResponses).build();
  }

  private List<ReportCard.ReportCardResponse> getStudentSportsReports(
      Student student, ReportCardTemplate sportsReportcardTemplate) {
    List<ReportCard.ReportCardResponse> response = new ArrayList<>();
    var studentSportsSubjectMetadataDetails =
        lmrSubjectMetadataRepository.getStudentSportsSubjectMetadata(student.getId());
    if (studentSportsSubjectMetadataDetails.isEmpty()) {
      return response;
    }
    studentSportsSubjectMetadataDetails.forEach(
        detail ->
            response.add(
                ReportCard.ReportCardResponse.builder()
                    .reportCardTemplateId(sportsReportcardTemplate.getId())
                    .reportCardTemplateType(ReportCardTemplateType.CUSTOM)
                    .subjectMetadataId(detail.getSubjectMetadataId())
                    .subjectMetadataName(detail.getSubjectMetadataName())
                    .subjectMetadataType(detail.getSubjectMetadataType())
                    .termId(detail.getTermId())
                    .termSlug(detail.getTermSlug())
                    .termName(detail.getTermName())
                    .build()));
    return response;
  }

  private List<ReportCard.ReportCardResponse> getStudentCustomReports(
      List<ReportCardTemplateGrade> reportCardTemplateGrades, Student student) {
    var customReportCardTemplates =
        reportCardTemplateGrades.stream()
            .filter(
                rctg ->
                    ReportCardTemplateType.CUSTOM.equals(
                        rctg.getReportCardTemplate().getReportCardTemplateType()))
            .toList();

    var eyReportPresent =
        customReportCardTemplates.stream()
            .filter(
                rct ->
                    List.of("comprehensive-evaluation.xml", "ey-report-card.xml")
                        .contains(rct.getReportCardTemplate().getConfig()))
            .toList();
    if (!eyReportPresent.isEmpty()) {
      return List.of(
          ReportCard.ReportCardResponse.builder()
              .reportCardTemplateType(ReportCardTemplateType.CUSTOM)
              .reportCardTemplateId(eyReportPresent.getFirst().getReportCardTemplate().getId())
              .termSlug("t1")
              .termName("Term 1")
              .build());
    }

    var section = student.getSection();
    var reportCardConfigs =
        reportCardConfigRepository.findAllByOrgSlugAndBoardSlugAndGradeSlugAndStudentViewEnabled(
            section.getOrganization(),
            section.getBoardSlug(),
            section.getGradeSlug(),
            Boolean.TRUE);
    if (customReportCardTemplates.isEmpty() || reportCardConfigs.isEmpty()) {
      return Collections.emptyList();
    }
    return reportCardConfigs.stream()
        .map(
            reportCardConfig -> {
              var term =
                  reportCardConfig
                      .getReportCardConfigDetails()
                      .getFirst()
                      .getTermAssessment()
                      .getTerm();
              return ReportCard.ReportCardResponse.builder()
                  .reportCardTemplateType(ReportCardTemplateType.CUSTOM)
                  .termSlug(term.getSlug())
                  .termName(term.getName())
                  .reportCardConfigId(reportCardConfig.getId())
                  .reportCardTemplateId(reportCardConfig.getTemplateId())
                  .build();
            })
        .toList();
  }

  private List<ReportCard.ReportCardResponse> getStudentCannedReports(Student student) {
    var section = student.getSection();
    List<OfflineTestDefinition> testDefinitions =
        offlineTestDefinitionRepository.getStudentExamTypes(
            section.getOrganization(), student.getId(), section.getUuid().toString());
    return getReportCardResponse(testDefinitions);
  }

  public List<ReportCard.ReportCardResponse> getReportCardResponse(
      List<OfflineTestDefinition> testDefinition) {
    return testDefinition.stream()
        .filter(td -> Boolean.TRUE.equals(td.getShowStudents()))
        .map(
            td ->
                ReportCard.ReportCardResponse.builder()
                    .testDefinitionId(td.getId())
                    .testDefinitionName(td.getTitle())
                    .reportCardTemplateType(ReportCardTemplateType.CANNED)
                    .reportCardTemplateId(td.getReportCardTemplate().getId())
                    .build())
        .toList();
  }

  public List<GenericMetricResponse> buildGradeAndPercentageSummary(
      String orgSlug, String boardSlug, String gradeSlug, String sectionUuid, Boolean withMarks) {
    List<ReportCardConfigDto.GradeAndPercentage> response = null;
    var students = getStudentIds(orgSlug, gradeSlug, boardSlug, sectionUuid);
    if (students.isEmpty()) {
      return Collections.emptyList();
    }
    if (List.of("i", "ii", "iii", "iv", "v").contains(gradeSlug)) {
      response = lowerGradeOverallReportCard.getGradeAndPercentage(students, withMarks);
    } else if (List.of("xi", "xig").contains(gradeSlug)) {
      response = interFirstGradeReportCard.getGradeAndPercentage(students, gradeSlug);
    } else if (List.of("vi", "vii", "viii").contains(gradeSlug)) {
      response = upperGradeOverAllReportCard.getGradeAndPercentage(students);
    } else if (Objects.equals("ix", gradeSlug)) {
      response = upperGradeReportCard.getGradeAndPercentage(students);
    }

    if (response == null || response.isEmpty()) {
      return Collections.emptyList();
    }

    return buildResponse(response);
  }

  private List<GenericMetricResponse> buildResponse(
      List<ReportCardConfigDto.GradeAndPercentage> responseList) {
    if (responseList == null || responseList.isEmpty()) {
      return List.of();
    }

    List<ReportCardConfigDto.GradeAndPercentageResponse> responses =
        responseList.stream()
            .filter(res -> res.student() != null && res.student().getUserInfo() != null)
            .map(
                res -> {
                  var student = res.student();
                  var userInfo = student.getUserInfo();
                  var name = userInfo.getFirstName() + " " + userInfo.getLastName();
                  return ReportCardConfigDto.GradeAndPercentageResponse.builder()
                      .studentName(name)
                      .sectionName(student.getSection().getName())
                      .studentId(getRollNumber(student.getRollNumber()))
                      .grade(res.grade())
                      .percentage(
                          Objects.equals(res.percentage(), "-")
                              ? "-"
                              : String.valueOf(
                                  validationUtils.formatMarks(
                                      Double.parseDouble(res.percentage()))))
                      .build();
                })
            .toList();

    Map<String, Object> data = Map.of("response", sortResponse(responses));
    return List.of(GenericMetricResponse.builder().data(data).build());
  }

  public Long getRollNumber(String rollNumber) {
    try {
      return Long.valueOf(rollNumber);
    } catch (Exception ex) {
      return null;
    }
  }

  private List<ReportCardConfigDto.GradeAndPercentageResponse> sortResponse(
      List<ReportCardConfigDto.GradeAndPercentageResponse> responses) {
    return responses.stream()
        .sorted(
            (r1, r2) -> {
              String p1 = r1.percentage();
              String p2 = r2.percentage();
              if (p1.equals("-") && p2.equals("-")) return 0;
              if (p1.equals("-")) return 1;
              if (p2.equals("-")) return -1;
              return Double.compare(Double.parseDouble(p2), Double.parseDouble(p1));
            })
        .toList();
  }

  private List<Student> getStudentIds(
      String orgSlug, String gradeSlug, String boardSlug, String sectionUuid) {
    List<Section> sections = new ArrayList<>();
    if (sectionUuid == null || sectionUuid.isEmpty()) {
      sections =
          sectionRepository.getSectionsUsingGradeSlugsAndBoardSlugs(
              List.of(gradeSlug), orgSlug, boardSlug);
    } else {
      Section section = sectionRepository.findAllByUuid(UUID.fromString(sectionUuid));
      if (section != null) {
        sections.add(section);
      }
    }
    if (sections.isEmpty()) {
      return List.of();
    }
    return sections.stream()
        .flatMap(
            section -> studentRepository.getStudentsBySectionAndDeletedAtIsNull(section).stream())
        .toList();
  }

  public List<GenericMetricResponse> buildAGradeSummary(
      String orgSlug, String boardSlug, String gradeSlug) {
    List<ReportCardConfigDto.GradeAndPercentageResponse> response = null;
    var students = getStudentIds(orgSlug, gradeSlug, boardSlug, null);

    if (students.isEmpty()) {
      return List.of();
    }

    if ("iii".equals(gradeSlug) || "iv".equals(gradeSlug) || "v".equals(gradeSlug)) {
      if (List.of("pal556078", "pal233196", "pal174599", "pal454783", "pal988947")
          .contains(orgSlug)) {
        response = pallaviProgressCard.getAGradeResponse(students, Boolean.FALSE, gradeSlug);
      } else {
        response =
            lowerGradeOverallReportCard.getAGradeResponse(students, Boolean.FALSE, gradeSlug);
      }
    } else if ("ix".equals(gradeSlug)) {
      response = upperGradeReportCard.getAGradeResponse(students);
    } else if ("vi".equals(gradeSlug) || "vii".equals(gradeSlug) || "viii".equals(gradeSlug)) {
      response = upperGradeSecondTermReportCard.getAGradeResponse(students);
    }

    if (List.of("i", "ii").contains(gradeSlug)) {
      if (List.of("pal556078", "pal174599", "pal233196", "pal174599", "pal454783", "pal988947")
          .contains(orgSlug)) {
        response = overAllHolisticReportCard.getAGradeResponse(students, Boolean.FALSE, gradeSlug);
      } else {
        response =
            lowerGradeOverallReportCard.getAGradeResponse(students, Boolean.FALSE, gradeSlug);
      }
    }

    if (response == null || response.isEmpty()) {
      return List.of();
    }

    Map<String, Object> data = Map.of("response", sortByGradePreference(response));
    return List.of(GenericMetricResponse.builder().data(data).build());
  }

  public List<ReportCardConfigDto.GradeAndPercentageResponse> sortByGradePreference(
      List<ReportCardConfigDto.GradeAndPercentageResponse> marksList) {
    Comparator<ReportCardConfigDto.GradeAndPercentageResponse> gradeComparator =
        Comparator.comparing(
            ReportCardConfigDto.GradeAndPercentageResponse::grade,
            (grade1, grade2) -> {
              List<String> gradeOrder = List.of("A", "A1", "A+");
              int index1 = gradeOrder.indexOf(grade1);
              int index2 = gradeOrder.indexOf(grade2);
              return Integer.compare(index1, index2);
            });

    marksList.sort(gradeComparator);
    return marksList;
  }

  @Transactional(REQUIRES_NEW)
  public void updateReportCardJob(
      Long reportCardJobId,
      ReportCardConfigDto.ReportCardJobStatus jobStatus,
      String failureReason) {
    var possibleCurrentJob = reportCardJobRepository.findById(reportCardJobId);
    if (possibleCurrentJob.isEmpty()) {
      return;
    }
    var reportCardJob = possibleCurrentJob.get();
    reportCardJob.setStatus(jobStatus);
    reportCardJob.setFailureReason(trimTo(failureReason));
    reportCardJob.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
    reportCardJobRepository.save(reportCardJob);
  }

  private String trimTo(String failureReason) {
    if (Objects.nonNull(failureReason) && failureReason.length() > 5000) {
      return failureReason.substring(0, 5000);
    }
    return failureReason;
  }

  public List<GenericMetricResponse> buildAGradeAllSubjectsSummary(
      String orgSlug, String boardSlug, String gradeSlug) {
    List<ReportCardConfigDto.GradeAndPercentageResponse> response = null;
    var students = getStudentIds(orgSlug, gradeSlug, boardSlug, null);

    if (students.isEmpty()) {
      return List.of();
    }

    if (List.of("iii", "iv", "v").contains(gradeSlug)) {
      if (List.of("pal556078", "pal233196", "pal174599", "pal454783", "pal988947")
          .contains(orgSlug)) {
        response = pallaviProgressCard.getAGradeAllSubjectsSummary(students);
      } else {
        response =
            lowerGradeOverallReportCard.getAGradeAllSubjectsSummary(
                students, Boolean.FALSE, gradeSlug);
      }
    } else if (List.of("i", "ii").contains(gradeSlug)) {
      response =
          lowerGradeOverallReportCard.getAGradeAllSubjectsSummary(
              students, Boolean.FALSE, gradeSlug);
    } else if ("ix".equals(gradeSlug)) {
      response = upperGradeReportCard.getAGradeAllSubjectsSummary(students);
    } else if (List.of("vi", "vii", "viii").contains(gradeSlug)) {
      response = upperGradeSecondTermReportCard.getAGradeAllSubjectsSummary(students);
    }
    if (response == null || response.isEmpty()) {
      return List.of();
    }

    Map<String, Object> data = Map.of("response", sortByGradePreference(response));
    return List.of(GenericMetricResponse.builder().data(data).build());
  }

  private List<ReportCard.StudentsList> buildStudentMarks(
      String orgSlug, ReportCard.StudentMarksRequest request) {
    var data =
        offlineTestDefinitionRepository.getStudentMarks(
            orgSlug, request.gradeSlug(), request.boardSlug(), request.sectionUuid());
    var studentList = data.stream().map(StudentMarksData::getStudentId).distinct().toList();
    List<ReportCard.StudentsList> studentResponseList = new ArrayList<>();
    var subjectSlugList =
        offlineTestDefinitionRepository.findSubjectsByOrgSlugAndBoardSlugAndGradeSlugAndSectionUuid(
            orgSlug, request.gradeSlug(), request.boardSlug(), request.sectionUuid());
    studentList.forEach(
        student -> {
          var studentData = data.stream().filter(x -> x.getStudentId().equals(student)).toList();

          studentResponseList.add(
              ReportCard.StudentsList.builder()
                  .rollNumber(student)
                  .classRollNumber(studentData.get(0).getClassRollNumber())
                  .studentId(studentData.get(0).getRollNo())
                  .name(studentData.get(0).getStudentName())
                  .subjects(buildSubjects(studentData, subjectSlugList))
                  .build());
        });

    return studentResponseList;
  }

  private List<Map<String, Object>> buildSubjects(
      List<StudentMarksData> studentData, List<StudentMarksData> subjectList) {
    List<Map<String, Object>> responseList = new ArrayList<>();

    subjectList.forEach(
        subject -> {
          Map<String, Object> subjectMap = new LinkedHashMap<>();
          subjectMap.put("name", subject.getSubjectName());

          var studentSubjectData =
              studentData.stream()
                  .filter(x -> x.getSubjectSlug().equals(subject.getSubjectSlug()))
                  .toList();

          studentSubjectData.forEach(
              data ->
                  subjectMap.put(
                      data.getTitle() + "(" + data.getScheduleMarks() + ")",
                      data.getStudentMarks()));

          if (!subjectMap.isEmpty()) {
            responseList.add(subjectMap);
          }
        });

    return responseList.stream().distinct().toList();
  }

  private List<Map<String, Integer>> getGradeCounts(
      String orgSlug, String gradeSlug, String subjectSlug, List<Student> students) {

    if (List.of("iii", "iv", "v").contains(gradeSlug)) {
      if (List.of("pal556078", "pal233196", "pal174599", "pal454783", "pal988947")
          .contains(orgSlug)) {
        return pallaviProgressCard.getGradeCounts(students, subjectSlug, orgSlug);
      }
      return lowerGradeOverallReportCard.getGradeCounts(students, false, gradeSlug, subjectSlug);
    }

    if (List.of("i", "ii").contains(gradeSlug)) {
      if (List.of("pal556078", "pal233196", "pal174599", "pal454783", "pal988947")
          .contains(orgSlug)) {
        return overAllHolisticReportCard.getGradeCounts(students, subjectSlug, false);
      }
      return lowerGradeOverallReportCard.getGradeCounts(
          students, Boolean.FALSE, gradeSlug, subjectSlug);
    }
    if (Set.of("vi", "vii", "viii").contains(gradeSlug)) {
      return upperGradeOverAllReportCard.getGradeCounts(students, subjectSlug, orgSlug);
    }

    return Collections.emptyList();
  }

  public ResponseEntity<byte[]> downloadGradeCounts(
      String orgSlug, String boardSlug, String gradeSlug, String subjectSlug) throws IOException {

    Workbook workbook = new XSSFWorkbook();
    Sheet sheet = workbook.createSheet("Grade Counts");

    List<Grade> allGrades = strapiService.getAllGrades();
    List<Entity> allBoards = strapiService.getAllBoards();
    Grade grade = validationUtils.findGradeBySlug(allGrades, gradeSlug);
    Entity board = validationUtils.findBoardBySlug(allBoards, boardSlug);

    CellStyle headerStyle = createHeaderStyle(workbook);
    CellStyle borderStyle = createBorderStyle(workbook);

    Row headerRow = sheet.createRow(0);
    String[] headers = {"Academic Year", "Campus", "Curriculum", "Class", "Subject", "Term"};
    for (int i = 0; i < headers.length; i++) {
      Cell cell = headerRow.createCell(i);
      cell.setCellValue(headers[i]);
      cell.setCellStyle(headerStyle);
    }
    var org = validationUtils.isOrgValid(orgSlug);
    Row dataRow = sheet.createRow(1);
    var subject =
        subjectsMetaDataRepository.findByOrgSlugAndWexlSubjectSlugInAndGradeSlugAndBoardSlug(
            orgSlug, Collections.singletonList(subjectSlug), gradeSlug, boardSlug);
    if (subject.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.InvalidSubjectSlug",
          new String[] {subjectSlug});
    }
    String[] data = {
      latestAcademicYear,
      org.getName(),
      board.getAssetName(),
      grade.getName(),
      subject.get(0).getName(),
      "Both"
    };
    for (int i = 0; i < data.length; i++) {
      Cell cell = dataRow.createCell(i);
      cell.setCellValue(data[i]);
      cell.setCellStyle(borderStyle);
    }

    var allSections = sectionService.getSectionsByGrade(orgSlug, gradeSlug);
    var sections = allSections.stream().filter(x -> x.boardSlug().equals(boardSlug)).toList();

    if (sections.isEmpty()) {
      return ResponseEntity.noContent().build();
    }

    List<Student> firstSectionStudents =
        getStudentIds(orgSlug, gradeSlug, boardSlug, sections.get(0).uuid().toString());
    var firstResponse = getGradeCounts(orgSlug, gradeSlug, subjectSlug, firstSectionStudents);

    if (firstResponse == null || firstResponse.isEmpty()) {
      return ResponseEntity.noContent().build();
    }

    Row dynamicHeaderRow = sheet.createRow(2);
    List<String> dynamicHeaders = firstResponse.get(0).keySet().stream().sorted().toList();

    Cell sectionCell = dynamicHeaderRow.createCell(0);
    sectionCell.setCellValue("SECTION");
    sectionCell.setCellStyle(headerStyle);

    for (int i = 0; i < dynamicHeaders.size(); i++) {
      Cell cell = dynamicHeaderRow.createCell(i + 1);
      cell.setCellValue(dynamicHeaders.get(i));
      cell.setCellStyle(headerStyle);
    }

    int rowNum = 3;
    Map<String, Integer> totalCounts = new HashMap<>();

    for (var section : sections) {
      List<Student> students =
          getStudentIds(orgSlug, gradeSlug, boardSlug, section.uuid().toString());
      var response = getGradeCounts(orgSlug, gradeSlug, subjectSlug, students);

      if (response == null || response.isEmpty()) {
        continue;
      }

      Row sectionRow = sheet.createRow(rowNum++);
      Cell sectionRowCell = sectionRow.createCell(0);
      sectionRowCell.setCellValue(section.name());
      sectionRowCell.setCellStyle(borderStyle);

      for (int i = 0; i < dynamicHeaders.size(); i++) {
        int count = response.get(0).getOrDefault(dynamicHeaders.get(i), 0);

        Cell cell = sectionRow.createCell(i + 1);
        cell.setCellValue(count);
        cell.setCellStyle(borderStyle);

        totalCounts.put(
            dynamicHeaders.get(i), totalCounts.getOrDefault(dynamicHeaders.get(i), 0) + count);
      }
    }

    Row totalRow = sheet.createRow(rowNum);
    Cell totalLabelCell = totalRow.createCell(0);
    totalLabelCell.setCellValue("Total");
    totalLabelCell.setCellStyle(borderStyle);

    for (int i = 0; i < dynamicHeaders.size(); i++) {
      Cell cell = totalRow.createCell(i + 1);
      cell.setCellValue(totalCounts.getOrDefault(dynamicHeaders.get(i), 0));
      cell.setCellStyle(borderStyle);
    }

    for (int i = 0; i < headers.length + dynamicHeaders.size(); i++) {
      sheet.autoSizeColumn(i);
    }

    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    workbook.write(outputStream);
    workbook.close();

    byte[] excelBytes = outputStream.toByteArray();

    HttpHeaders headersResponse = new HttpHeaders();
    headersResponse.setContentDisposition(
        ContentDisposition.attachment().filename("grade_counts.xlsx").build());
    headersResponse.setContentType(MediaType.APPLICATION_OCTET_STREAM);

    return new ResponseEntity<>(excelBytes, headersResponse, HttpStatus.OK);
  }

  private CellStyle createHeaderStyle(Workbook workbook) {
    CellStyle style = workbook.createCellStyle();
    Font font = workbook.createFont();
    font.setBold(true);
    font.setColor(IndexedColors.WHITE.getIndex());
    style.setFont(font);
    style.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
    style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
    style.setBorderBottom(BorderStyle.THIN);
    style.setBorderTop(BorderStyle.THIN);
    style.setBorderLeft(BorderStyle.THIN);
    style.setBorderRight(BorderStyle.THIN);
    style.setAlignment(HorizontalAlignment.CENTER);
    style.setVerticalAlignment(VerticalAlignment.CENTER);
    return style;
  }

  private CellStyle createBorderStyle(Workbook workbook) {
    CellStyle style = workbook.createCellStyle();
    style.setBorderBottom(BorderStyle.THIN);
    style.setBorderTop(BorderStyle.THIN);
    style.setBorderLeft(BorderStyle.THIN);
    style.setBorderRight(BorderStyle.THIN);
    style.setAlignment(HorizontalAlignment.CENTER);
    style.setVerticalAlignment(VerticalAlignment.CENTER);
    return style;
  }

  public ResponseEntity<byte[]> downloadStudentMarksList(
      String orgSlug, ReportCard.StudentMarksRequest request) throws IOException {
    Workbook workbook = new XSSFWorkbook();
    Sheet sheet = workbook.createSheet("Grade Counts");

    List<Grade> allGrades = strapiService.getAllGrades();
    List<Entity> allBoards = strapiService.getAllBoards();
    Grade grade = validationUtils.findGradeBySlug(allGrades, request.gradeSlug());
    Entity board = validationUtils.findBoardBySlug(allBoards, request.boardSlug());
    var section = validationUtils.findSectionByUuid(request.sectionUuid());

    CellStyle headerStyle = createHeaderStyle(workbook);
    CellStyle borderStyle = createBorderStyle(workbook);

    Row headerRow = sheet.createRow(0);
    String[] headers = {"Academic Year", "Campus", "Curriculum", "Class", "Section", "Term"};
    for (int i = 0; i < headers.length; i++) {
      Cell cell = headerRow.createCell(i);
      cell.setCellValue(headers[i]);
      cell.setCellStyle(headerStyle);
    }

    var org = validationUtils.isOrgValid(orgSlug);
    Row dataRow = sheet.createRow(1);
    String[] data = {
      latestAcademicYear,
      org.getName(),
      board.getAssetName(),
      grade.getName(),
      section.getName(),
      "Both"
    };
    for (int i = 0; i < data.length; i++) {
      Cell cell = dataRow.createCell(i);
      cell.setCellValue(data[i]);
      cell.setCellStyle(borderStyle);
    }

    var firstResponse = buildStudentMarks(orgSlug, request);
    if (firstResponse == null || firstResponse.isEmpty()) {
      return null;
    }

    Set<String> subjectsFromFirstStudent =
        firstResponse.get(0).subjects().stream()
            .map(map -> (String) map.get("name"))
            .collect(Collectors.toSet());

    Map<String, List<String>> subjectToAssessments =
        firstResponse.stream()
            .flatMap(response -> response.subjects().stream())
            .filter(map -> subjectsFromFirstStudent.contains(map.get("name")))
            .collect(
                Collectors.toMap(
                    map -> (String) map.get("name"),
                    map -> map.keySet().stream().filter(key -> !key.equals("name")).toList(),
                    (existing, replacement) -> {
                      List<String> merged = new ArrayList<>(existing);
                      merged.addAll(replacement);
                      return merged.stream().distinct().toList();
                    }));

    Row subjectRow = sheet.createRow(2);
    int colIndex = 3;
    Cell sectionCell = subjectRow.createCell(0);
    sectionCell.setCellValue("Name");
    sectionCell.setCellStyle(headerStyle);
    Cell sectionCell1 = subjectRow.createCell(1);
    sectionCell1.setCellValue("RollNo");
    sectionCell1.setCellStyle(headerStyle);
    Cell sectionCell2 = subjectRow.createCell(2);
    sectionCell2.setCellValue("Id");
    sectionCell2.setCellStyle(headerStyle);

    for (String subject : subjectToAssessments.keySet()) {
      Cell cell = subjectRow.createCell(colIndex);
      cell.setCellValue(subject);
      cell.setCellStyle(headerStyle);
      colIndex += subjectToAssessments.get(subject).size();
    }

    Row assessmentRow = sheet.createRow(3);
    colIndex = 3;
    Cell sectionCell3 = assessmentRow.createCell(0);
    sectionCell3.setCellValue("Name");
    sectionCell3.setCellStyle(headerStyle);
    Cell sectionCell4 = assessmentRow.createCell(1);
    sectionCell4.setCellValue("RollNo");
    sectionCell4.setCellStyle(headerStyle);
    Cell sectionCell5 = assessmentRow.createCell(2);
    sectionCell5.setCellValue("Id");
    sectionCell5.setCellStyle(headerStyle);
    for (Map.Entry<String, List<String>> entry : subjectToAssessments.entrySet()) {
      for (String assessment : entry.getValue()) {
        Cell cell = assessmentRow.createCell(colIndex++);
        cell.setCellValue(assessment);
        cell.setCellStyle(headerStyle);
      }
    }

    int rowNum = 4;
    for (var student : firstResponse) {
      Row studentRow = sheet.createRow(rowNum++);
      studentRow.createCell(0).setCellValue(student.name());
      studentRow
          .createCell(1)
          .setCellValue(
              student.classRollNumber() == null ? student.studentId() : student.classRollNumber());
      studentRow.createCell(2).setCellValue(student.studentId());

      int colIdx = 3;

      Map<String, Map<String, Double>> subjectMarks =
          student.subjects().stream()
              .filter(Objects::nonNull)
              .filter(s -> s.get("name") != null)
              .collect(
                  Collectors.toMap(
                      s -> (String) s.get("name"),
                      s ->
                          s.entrySet().stream()
                              .filter(e -> e.getKey() != null && e.getValue() != null)
                              .filter(e -> !e.getKey().equals("name"))
                              .collect(
                                  Collectors.toMap(
                                      Map.Entry::getKey,
                                      e ->
                                          e.getValue() instanceof Number
                                              ? ((Number) e.getValue()).doubleValue()
                                              : 0.0)),
                      (existing, replacement) -> existing));

      for (String subject : subjectToAssessments.keySet()) {
        Map<String, Double> marks = subjectMarks.getOrDefault(subject, new HashMap<>());

        for (String assessment : subjectToAssessments.get(subject)) {
          Double score = marks.getOrDefault(assessment, 0.0);
          Cell cell = studentRow.createCell(colIdx++);
          cell.setCellValue(score);
          cell.setCellStyle(borderStyle);
        }
      }
    }

    for (int i = 0; i < colIndex; i++) {
      sheet.autoSizeColumn(i);
    }

    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    workbook.write(outputStream);
    workbook.close();

    HttpHeaders headersResponse = new HttpHeaders();
    headersResponse.setContentDisposition(
        ContentDisposition.attachment().filename("student_marks.xlsx").build());
    headersResponse.setContentType(MediaType.APPLICATION_OCTET_STREAM);

    return new ResponseEntity<>(outputStream.toByteArray(), headersResponse, HttpStatus.OK);
  }
}
