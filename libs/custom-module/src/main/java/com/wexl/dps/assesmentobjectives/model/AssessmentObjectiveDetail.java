package com.wexl.dps.assesmentobjectives.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@RequiredArgsConstructor
@Builder
@Entity
@AllArgsConstructor
@Table(name = "assessment_objective_details")
public class AssessmentObjectiveDetail extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(columnDefinition = "TEXT")
  private String text;

  @Column(name = "published_at")
  private LocalDateTime publishedAt;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "assessment_objective_id")
  private AssessmentObjective assessmentObjective;
}
