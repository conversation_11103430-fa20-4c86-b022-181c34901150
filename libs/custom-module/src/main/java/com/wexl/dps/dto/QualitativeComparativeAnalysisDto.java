package com.wexl.dps.dto;

import com.wexl.retail.offlinetest.dto.ReportCardDto;
import java.util.List;
import lombok.Builder;

public record QualitativeComparativeAnalysisDto() {

  @Builder
  public record Response(Header header, Body body) {}

  @Builder
  public record Header(
      String schoolName,
      String schoolLogo,
      String schoolWaterMark,
      String academicYear,
      String testName,
      String testType,
      String admissionNumber,
      String address,
      String isoData,
      Long studentId,
      String campus,
      String curriculum,
      String studentName) {}

  @Builder
  public record Body(
      List<SubjectPercentage> subjectPercentages,
      String name,
      String rollNumber,
      String className,
      String mothersName,
      String fathersName,
      String dateOfBirth,
      String gradingScale,
      String orgSlug,
      String gradeSlug,
      String testType,
      ReportCardDto.FirstTable firstTable,
      ReportCardDto.SecondTable secondTable,
      ReportCardDto.ThirdTable thirdTable,
      ReportCardDto.Attendance attendance,
      List<String> testNames,
      Colors colors) {}

  @Builder
  public record SubjectPercentage(
      Long seqNo,
      String subjectName,
      Integer actualPercentage,
      Double pt1ActualPercentage,
      Double pt2ActualPercentage,
      Double pt3ActualPercentage,
      Double pt4ActualPercentage) {}

  @Builder
  public record Marks(
      Long sno,
      String subject,
      Double pt1,
      Double pt2,
      Long seqNo,
      Double pt1Weightage,
      Double pt2Weightage,
      Double pt1Percentage,
      Double pt2Percentage,
      Double pt3Percentage,
      Double pt4Percentage) {}

  @Builder
  public record Colors(String gray, String black, String pink, String green) {}
}
