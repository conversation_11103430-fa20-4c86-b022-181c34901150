package com.wexl.dps.reportcard;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.repository.ReportCardTemplateRepository;
import com.wexl.retail.offlinetest.service.OfflineTestReportService;
import com.wexl.retail.offlinetest.service.ReportCardTemplateService;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.util.ValidationUtils;
import com.wexl.retail.v2.dto.GenericAsnwerSheetDto;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

@Component
@Slf4j
@RequiredArgsConstructor
public class GenericAnswerSheet {

  private final ValidationUtils validationUtils;
  private final ScheduleTestRepository scheduleTestRepository;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final TestDefinitionRepository testDefinitionRepository;
  private final TemplateEngine templateEngine;
  private final OfflineTestReportService offlineTestReportService;
  private final ReportCardTemplateRepository reportCardTemplateRepository;
  private final ReportCardTemplateService reportCardTemplateService;

  public byte[] getAnswerSheet(
      String orgSlug, Long testScheduleId, GenericAsnwerSheetDto.Request request) {
    return buildBody(orgSlug, testScheduleId, request);
  }

  private byte[] buildBody(
      String orgSlug, Long testScheduleId, GenericAsnwerSheetDto.Request request) {
    var org = validationUtils.isOrgValid(orgSlug);
    var scheduleTest =
        scheduleTestRepository
            .findById(testScheduleId)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "Invalid Test Schedule ID"));
    var scheduleTestStudents = scheduleTestStudentRepository.findByScheduleTest(scheduleTest);
    List<String> studentAuthIds =
        scheduleTestStudents.stream()
            .map(student -> student.getStudent().getAuthUserId())
            .collect(Collectors.toList());

    List<byte[]> pdfResponses = new ArrayList<>();
    for (String authId : studentAuthIds) {
      User user = validationUtils.isValidUser(authId);
      Student student = user.getStudentInfo();

      var testDefinition = scheduleTest.getTestDefinition();
      if (testDefinition.getReportCardTemplateId() == null) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Please Set Template ID");
      }

      var reportCardTemplate =
          reportCardTemplateRepository
              .findById(testDefinition.getReportCardTemplateId())
              .orElseThrow(
                  () ->
                      new ApiException(
                          InternalErrorCodes.INVALID_REQUEST, "Report Card Template not found"));

      ReportCardDto.Body data =
          ReportCardDto.Body.builder()
              .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
              .rollNumber(student.getRollNumber())
              .answerSheet(buildAnswerSheet(request))
              .build();

      var context = new Context(Locale.getDefault(), Map.of("model", data));
      String foTemplate =
          templateEngine.process("report-card/dps/" + reportCardTemplate.getConfig(), context);

      pdfResponses.add(offlineTestReportService.generatePdf(foTemplate));
    }
    return reportCardTemplateService.mergeReportCards(pdfResponses);
  }

  private List<ReportCardDto.AnswerSheet> buildAnswerSheet(GenericAsnwerSheetDto.Request request) {
    return request.data().stream()
        .map(
            answer ->
                ReportCardDto.AnswerSheet.builder()
                    .answerMarks(answer.marks())
                    .startingSequenceNo(answer.startingSequenceNo())
                    .endingSequenceNo(answer.endingSequenceNo())
                    .build())
        .toList();
  }
}
