package com.wexl.dps.dto;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;

public record ErpIntegrationDto() {

  @Builder
  public record DpsTeacherResponse(
      int id,
      String employeeName,
      @NotNull String employeeCode,
      String email,
      String phone,
      String gender,
      String branchCode,
      String branchName,
      String curriculum,
      String grade,
      String section,
      String orgSlug) {}

  @Builder
  public record DpsStudentResponse(
      int id,
      String studentName,
      @NotNull String studentCode,
      String email,
      String phone,
      String branchCode,
      String branchName,
      Integer rollNumber,
      String curriculum,
      String grade,
      String section,
      String gender,
      String orgSlug,
      String sectionUuid,
      String setionName,
      String gradeSlug,
      String fatherName,
      String motherName,
      String dob,
      Boolean feeDefaulter) {}

  @Builder
  public record DpsParentResponse(
      int id,
      String parentName,
      @NotNull String parentCode,
      String email,
      String phone,
      String branchCode,
      String branchName,
      String studentCode1,
      String studentCode2,
      String studentCode3) {}

  @Builder
  public record DpsEntityChange(
      String changeType,
      String commitId,
      Integer id,
      String dateTime,
      String employeeCode,
      String type,
      DpsTeacherResponse teacherResponse,
      DpsStudentResponse studentResponse,
      DpsParentResponse parentResponse) {}

  @Builder
  public record DpsEntityChangeResponse(List<DpsEntityChange> dpsEntityChangeList) {}
}
