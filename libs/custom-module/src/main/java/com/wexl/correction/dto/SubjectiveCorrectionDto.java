package com.wexl.correction.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record SubjectiveCorrectionDto() {

  @Builder
  public record SubjectiveCorrectionSubmitAnswerResponse(@JsonProperty("exam_id") Long examId) {}

  @Builder
  public record SubjectiveCorrectionSubmitAnswerRequest(
      String answerSheetPath, String rollNumber, List<Answer> answers, boolean force) {}

  @Builder
  public record Answer(String uuid, String answer, String type, String aiAnalysis, Float aiMarks) {}

  @Builder
  public record Analysis(String grammar, String spelling, String contextual) {}
}
