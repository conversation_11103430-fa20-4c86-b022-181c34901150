package com.wexl.mgcv.feeslite.model;

import com.wexl.retail.model.Student;
import jakarta.persistence.*;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "mgcv_fees_lite")
public class MgcvFeesLite {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "student_id")
  private Student student;

  @JoinColumn(name = "term1_status")
  private String term1Status;

  @JoinColumn(name = "term2_status")
  private String term2Status;

  @JoinColumn(name = "term1_paid_date")
  private Timestamp term1PaidDate;

  @JoinColumn(name = "term2_paid_date")
  private Timestamp term2PaidDate;
}
