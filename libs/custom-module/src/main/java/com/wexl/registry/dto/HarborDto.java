package com.wexl.registry.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;
import java.util.List;

public record HarborDto() {
  public record RetagRequest(String image, String version) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record Repository(
      @JsonProperty("name") String name,
      @JsonProperty("artifact_count") Integer artifactCount,
      @JsonProperty("creation_time") LocalDateTime creationTime,
      @JsonProperty("update_time") LocalDateTime updateTime) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record Artifact(
      @JsonProperty("id") Long id,
      @JsonProperty("digest") String digest,
      @JsonProperty("repository_name") String repositoryName,
      @JsonProperty("push_time") LocalDateTime pushTime,
      @JsonProperty("tags") List<Tag> tags) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record Tag(
      @JsonProperty("name") String name, @JsonProperty("push_time") LocalDateTime pushTime) {}
}
