package com.wexl.erp.leaves.model;

import com.wexl.retail.model.Student;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "student_leave_requests")
public class StudentLeaveRequest {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "student_id")
  private Student student;

  @Column(name = "org_slug", nullable = false)
  private String orgSlug;

  @Column(name = "from_date", nullable = false)
  private LocalDateTime fromDate;

  @Column(name = "to_date", nullable = false)
  private LocalDateTime toDate;

  @Column(name = "leave_reason", nullable = false, columnDefinition = "TEXT")
  private String leaveReason;

  @Column(name = "attachment_url", columnDefinition = "TEXT")
  private String attachmentUrl;

  @Column(name = "link", columnDefinition = "TEXT")
  private String link;

  @Enumerated(EnumType.STRING)
  @Column(name = "status", nullable = false)
  private LeaveStatus status;

  @Column(name = "applied_date", nullable = false)
  private LocalDateTime appliedDate;

  @Column(name = "reviewed_by")
  private String reviewedBy;

  @Column(name = "reviewed_on")
  private LocalDateTime reviewedOn;
}
