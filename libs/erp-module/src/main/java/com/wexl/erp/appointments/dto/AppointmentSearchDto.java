package com.wexl.erp.appointments.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.globalprofile.model.AppTemplate;
import java.util.List;
import lombok.Builder;

public class AppointmentSearchDto {
  public record Request(@JsonProperty("searchKey") String searchKey) {}

  @Builder
  public record StaffResponse(String authUserId, String fullName, List<StaffRole> roles) {}

  @Builder
  public record StaffRole(String roleName, AppTemplate role) {}

  @Builder
  public record TeacherResponse(String name, Long userId, String authUserId) {}

  @Builder
  public record Response(
      List<StaffResponse> staffResponse, List<TeacherResponse> teacherResponse) {}
}
