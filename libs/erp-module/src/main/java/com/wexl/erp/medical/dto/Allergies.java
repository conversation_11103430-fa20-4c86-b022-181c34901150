package com.wexl.erp.medical.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Allergies {
  private Boolean bronchialAsthma;
  private Boolean headaches;
  private Boolean vomitings;
  private Boolean urticaria;
  private Boolean convulsions;
  private Boolean tonsillitis;
  private Boolean anaemia;
  private Boolean sinusitis;
  private Boolean bronchitis;
  private Boolean tuberculosis;
  private Boolean pneumonia;
  private Boolean epilepsyAnyOther;
  private String anyOther;
}
