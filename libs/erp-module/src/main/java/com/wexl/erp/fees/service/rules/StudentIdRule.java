package com.wexl.erp.fees.service.rules;

import com.wexl.retail.model.Student;
import com.wexl.retail.repository.StudentRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class StudentIdRule implements Rule {

  private final StudentRepository studentRepository;

  @Override
  public boolean isApplicable(RuleDto.StudentFeeDto student, RuleDto.RuleParam ruleParam) {
    // Check if student.studentId() is in ruleParam.paramValues(), check ignore case
    return ruleParam.paramValues().stream()
        .anyMatch(value -> value.equalsIgnoreCase(student.id().toString()));
  }

  @Override
  public boolean supports(RuleDto.RuleParam ruleParam) {
    return RuleParamType.STUDENT_ID.equals(ruleParam.paramType());
  }

  @Override
  public List<Student> getStudents(RuleDto.RuleParam ruleParam) {
    List<Long> studentIds = ruleParam.paramValues().stream().map(Long::valueOf).toList();
    return studentRepository.findStudentListById(studentIds);
  }
}
