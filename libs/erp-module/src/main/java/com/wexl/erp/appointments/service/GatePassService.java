package com.wexl.erp.appointments.service;

import com.wexl.erp.appointments.dto.GatePassDto;
import com.wexl.erp.appointments.model.Appointment;
import com.wexl.erp.appointments.model.AppointmentStatus;
import com.wexl.erp.appointments.model.AppointmentType;
import com.wexl.erp.appointments.repository.AppointmentRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.guardian.model.Guardian;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.dto.NotificationType;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.offlinetest.service.OfflineTestReportService;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.telegram.service.UserService;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

@Service
@Slf4j
@RequiredArgsConstructor
public class GatePassService {

  private final AppointmentRepository appointmentRepository;
  private final AppointmentService appointmentService;
  private final NotificationsService notificationsService;
  private final DateTimeUtil dateTimeUtil;
  private final UserRepository userRepository;
  private final GuardianService guardianService;
  private final UserService userService;
  private final EventNotificationService eventNotificationService;
  private final OrganizationRepository organizationRepository;
  private final OfflineTestReportService offlineTestReportService;

  @Qualifier("stringBasedSpringTemplateEngine")
  private final SpringTemplateEngine templateEngine;

  private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");

  private GatePassDto.Response buildGatePassResponse(Appointment appointment) {
    return GatePassDto.Response.builder()
        .gatePassId(appointment.getId())
        .studentId(appointment.getStudent().getId())
        .guardianName(
            appointment.getGuardian() != null
                ? guardianService.getGuardianName(appointment.getGuardian())
                : null)
        .guardianId(appointment.getGuardian().getId())
        .studentName(userService.getNameByUserInfo(appointment.getStudent().getUserInfo()))
        .gradeName(appointment.getStudent().getSection().getGradeName())
        .gradeSlug(appointment.getStudent().getSection().getGradeSlug())
        .studentSection(appointment.getStudent().getSection().getName())
        .recipientName(appointment.getRecipientName())
        .role(appointment.getRole())
        .gatePassDate(DateTimeUtil.convertIso8601ToEpoch(appointment.getAppointmentDate()))
        .gatePassReason(appointment.getAppointmentReason())
        .status(appointment.getStatus())
        .appliedDate(DateTimeUtil.convertIso8601ToEpoch(appointment.getAppliedDate()))
        .reviewedBy(appointment.getReviewedBy())
        .reviewedOn(
            appointment.getReviewedOn() != null
                ? DateTimeUtil.convertIso8601ToEpoch(appointment.getReviewedOn())
                : null)
        .pickupPersonRelation(appointment.getRelationType())
        .gatePassType(String.valueOf(appointment.getType()))
        .pickupPersonName(appointment.getGuardianName())
        .pickupPersonMobile(appointment.getMobileNumber())
        .build();
  }

  private Appointment buildGatePassRequest(GatePassDto.Request request) {
    return Appointment.builder()
        .appointmentDate(dateTimeUtil.convertEpochToIso8601(request.gatePassDate()))
        .appointmentReason(request.gatePassReason())
        .recipientName(request.recipientName())
        .status(AppointmentStatus.PENDING)
        .appliedDate(LocalDateTime.now())
        .type(AppointmentType.GATEPASS)
        .role(request.role())
        .mobileNumber(request.pickupPersonMobile())
        .guardianName(request.pickupPersonName())
        .relationType(
            request.pickupPersonRelation() != null ? (request.pickupPersonRelation()) : null)
        .build();
  }

  @Transactional
  public GatePassDto.Response applyForGatePass(
      String orgSlug, String guardianAuthId, GatePassDto.Request request) {

    User user = guardianService.validateUser(guardianAuthId);
    Student student = user.getStudentInfo();
    if (student == null) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "Student not found");
    }

    Guardian guardian = appointmentService.getValidatedGuardianForStudent(student);

    Appointment gatePass = buildGatePassRequest(request);
    gatePass.setStudent(student);
    gatePass.setGuardian(guardian);
    gatePass.setOrgSlug(orgSlug);
    Appointment savedGatePass = appointmentRepository.save(gatePass);

    String orgAdminForOrganization =
        userRepository.getOrgAdminForOrganization(orgSlug).getAuthUserId();

    LocalDateTime appliedDate = LocalDateTime.now();

    String notificationMessage =
        guardian.getFirstName()
            + " "
            + guardian.getLastName()
            + " has requested a gate pass for "
            + userService.getNameByUserInfo(student.getUserInfo())
            + " on "
            + gatePass.getAppointmentDate().format(formatter)
            + ". Reason: "
            + gatePass.getAppointmentReason()
            + ". Applied on: "
            + appliedDate.format(formatter);

    if (request.teacherAuthId() != null && !request.teacherAuthId().isEmpty()) {
      NotificationDto.NotificationRequest notificationRequest =
          NotificationDto.NotificationRequest.builder()
              .title("New Gate Pass Request")
              .message(notificationMessage)
              .notificationType(NotificationType.GATEPASS_REQUEST)
              .userAuthId(request.teacherAuthId())
              .build();
      List<String> authIds = new ArrayList<>();
      authIds.add(request.teacherAuthId());
      authIds.add(orgAdminForOrganization);
      for (String authId : authIds) {
        notificationsService.createNotificationByTeacher(
            orgSlug,
            notificationRequest,
            authId,
            false,
            dateTimeUtil.convertLocalTimeToEpoch(LocalTime.from(appliedDate)));
        eventNotificationService.sendPushNotificationForUser(
            authId,
            notificationRequest.message(),
            orgSlug,
            NotificationType.GATEPASS_REQUEST,
            "Gate Pass Notification");
      }
    }

    log.info("Gate pass request created with ID: {}", savedGatePass.getId());
    return buildGatePassResponse(savedGatePass);
  }

  @Transactional
  public void approveOrRejectGatePassRequest(
      String orgSlug, String teacherAuthId, GatePassDto.ApprovalRequest approvalRequest) {

    Appointment gatePass = appointmentService.getAppointmentById(approvalRequest.gatePassId());

    if (gatePass.getType() != AppointmentType.GATEPASS) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid gate pass ID");
    }

    gatePass.setStatus(approvalRequest.status());
    gatePass.setReviewedBy(teacherAuthId);
    gatePass.setReviewedOn(LocalDateTime.now());

    appointmentRepository.save(gatePass);

    String message =
        "Your gate pass request for "
            + userService.getNameByUserInfo(gatePass.getStudent().getUserInfo())
            + " on "
            + gatePass.getAppointmentDate().format(formatter)
            + " has been "
            + approvalRequest.status().toString().toLowerCase();

    NotificationDto.NotificationRequest.NotificationRequestBuilder notificationBuilder =
        NotificationDto.NotificationRequest.builder()
            .title("Gate Pass Request " + approvalRequest.status())
            .userAuthId(gatePass.getStudent().getUserInfo().getAuthUserId())
            .message(message)
            .studentIds(List.of(gatePass.getStudent().getId()))
            .notificationType(
                approvalRequest.status() == AppointmentStatus.APPROVED
                    ? NotificationType.GATEPASS_APPROVED
                    : NotificationType.GATEPASS_DISAPPROVED);

    NotificationDto.NotificationRequest notificationRequest = notificationBuilder.build();

    notificationsService.createNotificationByUser(
        orgSlug, notificationRequest, gatePass.getStudent().getUserInfo().getAuthUserId());
    eventNotificationService.sendPushNotificationForUser(
        gatePass.getStudent().getUserInfo().getAuthUserId(),
        notificationRequest.message(),
        orgSlug,
        approvalRequest.status() == AppointmentStatus.APPROVED
            ? NotificationType.GATEPASS_APPROVED
            : NotificationType.GATEPASS_DISAPPROVED,
        "Gate Pass Notification");
  }

  public byte[] generateGatePassPdfBytes(Long gatePassId) {
    Appointment gatePass = appointmentService.getAppointmentById(gatePassId);

    try {
      Map<String, Object> model = buildGatePassModel(gatePass, gatePass.getReviewedBy());
      var context = new Context(Locale.getDefault(), Map.of("model", model));
      String foTemplate = templateEngine.process("report-card/dps/gate-pass.xml", context);

      return offlineTestReportService.generatePdf(foTemplate);

    } catch (Exception e) {
      log.error("Error generating gate pass PDF for ID: {}", gatePass.getId(), e);
      throw new ApiException(
          InternalErrorCodes.SERVER_ERROR,
          "Failed to generate gate pass PDF: " + e.getMessage(),
          e);
    }
  }

  private Map<String, Object> buildGatePassModel(Appointment gatePass, String teacherAuthId) {
    Map<String, Object> model = new HashMap<>();
    User teacheruser = userRepository.getUserByAuthUserId(teacherAuthId);
    Organization organization = organizationRepository.findBySlug(gatePass.getOrgSlug());
    model.put("schoolLogo", organization != null ? organization.getLogo() : null);
    model.put("schoolName", organization != null ? organization.getName() : "School Name");
    model.put("gatePassNumber", +gatePass.getId());
    model.put(
        "gatePassDate",
        gatePass.getAppointmentDate().format(DateTimeFormatter.ofPattern("dd-MM-yyyy")));
    model.put("studentName", userService.getNameByUserInfo(gatePass.getStudent().getUserInfo()));
    model.put("rollNumber", gatePass.getStudent().getRollNumber());
    model.put("className", gatePass.getStudent().getSection().getGradeName());
    model.put("sectionName", gatePass.getStudent().getSection().getName());
    model.put("reason", gatePass.getAppointmentReason());
    model.put("type", gatePass.getType().toString());
    model.put("pickupPersonName", gatePass.getGuardianName());
    model.put("pickupPersonMobile", gatePass.getMobileNumber());
    model.put("pickupPersonRelation", gatePass.getRelationType().toString());
    model.put(
        "appliedDate", gatePass.getAppliedDate().format(DateTimeFormatter.ofPattern("dd-MM-yyyy")));
    model.put("status", gatePass.getStatus().toString());
    model.put("approvedBy", userService.getNameByUserInfo(teacheruser));
    model.put(
        "approvalDate",
        LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm")));

    return model;
  }
}
