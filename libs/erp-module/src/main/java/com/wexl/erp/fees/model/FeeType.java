package com.wexl.erp.fees.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import jakarta.persistence.*;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "fee_types")
public class FeeType extends Model {
  @Id @GeneratedValue private UUID id;

  private String name;

  @Column(name = "org_slug", nullable = false)
  private String orgSlug;

  private String code;
  private String description;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "createdBy")
  private User createdBy;
}
