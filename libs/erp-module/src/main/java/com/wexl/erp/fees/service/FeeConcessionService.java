package com.wexl.erp.fees.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.Concession;
import com.wexl.erp.fees.model.ConcessionHead;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.repository.ConcessionHeadRepository;
import com.wexl.erp.fees.repository.ConcessionRepository;
import com.wexl.erp.fees.repository.FeeHeadRepository;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FeeConcessionService {

  private final ConcessionRepository concessionRepository;
  private final ConcessionHeadRepository concessionHeadRepository;
  private final FeeHeadRepository feeHeadRepository;
  private final AuthService authService;

  public void saveConcessions(String orgSlug, FeeDto.ConcessionRequest request) {
    if (request.concessionType() == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Concession.TypeRequired");
    }
    if (request.value() == null || request.value() <= 0) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Concession.ValueRequired");
    }
    concessionRepository.save(
        Concession.builder()
            .orgSlug(orgSlug)
            .type(request.concessionType())
            .value(request.value())
            .description(request.description())
            .createdBy(authService.getUserDetails())
            .build());
  }

  public List<FeeDto.ConcessionResponse> getConcessions(String orgSlug) {
    List<FeeDto.ConcessionResponse> responses = new ArrayList<>();
    var concessions = concessionRepository.findAllByOrgSlug(orgSlug);
    if (concessions.isEmpty()) {
      return responses;
    }
    concessions.forEach(
        concession ->
            responses.add(
                FeeDto.ConcessionResponse.builder()
                    .id(concession.getId())
                    .concessionType(concession.getType())
                    .value(concession.getValue())
                    .description(concession.getDescription())
                    .createdOn(convertIso8601ToEpoch(concession.getCreatedAt().toLocalDateTime()))
                    .isPublished(concession.getPublishedAt() != null ? Boolean.TRUE : Boolean.FALSE)
                    .createdBy(
                        concession.getCreatedBy().getFirstName()
                            + " "
                            + concession.getCreatedBy().getLastName())
                    .build()));
    return responses;
  }

  public void updateConcessionById(
      String orgSlug, String concessionId, FeeDto.ConcessionRequest request) {
    var concession = getConcessionById(concessionId, orgSlug);
    concession.setType(request.concessionType());
    concession.setValue(request.value());
    concession.setDescription(request.description());
    concessionRepository.save(concession);
  }

  public void deleteConcessionById(String orgSlug, String concessionId) {
    var concession = getConcessionById(concessionId, orgSlug);
    concessionRepository.delete(concession);
  }

  public void saveConcessionHeads(
      String orgSlug, FeeDto.ConcessionHeadRequest request, String concessionId) {
    var concession = getConcessionById(concessionId, orgSlug);
    if (request.feeHeadId() == null || request.studentId() == null) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.ConcessionHead.InvalidRequest");
    }
    var feeHead = getFeeHeadById(request.feeHeadId(), orgSlug);
    request
        .studentId()
        .forEach(
            student -> {
              var concessionHead =
                  ConcessionHead.builder()
                      .feeType(feeHead.getFeeType())
                      .concession(concession)
                      .createdBy(authService.getUserDetails())
                      .student(feeHead.getStudent())
                      .isApproved(Boolean.FALSE)
                      .feeHead(feeHead)
                      .build();
              concessionHeadRepository.save(concessionHead);
              feeHead.setBalanceAmount(feeHead.getBalanceAmount() - concession.getValue());
              feeHead.setDiscountAmount(concession.getValue());
              feeHeadRepository.save(feeHead);
            });
  }

  private Concession getConcessionById(String concessionId, String orgSlug) {
    var concession =
        concessionRepository.findByIdAndOrgSlug(UUID.fromString(concessionId), orgSlug);
    if (concession.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Concession.NotFound");
    }
    return concession.get();
  }

  private FeeHead getFeeHeadById(String feeHeadId, String orgSlug) {
    var feeHead = feeHeadRepository.findByIdAndOrgSlug(UUID.fromString(feeHeadId), orgSlug);
    if (feeHead.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.FeeHead.NotFound");
    }
    return feeHead.get();
  }

  public List<FeeDto.ConcessionHeadResponse> getConcessionHeads(
      String orgSlug, String concessionId) {
    List<FeeDto.ConcessionHeadResponse> responses = new ArrayList<>();
    var concession = getConcessionById(concessionId, orgSlug);
    var concessionHeads = concessionHeadRepository.findAllByConcession(concession);
    if (concessionHeads.isEmpty()) {
      return responses;
    }
    concessionHeads.forEach(
        concessionHead ->
            responses.add(
                FeeDto.ConcessionHeadResponse.builder()
                    .feeHeadId(concessionHead.getFeeType().getId().toString())
                    .studentId(concessionHead.getStudent().getId())
                    .approvedBy(
                        concessionHead.getApprovedBy().getFirstName()
                            + " "
                            + concessionHead.getApprovedBy().getLastName())
                    .approvedByUserId(concessionHead.getApprovedBy().getId())
                    .isApproved(concessionHead.getIsApproved())
                    .reason(concessionHead.getReason())
                    .reason(concessionHead.getReason())
                    .build()));
    return responses;
  }

  private ConcessionHead getConcessionHeadById(
      String concessionHeadId, String orgSlug, Concession concession) {
    var concessionHead = concessionHeadRepository.findById(UUID.fromString(concessionHeadId));
    if (concessionHead.isEmpty() || !concessionHead.get().getConcession().equals(concession)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ConcessionHead.NotFound");
    }
    return concessionHead.get();
  }

  public void deleteConcessionHeadById(
      String orgSlug, String concessionId, String concessionHeadId) {
    var concession = getConcessionById(concessionId, orgSlug);
    var concessionHead = getConcessionHeadById(concessionHeadId, orgSlug, concession);
    if (Boolean.TRUE.equals(concessionHead.getIsApproved())) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.ConcessionHead.AlreadyApproved");
    }
    concessionHeadRepository.delete(concessionHead);
  }

  public void publishConcession(String orgSlug, String concessionId) {
    var concession = getConcessionById(concessionId, orgSlug);
    concession.setPublishedAt(LocalDateTime.now());
    concessionRepository.save(concession);
  }
}
