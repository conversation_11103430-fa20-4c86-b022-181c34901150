package com.wexl.erp.leaves.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.erp.leaves.model.LeaveStatus;
import lombok.Builder;

public class StudentLeaveRequestDto {

  public record Request(
      @JsonProperty("from_date") Long fromDate,
      @JsonProperty("to_date") Long toDate,
      @JsonProperty("leave_reason") String leaveReason,
      @JsonProperty("attachment_url") String attachmentUrl,
      @JsonProperty("link") String link) {}

  @Builder
  public record Response(
      Long leaveId,
      String studentName,
      String studentSection,
      String gradeName,
      String gradeSlug,
      Long studentId,
      Long fromDate,
      Long toDate,
      String leaveReason,
      String attachmentUrl,
      String link,
      LeaveStatus status,
      Long appliedDate,
      String reviewedBy,
      Long reviewedOn) {}

  public record ApprovalRequest(@JsonProperty("leave_status") LeaveStatus status) {}
}
