package com.wexl.erp.fees.service.rules;

import com.wexl.retail.model.Student;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.dto.response.SectionEntityDto;
import com.wexl.retail.section.service.SectionService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GradeRule implements Rule {

  private final SectionService sectionService;
  private final StudentRepository studentRepository;

  @Override
  public boolean isApplicable(RuleDto.StudentFeeDto student, RuleDto.RuleParam ruleParam) {
    // check if student.gradeSlug() is in ruleParam.paramValues(), check ignore case
    return ruleParam.paramValues().stream()
        .anyMatch(value -> value.equalsIgnoreCase(student.gradeSlug()));
  }

  @Override
  public boolean supports(RuleDto.RuleParam ruleParam) {
    return RuleParamType.GRADE.equals(ruleParam.paramType());
  }

  public List<Student> getStudents(RuleDto.RuleParam ruleParam) {
    var sections =
        ruleParam.paramValues().stream()
            .flatMap(
                grade -> sectionService.getSectionsByGrade(ruleParam.orgSlug(), grade).stream())
            .map(SectionEntityDto.Response::uuid)
            .distinct()
            .toList();
    return studentRepository.getStudentsBySectionUuidsAndOrgSlug(sections, ruleParam.orgSlug());
  }
}
