package com.wexl.erp.fees.service;

import com.wexl.erp.fees.service.rules.Rule;
import com.wexl.erp.fees.service.rules.RuleDto;
import com.wexl.retail.model.Student;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class FeeRuleEngine {
  private final List<Rule> rules;

  public List<UUID> evaluateRules(
      RuleDto.StudentFeeDto student, List<RuleDto.RuleParam> ruleParams) {
    List<UUID> eligibleFeeMasters = new ArrayList<>();
    for (RuleDto.RuleParam ruleParam : ruleParams) {
      // Find the first applicable rule for the current rule parameter
      List<Rule> list =
          rules.stream()
              .filter(rule -> rule.supports(ruleParam))
              .filter(rule -> rule.isApplicable(student, ruleParam))
              .toList();
      if (!list.isEmpty()) {
        eligibleFeeMasters.add(ruleParam.feeMasterId());
      }
    }
    return eligibleFeeMasters;
  }

  public List<Student> getStudentsForRule(RuleDto.RuleParam ruleParam) {
    List<Rule> applicableRules = rules.stream().filter(rule -> rule.supports(ruleParam)).toList();

    if (applicableRules.isEmpty()) {
      return List.of(); // No applicable rules found
    }

    return applicableRules.stream()
        .map(rule -> rule.getStudents(ruleParam))
        .flatMap(List::stream)
        .toList();
  }
}
