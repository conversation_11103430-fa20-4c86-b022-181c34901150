package com.wexl.retail.test.schedule.repository;

import com.wexl.retail.test.schedule.domain.TestScheduleStudentAnswer;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface StudentScheduleTestAnswerRepository
    extends JpaRepository<TestScheduleStudentAnswer, UUID> {
  Optional<TestScheduleStudentAnswer> findByUuidAndUserNameAndTssUuid(
      UUID fromString, String studentId, String tssUuid);

  List<TestScheduleStudentAnswer> findAllByTssUuidAndUserName(String tssUuid, String studentId);

  List<TestScheduleStudentAnswer> findAllByTssUuid(String fromString);
}
