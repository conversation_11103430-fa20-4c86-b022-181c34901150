package com.wexl.retail.mlp.service;

import com.wexl.retail.util.CryptoUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class VideoUrlEncryptor {
  private static final String URL_PREFIX = "https://";
  private final CryptoUtils cryptoUtils;

  public String convertEncrypted(String url) {
    if (!isPlainText(url)) {
      return url;
    }
    return cryptoUtils.encrypt(url);
  }

  public String convertPlain(String url) {
    if (isPlainText(url)) {
      return url;
    }
    return cryptoUtils.decrypt(url);
  }

  private boolean isPlainText(String url) {
    return StringUtils.isNotBlank(url) && url.startsWith(URL_PREFIX);
  }
}
