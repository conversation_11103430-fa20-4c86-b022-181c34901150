package com.wexl.retail.msg91.service;

import static com.wexl.retail.util.Constants.ACCEPTED_ORGS_FOR_TXT_LOCAL;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DelegatingEmailService {
  private final Msg91EmailService msg91EmailService;
  private final BrevoEmailService brevoEmailService;

  public EmailService get(String orgSlug) {

    if (ACCEPTED_ORGS_FOR_TXT_LOCAL.contains(orgSlug)) {
      return brevoEmailService;
    }
    return msg91EmailService;
  }
}
