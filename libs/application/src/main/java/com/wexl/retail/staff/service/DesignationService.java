package com.wexl.retail.staff.service;

import com.wexl.retail.staff.dto.StaffDto;
import com.wexl.retail.staff.model.Department;
import com.wexl.retail.staff.model.Designation;
import com.wexl.retail.staff.repository.DepartmentRepository;
import com.wexl.retail.staff.repository.DesignationRepository;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DesignationService {
  private final DepartmentRepository departmentRepository;
  private final DesignationRepository designationRepository;
  private final StaffService staffService;

  public StaffDto.DesignationResponse createDesignation(
      StaffDto.DesignationRequest request, String orgSlug) {
    Optional<Designation> existingDesignation = designationRepository.findByName(request.name());
    if (existingDesignation.isPresent()) {
      throw new IllegalArgumentException(
          "Designation with name " + request.name() + " already exists");
    }
    Designation designation =
        Designation.builder()
            .name(request.name())
            .description(request.description())
            .orgSlug(orgSlug)
            .is_active(request.active())
            .build();

    if (request.departmentId() != null) {
      Department department =
          departmentRepository
              .findById(request.departmentId())
              .orElseThrow(
                  () ->
                      new IllegalArgumentException(
                          "Department not found with id: " + request.departmentId()));
      designation.setDepartment(department);
    }
    Designation savedDesignation = designationRepository.save(designation);
    return staffService.mapToDesignationResponse(savedDesignation);
  }

  public StaffDto.DesignationResponse getDesignationById(Long id) {
    Designation designation =
        designationRepository
            .findById(id)
            .orElseThrow(
                () -> new IllegalArgumentException("Designation not found with id: " + id));
    return staffService.mapToDesignationResponse(designation);
  }

  public List<StaffDto.DesignationResponse> getAllDesignations() {
    return designationRepository.findAll().stream()
        .map(designation -> staffService.mapToDesignationResponse(designation))
        .collect(Collectors.toList());
  }

  public List<StaffDto.DesignationResponse> getDesignationsByDepartment(Long departmentId) {
    if (!departmentRepository.existsById(departmentId)) {
      throw new IllegalArgumentException("Department not found with id: " + departmentId);
    }
    return designationRepository.findByDepartmentId(departmentId).stream()
        .map(designation -> staffService.mapToDesignationResponse(designation))
        .collect(Collectors.toList());
  }

  public StaffDto.DesignationResponse updateDesignation(
      Long id, StaffDto.DesignationRequest request) {
    Designation designation =
        designationRepository
            .findById(id)
            .orElseThrow(
                () -> new IllegalArgumentException("Designation not found with id: " + id));

    designation.setName(request.name());
    designation.setDescription(request.description());
    designation.setIs_active(request.active());

    if (request.departmentId() != null) {
      Department department =
          departmentRepository
              .findById(request.departmentId())
              .orElseThrow(
                  () ->
                      new IllegalArgumentException(
                          "Department not found with id: " + request.departmentId()));
      designation.setDepartment(department);
    } else {
      designation.setDepartment(null);
    }

    Designation updatedDesignation = designationRepository.save(designation);
    return staffService.mapToDesignationResponse(updatedDesignation);
  }

  public void deleteDesignation(Long id) {
    if (!designationRepository.existsById(id)) {
      throw new IllegalArgumentException("Designation not found with id: " + id);
    }
    designationRepository.deleteById(id);
  }
}
