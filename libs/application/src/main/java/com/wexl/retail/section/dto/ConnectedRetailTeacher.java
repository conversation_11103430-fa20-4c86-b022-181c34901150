package com.wexl.retail.section.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ConnectedRetailTeacher extends ConnectedTeacher {
  String defaultSectionUuid;

  @Builder(builderMethodName = "teacherBuilder")
  public ConnectedRetailTeacher(
      long id,
      String firstName,
      String lastName,
      String emailId,
      String mobileNumber,
      long userId,
      String teacherCode,
      String defaultSectionUuid) {
    super(id, firstName, lastName, emailId, mobileNumber, userId, teacherCode);
    this.defaultSectionUuid = defaultSectionUuid;
  }
}
