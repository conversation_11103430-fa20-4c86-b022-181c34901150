package com.wexl.retail.generic;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class SqlQueryResponse {
  @JsonProperty("Key")
  private String key;

  @JsonProperty("QueryStatement")
  private String query;

  @JsonProperty("Status")
  private boolean status;
}
