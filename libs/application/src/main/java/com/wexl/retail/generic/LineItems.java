package com.wexl.retail.generic;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@Data
@AllArgsConstructor
@NoArgsConstructor
@ConfigurationProperties(prefix = "app.items")
public class LineItems {
  private List<LineItem> config;

  @Data
  public static class LineItem {
    private String url;
    private String title;
    private String gradeSlug;
  }
}
