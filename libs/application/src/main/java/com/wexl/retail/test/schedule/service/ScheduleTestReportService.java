package com.wexl.retail.test.schedule.service;

import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ScheduleTestReportService {

  @Autowired private ScheduleTestRepository scheduleTestRepository;
  @Autowired private final DateTimeUtil dateTimeUtil;

  public List<GenericMetricResponse> getScheduledTestsByInstitute(
      List<String> org, List<String> subject, List<String> gradeList, Integer timePeriod) {
    LocalDate fromDate = LocalDate.now().minusWeeks(timePeriod);

    var scheduleTestReportList =
        scheduleTestRepository.getScheduledTestsByInstitute(
            org, fromDate.toString(), subject, gradeList);
    return scheduleTestReportList.stream()
        .map(
            scheduleTestReport ->
                GenericMetricResponse.builder()
                    .data(data(scheduleTestReport))
                    .summary(summary(org, subject, gradeList))
                    .date(DateTimeUtil.convertIso8601ToEpoch(scheduleTestReport.getScheduledDate()))
                    .build())
        .sorted(Comparator.comparing(GenericMetricResponse::getDate))
        .toList();
  }

  private Map<String, Object> summary(
      List<String> org, List<String> subject, List<String> gradeList) {
    Map<String, Object> map = new HashMap<>();
    map.put("org", org);
    map.put("grade_list", gradeList);
    map.put("subject_list", subject);
    return map;
  }

  private Map<String, Object> data(ScheduledTestData scheduleTestReport) {
    Map<String, Object> map = new HashMap<>();
    map.put("institution", scheduleTestReport.getInstitution());
    map.put("teacher_name", scheduleTestReport.getTeacherName());
    map.put("subject_name", scheduleTestReport.getSubject());
    map.put("test_name", scheduleTestReport.getTestName());
    map.put("grade", scheduleTestReport.getGradeSlug());
    map.put(
        "total_marks",
        (scheduleTestReport.getLeastMarksScored() == null
            ? "0"
            : scheduleTestReport.getTotalMarks().toString()));
    map.put(
        "highest_marks_scored",
        (scheduleTestReport.getLeastMarksScored() == null
            ? "0"
            : scheduleTestReport.getHighestMarksScored().toString()));
    map.put(
        "least_marks_scored",
        (scheduleTestReport.getLeastMarksScored() == null
            ? "0"
            : scheduleTestReport.getLeastMarksScored().toString()));
    map.put("attempted", scheduleTestReport.getAttempted().toString());
    map.put("notAttempted", scheduleTestReport.getNotAttempted().toString());
    map.put("assigned", scheduleTestReport.getAssigned().toString());
    return map;
  }
}
