package com.wexl.retail.student.exam.revision.controller;

import static com.wexl.retail.util.Constants.AUTHORIZATION_HEADER;

import com.wexl.retail.content.model.QuestionResponse;
import com.wexl.retail.student.exam.revision.dto.RevisionQuestionsCount;
import com.wexl.retail.student.exam.revision.service.ExamRevisionService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
public class ExamRevisionController {

  private final ExamRevisionService examRevisionService;

  @GetMapping("/orgs/{orgSlug}/students/{authUserId}/revision")
  public QuestionResponse getRevisionQuestions(
      @RequestParam String subjectSlug,
      @RequestParam String chapterSlug,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {

    return examRevisionService.getRevisionQuestions(chapterSlug, subjectSlug, bearerToken);
  }

  @GetMapping("/orgs/{orgSlug}/students/{authUserId}/revision-questions-count")
  public List<RevisionQuestionsCount> getRevisionQuestionsCount(@RequestParam String subjectSlug) {

    return examRevisionService.getRevisionQuestionsCountByChapter(subjectSlug);
  }
}
