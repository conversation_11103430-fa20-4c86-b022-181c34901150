package com.wexl.retail.custom.controller;

import com.wexl.retail.custom.dto.CustomDto;
import com.wexl.retail.custom.service.CustomService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class CustomController {

  private final CustomService customService;

  @PostMapping("bet-sections/speech-question:upload")
  public CustomDto.UploadFileResponse uploadSpeechRecording(
      @PathVariable String orgSlug, @RequestPart(value = "files") MultipartFile file) {
    return customService.uploadSpeechRecording(file, orgSlug);
  }
}
