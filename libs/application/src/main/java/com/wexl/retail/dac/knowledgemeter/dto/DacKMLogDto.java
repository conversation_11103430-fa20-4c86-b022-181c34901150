package com.wexl.retail.dac.knowledgemeter.dto;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

public record DacKMLogDto() {

  @Getter
  @RequiredArgsConstructor
  public enum JobStatus {
    IN_PROGRESS("IN_PROGRESS"),
    FAILED("FAILED"),
    COMPLETED("COMPLETED");

    private final String value;

    public static JobStatus fromValue(String value) {
      for (JobStatus type : JobStatus.values()) {
        if (type.value.equals(value)) {
          return type;
        }
      }
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.BuildErrors", new String[] {value});
    }
  }
}
