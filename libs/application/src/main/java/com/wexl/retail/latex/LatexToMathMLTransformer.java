package com.wexl.retail.latex;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Component
@Slf4j
@RequiredArgsConstructor
public class LatexToMathMLTransformer {
  private final RestTemplate restTemplate;
  private final Environment environment;

  public String convertStringContainingLatexToMathMl(String latexString) {
    // Pre-process the input string to handle literal \n characters
    latexString = latexString.replace("\\n", "<br>");

    // Define the regex pattern for LaTeX expressions with different delimiters
    // The pattern is non-greedy and handles all three delimiter types
    // Note: The order is important - we try $$...$$ first to avoid matching $...$ inside it
    Pattern pattern = Pattern.compile("\\$\\$(.+?)\\$\\$|\\$(.+?)\\$|\\\\\\((.+?)\\\\\\)");
    Matcher matcher = pattern.matcher(latexString);
    StringBuilder sb = new StringBuilder();

    while (matcher.find()) {
      // Determine which group matched and if it's display math
      String latexExpr = null;
      boolean isDisplayMath = false;

      if (matcher.group(1) != null) {
        latexExpr = matcher.group(1); // $$...$$ format (display)
        isDisplayMath = true;
      } else if (matcher.group(2) != null) {
        latexExpr = matcher.group(2); // $...$ format (inline)
      } else if (matcher.group(3) != null) {
        latexExpr = matcher.group(3); // \(...\) format (inline)
      }

      // Skip if no expression was found (shouldn't happen with the pattern)
      if (latexExpr == null) continue;

      // Clean the LaTeX expression
      latexExpr = latexExpr.replaceAll("^\\$+|\\$+$", "");

      // Convert to MathML
      String mathmlString = convertLatexToMathML(massageLatexString(latexExpr), restTemplate);

      // Clean the MathML output
      mathmlString =
          mathmlString
              .replaceAll("^\"|\"$", "") // Remove quotes
              .replaceAll("\\\\\"", "\"") // Replace \" with "
              .replaceAll("\\\\n", ""); // Remove \n

      // Replace the LaTeX with MathML
      matcher.appendReplacement(sb, Matcher.quoteReplacement(mathmlString));
    }
    matcher.appendTail(sb);

    // Return the original string with LaTeX replaced by MathML
    String finalString = sb.toString();
    log.info("Final Expression: {}", finalString);
    return finalString.replaceAll("\n", "<br>");
  }

  private String massageLatexString(String latexExpr) {
    return latexExpr
        .trim()
        .replace("\\\\", "\\")
        .replace("imes", "\times")
        .replace("\t\times", "\times")
        .replace("\t" + "imes", "\\times")
        .replace("\\t\\times", "\\times")
        .replace("ext", "\text")
        .replace("\t\text", "\text")
        .replace("\t" + "ext", "\\text")
        .replace("\\t\\text", "\\text")
        .replace("rac", "\frac")
        .replace("\f\frac", "\frac")
        .replace("\f" + "rac", "\\frac")
        .replace("\\f\\frac", "\\frac");
  }

  private String convertLatexToMathML(String latexExpr, RestTemplate restTemplate) {

    // Prepare the request headers
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setAccept(List.of(MediaType.APPLICATION_JSON));

    // Prepare the request body
    Map<String, Object> requestBody = new HashMap<>();
    requestBody.put("text", latexExpr);
    requestBody.put("from", "tex");
    requestBody.put("to", "mathml");
    requestBody.put("display", false);

    // Create the HttpEntity
    HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

    // Send the POST request
    ResponseEntity<String> response =
        restTemplate.postForEntity(getTexMathUrl(), requestEntity, String.class);

    if (response.getStatusCode().isError()) {
      log.error("Error converting from {} to MathML", latexExpr);
      return latexExpr;
    }

    // Extract the MathML string from the response
    String mathmlString = response.getBody();
    log.info("Converted from {} to {}", latexExpr, mathmlString);

    return mathmlString;
  }

  public String getTexMathUrl() {
    return isProd()
        ? "http://texmath.ndprod.svc.cluster.local:3000/convert"
        : "https://tm.wexledu.com/convert";
  }

  private boolean isProd() {
    return environment != null && Arrays.asList(environment.getActiveProfiles()).contains("prod");
  }
}
