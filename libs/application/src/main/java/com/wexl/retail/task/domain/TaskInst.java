package com.wexl.retail.task.domain;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.Student;
import com.wexl.retail.student.exam.Exam;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "task_inst", schema = "public")
public class TaskInst extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  @ManyToOne
  @JoinColumn(name = "task_id")
  private Task task;

  @OneToOne private Student student;

  @OneToOne private Exam exam;

  @Enumerated(EnumType.STRING)
  private TaskStatus completionStatus;

  private LocalDateTime completedAt;
  private String remarks;
}
