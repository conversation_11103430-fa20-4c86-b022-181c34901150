package com.wexl.retail.content.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SubtopicDetails {
  private Long id;

  @JsonProperty("name")
  private String name;

  @JsonProperty("slug")
  private String subtopicSlug;

  @JsonProperty("status")
  private Boolean status;

  @JsonProperty("mcq_question_count")
  private int mcqQuestionCount;

  @JsonProperty("subjective_question_count")
  private int subjectiveQuestionCount;

  private Boolean mlp;
}
