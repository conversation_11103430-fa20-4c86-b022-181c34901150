package com.wexl.retail.mlp.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.mlp.model.KMData;
import java.util.List;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KMSummaryResponse {

  @JsonProperty("org_name")
  private String orgName;

  @JsonProperty("name")
  private String name;

  @JsonProperty("average_percentage")
  private double averageKnowledgePercentage;

  @JsonProperty("data")
  private List<KMData> data;

  @JsonProperty("average_attendance_percentage")
  private double averageAttendancePercentage;
}
