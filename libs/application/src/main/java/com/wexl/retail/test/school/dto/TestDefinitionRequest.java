package com.wexl.retail.test.school.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.liveworksheet.dto.WorkSheetType;
import com.wexl.retail.test.school.domain.TestType;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

@Data
@Getter
@Builder
public class TestDefinitionRequest {
  private String gradeSlug;
  private String subjectSlug;
  private String testName;
  private Boolean isAutoEnabled;
  private String complexityLevelSlug;
  private Integer noOfQuestions;
  private String message;
  private Boolean active;
  private String boardSlug;
  private TestType testType;
  private List<TestQuestionRequest> questions;
  private long teacherId;
  private String questionPath;
  private String solutionPath;
  private String instructions;

  @JsonProperty("work_sheet_type")
  private WorkSheetType workSheetType;

  private String theme;

  @JsonProperty("explanation_video_link")
  private String explanationVideoLink;

  @JsonProperty("asset_slug")
  private String assetSlug;
}
