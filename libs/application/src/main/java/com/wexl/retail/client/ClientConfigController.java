package com.wexl.retail.client;

import com.wexl.retail.teacher.training.controller.SimpleDataControllerHelper;
import java.io.IOException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/client")
public class ClientConfigController {

  private final SimpleDataControllerHelper simpleDataControllerHelper;

  @GetMapping(value = "/config", produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<String> getConfig() throws IOException {
    return ResponseEntity.ok()
        .body(simpleDataControllerHelper.getStringResponseEntity("client-config.json"));
  }
}
