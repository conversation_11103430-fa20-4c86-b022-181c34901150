package com.wexl.retail.courses.step.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.courses.enrollment.dto.StudentStepProgress;
import com.wexl.retail.courses.module.model.CourseModule;
import com.wexl.retail.courses.step.dto.CourseStepRequest;
import com.wexl.retail.courses.step.dto.CourseStepRequest.CourseStepRequestAttribute;
import com.wexl.retail.courses.step.dto.CourseStepResponse;
import com.wexl.retail.courses.step.model.CourseItem;
import com.wexl.retail.courses.step.model.CourseItemAttributesDto;
import com.wexl.retail.courses.step.model.CourseItemAttributesDto.Attributes;
import com.wexl.retail.courses.step.model.CourseItemAttributesTestMetadata;
import com.wexl.retail.courses.step.model.CourseItemAttributesVideoMetadata;
import com.wexl.retail.courses.step.model.CourseItemType;
import com.wexl.retail.courses.step.model.CoursePage;
import com.wexl.retail.courses.step.repository.CourseStepRepository;
import com.wexl.retail.erp.attendance.domain.CompletionStatus;
import com.wexl.retail.mlp.repository.StudentKMeterRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.util.ValidationUtils;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class CourseItemFactory {
  private final UserRepository userRepository;

  private final DateTimeUtil dateTimeUtil;
  private final CoursePageService coursePageService;
  private final ScheduleTestService scheduleTestService;
  private final CourseStepRepository courseStepRepository;
  private final TestDefinitionRepository testDefinitionRepository;
  private final AuthService authService;
  private final ExamRepository examRepository;
  private final StudentRepository studentRepository;
  private final TeacherRepository teacherRepository;
  private final StudentKMeterRepository studentKMeterRepository;
  private final ValidationUtils validationUtils;

  public static final String VIDEO_LINK_KEY = "video_link";
  public static final String VIDEO_THUMBNAIL_KEY = "video_thumbnail";
  public static final String VIDEO_SHA_KEY = "video_sha";

  public CourseStepResponse createPageItem(CourseModule courseModule, CourseStepRequest request) {
    var coursePage = coursePageService.findCoursePageById(Long.parseLong(request.getItemPK()));
    var courseItem = buildCourseStepPage(courseModule, coursePage, request.getTitle());
    return buildCourseStepResponse(courseStepRepository.save(courseItem));
  }

  public CourseItem buildCourseStepPage(
      int sequenceNumber, CourseModule courseModule, CoursePage coursePage, String title) {
    var courseItem = buildCourseStep(courseModule, sequenceNumber, title);
    courseItem.setCoursePage(coursePage);
    courseItem.setItemType(CourseItemType.PAGE);
    return courseItem;
  }

  public CourseItem buildCourseStepAsset(
      int sequenceNumber, CourseModule courseModule, String assetSlug, String title) {
    var courseItem = buildCourseStep(courseModule, sequenceNumber, title);
    courseItem.setAssetSlug(assetSlug);
    courseItem.setItemType(CourseItemType.ASSET);
    return courseItem;
  }

  public CourseItem buildCourseStepPage(
      CourseModule courseModule, CoursePage coursePage, String title) {
    var courseItem =
        buildCourseStep(
            courseModule, courseStepRepository.getNextSeqNumInStep(courseModule.getId()), title);
    courseItem.setCoursePage(coursePage);
    courseItem.setItemType(CourseItemType.PAGE);
    return courseItem;
  }

  public CourseItem buildTestCourseStep(
      int sequenceNumber,
      CourseModule courseModule,
      String assetSlug,
      String title,
      TestDefinition testDef,
      CourseItemAttributesDto.Attributes attributes,
      CourseItemType type) {
    var courseItem = buildCourseStep(courseModule, sequenceNumber, title);
    courseItem.setAssetSlug(assetSlug);
    courseItem.setItemType(type);
    courseItem.setTestDefinition(testDef);
    courseItem.setAttributes(attributes);
    return courseItem;
  }

  public CourseStepResponse createAssetStep(CourseModule courseModule, CourseStepRequest request) {
    var courseItem =
        buildCourseStep(
            courseModule,
            courseStepRepository.getNextSeqNumInStep(courseModule.getId()),
            request.getTitle());
    courseItem.setItemType(CourseItemType.ASSET);
    courseItem.setAssetSlug(request.getItemPK());
    return buildCourseStepResponse(courseStepRepository.save(courseItem));
  }

  public CourseStepResponse createTestDefStep(
      CourseModule courseModule, CourseStepRequest request) {
    var testDefinition =
        scheduleTestService.findTestDefinitionById(Long.parseLong(request.getItemPK()));
    var courseItem =
        buildCourseStep(
            courseModule,
            courseStepRepository.getNextSeqNumInStep(courseModule.getId()),
            request.getTitle());
    courseItem.setItemType(CourseItemType.SCHOOL_TEST);
    courseItem.setTestDefinition(testDefinition);
    courseItem.setAttributes(
        CourseItemAttributesDto.Attributes.builder()
            .metaData(
                CourseItemAttributesDto.MetaData.builder()
                    .testMetadata(
                        CourseItemAttributesTestMetadata.builder()
                            .questionCount(testDefinition.getNoOfQuestions())
                            .complexity(testDefinition.getComplexityLevelSlug())
                            .build())
                    .build())
            .build());
    return buildCourseStepResponse(courseStepRepository.save(courseItem));
  }

  public CourseStepResponse createMockTestStep(
      CourseModule courseModule, CourseStepRequest request) {
    var testDefinition =
        scheduleTestService.findTestDefinitionById(Long.parseLong(request.getItemPK()));
    var courseItem =
        buildCourseStep(
            courseModule,
            courseStepRepository.getNextSeqNumInStep(courseModule.getId()),
            request.getTitle());
    courseItem.setItemType(CourseItemType.MOCK_TEST);
    courseItem.setTestDefinition(testDefinition);
    courseItem.setAttributes(
        CourseItemAttributesDto.Attributes.builder()
            .metaData(
                CourseItemAttributesDto.MetaData.builder()
                    .testMetadata(
                        CourseItemAttributesTestMetadata.builder()
                            .questionCount(testDefinition.getNoOfQuestions())
                            .complexity(testDefinition.getComplexityLevelSlug())
                            .build())
                    .build())
            .build());
    return buildCourseStepResponse(courseStepRepository.save(courseItem));
  }

  public CourseStepResponse createScormStep(CourseModule courseModule, CourseStepRequest request) {
    var courseItem =
        buildCourseStep(
            courseModule,
            courseStepRepository.getNextSeqNumInStep(courseModule.getId()),
            request.getTitle());
    String uploadPath = createS3ObjectKey(courseModule, request);
    courseItem.setItemType(CourseItemType.SCORM);
    courseItem.setS3ObjectKey(uploadPath);
    return buildCourseStepResponse(courseStepRepository.save(courseItem));
  }

  public CourseStepResponse createAssignmentStep(
      CourseModule courseModule, CourseStepRequest request) {
    String orgSlug = authService.getUserDetails().getOrganization();
    var testDefinition =
        testDefinitionRepository.findByIdAndOrganization(
            Long.parseLong(request.getItemPK()), orgSlug);
    if (testDefinition.isEmpty()) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.InvalidAssignmentId");
    }

    var courseItem =
        buildCourseStep(
            courseModule,
            courseStepRepository.getNextSeqNumInStep(courseModule.getId()),
            request.getTitle());
    courseItem.setItemType(CourseItemType.ASSIGNMENT);
    courseItem.setTestDefinition(testDefinition.get());
    return buildCourseStepResponse(courseStepRepository.save(courseItem));
  }

  public String createS3ObjectKey(CourseModule courseModule, CourseStepRequest request) {
    return String.format(
        "%s/%s/%s/%s",
        courseModule.getOrgSlug(), request.getItemType(), courseModule.getId(), request.getTitle());
  }

  public CourseItem buildCourseStep(CourseModule courseModule, int sequenceNumber, String title) {
    return CourseItem.builder()
        .title(title)
        .orgSlug(courseModule.getOrgSlug())
        .sequenceNumber(sequenceNumber)
        .courseModule(courseModule)
        .build();
  }

  public CourseStepResponse updatePageItem(CourseItem courseStep, CourseStepRequest request) {
    var coursePage = coursePageService.findCoursePageById(Long.parseLong(request.getItemPK()));
    courseStep.setTitle(request.getTitle());
    courseStep.setCoursePage(coursePage);
    return buildCourseStepResponse(courseStepRepository.save(courseStep));
  }

  public CourseStepResponse updateAssetItem(CourseItem courseStep, CourseStepRequest request) {
    courseStep.setTitle(request.getTitle());
    courseStep.setAssetSlug(request.getItemPK());
    return buildCourseStepResponse(courseStepRepository.save(courseStep));
  }

  public CourseStepResponse updateTestDefItem(CourseItem courseStep, CourseStepRequest request) {
    var testDefinition =
        scheduleTestService.findTestDefinitionById(Long.parseLong(request.getItemPK()));
    courseStep.setTitle(request.getTitle());
    courseStep.setTestDefinition(testDefinition);
    return buildCourseStepResponse(courseStepRepository.save(courseStep));
  }

  public CourseStepResponse buildCourseStepResponse(CourseItem courseItem) {

    return CourseStepResponse.builder()
        .id(courseItem.getId())
        .title(courseItem.getTitle())
        .sequenceNumber(courseItem.getSequenceNumber())
        .itemType(courseItem.getItemType().toString())
        .courseModuleId(
            Objects.nonNull(courseItem.getCourseModule())
                ? courseItem.getCourseModule().getId()
                : null)
        .coursePageId(
            Objects.nonNull(courseItem.getCoursePage()) ? courseItem.getCoursePage().getId() : null)
        .publishedAt(
            Objects.nonNull(courseItem.getPublishedAt())
                ? DateTimeUtil.convertIso8601ToEpoch(courseItem.getPublishedAt().toLocalDateTime())
                : null)
        .testDefinitionId(
            Objects.nonNull(courseItem.getTestDefinition())
                ? courseItem.getTestDefinition().getId()
                : null)
        .assetSlug(courseItem.getAssetSlug())
        .fileId(courseItem.getFileId())
        .attributes(courseItem.getAttributes())
        .build();
  }

  public CourseStepResponse buildCourseStepStudentProgress(StudentStepProgress response) {
    List<Long> examId;
    double percentage = 0.0;
    Optional<Exam> exam = Optional.empty();
    var student = studentRepository.findByUserId(response.getStudentId());
    examId =
        examRepository.getExamIdByCourseItemIdAndStudentId(
            student.getId(),
            response.getTestDefinitionId(),
            response.getCourseScheduleItemInstId());
    if (!examId.isEmpty()) exam = examRepository.findById(examId.getFirst());
    if (exam.isPresent()) {
      var studentKM =
          studentKMeterRepository.findByStudentIdAndExamTypeAndExamId(
              student.getId(), exam.get().getExamType(), exam.get().getId());
      percentage = studentKM.isEmpty() ? 0.0 : studentKM.get().getKnowledgePercentage();
    }

    return CourseStepResponse.builder()
        .id(response.getId())
        .title(response.getTitle())
        .examId(examId)
        .sequenceNumber(response.getSequenceNumber())
        .itemType(response.getItemType())
        .courseModuleId(response.getCourseModuleId())
        .coursePageId(response.getCoursePageId())
        .assetSlug(response.getAssetSlug())
        .attributes(convertJsonToObject(response.getAttributes()))
        .publishedAt(
            Objects.nonNull(response.getPublishedAt())
                ? DateTimeUtil.convertIso8601ToEpoch(response.getPublishedAt().toLocalDateTime())
                : null)
        .testDefinitionId(response.getTestDefinitionId())
        .corrected(exam.isPresent() ? exam.get().isCorrected() : null)
        .status(exam.isPresent() ? CompletionStatus.COMPLETED.toString() : response.getStatus())
        .courseScheduleItemInstId(response.getCourseScheduleItemInstId())
        .courseStepKM(percentage)
        .build();
  }

  private CourseItemAttributesDto.Attributes convertJsonToObject(String attributes) {
    if (StringUtils.isBlank(attributes)) {
      return defaultCourseItemAttributes();
    }
    try {
      ObjectMapper mapper = new ObjectMapper();
      var metadata = mapper.readValue(attributes, CourseItemAttributesDto.MetaData.class);
      return Attributes.builder().metaData(metadata).build();
    } catch (Exception ex) {
      log.error("Unable to parse the attributes value for course [" + attributes + "");
    }
    return defaultCourseItemAttributes();
  }

  public CourseItemAttributesDto.Attributes defaultCourseItemAttributes() {
    return CourseItemAttributesDto.Attributes.builder()
        .metaData(
            CourseItemAttributesDto.MetaData.builder()
                .videoMetadata(
                    (CourseItemAttributesVideoMetadata.builder().link("").thumbnail("").build()))
                .testMetadata(
                    CourseItemAttributesTestMetadata.builder()
                        .questionCount(0)
                        .complexity("")
                        .build())
                .build())
        .build();
  }

  public CourseStepResponse createConceptVideosStep(
      CourseModule courseModule, CourseStepRequest request) {
    var courseItem =
        buildCourseStep(
            courseModule,
            courseStepRepository.getNextSeqNumInStep(courseModule.getId()),
            request.getTitle());
    courseItem.setItemType(CourseItemType.CONCEPT_VIDEOS);
    courseItem.setConceptVideoUuid(request.getItemPK());
    courseItem.setAttributes(
        CourseItemAttributesDto.Attributes.builder()
            .metaData(
                CourseItemAttributesDto.MetaData.builder()
                    .videoMetadata(createVideoMetadata(request.getAttributes()))
                    .build())
            .build());

    return buildCourseStepResponse(courseStepRepository.save(courseItem));
  }

  private CourseItemAttributesVideoMetadata createVideoMetadata(
      List<CourseStepRequestAttribute> attributes) {
    return CourseItemAttributesVideoMetadata.builder()
        .link(getValueFromList(attributes, VIDEO_LINK_KEY))
        .sha(getValueFromList(attributes, VIDEO_SHA_KEY))
        .thumbnail(getValueFromList(attributes, VIDEO_THUMBNAIL_KEY))
        .build();
  }

  private String getValueFromList(List<CourseStepRequestAttribute> attributes, String key) {
    final Optional<String> value =
        attributes.stream()
            .filter(attr -> key.equals(attr.getKey()))
            .findFirst()
            .map(CourseStepRequestAttribute::getValue);

    return value.orElse("");
  }

  public CourseItem buildCourseStepConceptVideos(
      int sequenceNumber,
      CourseModule courseModule,
      String conceptVideoUuid,
      String title,
      CourseItemAttributesDto.Attributes attributes) {
    var courseItem = buildCourseStep(courseModule, sequenceNumber, title);
    courseItem.setConceptVideoUuid(conceptVideoUuid);
    courseItem.setItemType(CourseItemType.CONCEPT_VIDEOS);
    courseItem.setAttributes(attributes);
    return courseItem;
  }
}
