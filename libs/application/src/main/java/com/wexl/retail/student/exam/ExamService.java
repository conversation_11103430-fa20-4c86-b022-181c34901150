package com.wexl.retail.student.exam;

import static com.wexl.retail.util.Constants.*;

import annotationeer.pdfbox.WexlAnnotationSaver;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.ai.GPTQuestionAnalysis;
import com.wexl.retail.ai.dto.ExamAnalysis;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.commons.util.MathUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.*;
import com.wexl.retail.mlp.service.VideoUrlEncryptor;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.pdf.viewer.service.AnnotationService;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.answer.*;
import com.wexl.retail.student.exam.dto.ExamDto;
import com.wexl.retail.student.exam.publisher.ExamCompletionEventPublisher;
import com.wexl.retail.student.home.ActivitySummary;
import com.wexl.retail.task.domain.Task;
import com.wexl.retail.task.domain.TaskStatus;
import com.wexl.retail.task.publisher.TaskCompletionEventPublisher;
import com.wexl.retail.task.service.TaskInstService;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestDefinitionMetadata.VideoExplanationMetadata;
import com.wexl.retail.test.school.domain.TestQuestion;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.util.UploadService;
import io.jsonwebtoken.lang.Assert;
import io.jsonwebtoken.lang.Collections;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExamService {

  private final AuthService authService;
  private final ExamFactory examFactory;
  private final StrapiService strapiService;
  private final ContentService contentService;
  private final UploadService uploadService;
  private final StorageService storageService;
  private final ExamRepository examRepository;
  private final UserRepository userRepository;
  private final ExamTransformer examTransformer;
  private final AnnotationService annotationService;
  private final ExamActivityService examActivityService;
  private final StudentAnswerService studentAnswerService;
  private final StudentAnswerRepository studentAnswerRepository;
  private final ExamCompletionEventPublisher examCompletionEventPublisher;
  private final GPTQuestionAnalysis gptQuestionAnalysis;
  private final ExamAnswerRepository examAnswerRepository;

  private final TaskCompletionEventPublisher taskCompletionEventPublisher;

  private final TaskInstService taskInstService;
  private final VideoUrlEncryptor videoUrlEncryptor;

  private final TestDefinitionService testDefinitionService;
  private static final String ERROR_EXAMID = "error.ExamId";

  /**
   * Find By Id.
   *
   * @param examId ExamId
   * @return Exam
   */
  public Exam findById(long examId) {
    return examRepository
        .findById(examId)
        .orElseThrow(() -> new ApiException(InternalErrorCodes.INVALID_REQUEST, ERROR_EXAMID));
  }

  /**
   * Save Exam.
   *
   * @param examRequest @{@link ExamRequest}
   * @return @{@link ExamResponse}
   */
  public ExamResponse createExamTest(ExamRequest examRequest) {
    var exam = examFactory.createTest(examRequest.getUuid());
    exam.setNoOfQuestions(30);
    return buildExamUsingChapter(exam, examRequest);
  }

  private ExamResponse buildExamUsingChapter(Exam exam, ExamRequest examRequest) {
    exam.setExamDifficultyLevelId(examRequest.getExamDifficultyLevelId());
    exam.setAllowedDuration(Constants.ALLLOWED_DURATION);

    Chapter chapter = strapiService.getChapterById(examRequest.getChapterId());
    exam.setChapterId(examRequest.getChapterId());
    exam.setSubjectName(chapter.getSubject().getName());
    exam.setSubjectSlug(chapter.getSubject().getSlug());
    exam.setChapterName(chapter.getName());
    exam.setChapterSlug(chapter.getSlug());
    exam.setSubjectId(chapter.getSubject().getId());
    exam.setAllChaptersSelected(examRequest.isAllChapterSelected());
    return examTransformer.mapExamToExamResponse(examRepository.save(exam));
  }

  /**
   * Save Exam.
   *
   * @param examRequest @{@link ExamRequest}
   * @return @{@link ExamResponse}
   */
  public ExamResponse createExamPractice(
      Student student, ExamRequest examRequest, SubTopicResponse subtopic) {
    log.debug("Save Exam Called");
    var exam = examFactory.createPracticeTest(examRequest.getUuid(), student);
    exam.setExamDifficultyLevelId(examRequest.getExamDifficultyLevelId());
    exam.setAllowedDuration(Constants.ALLLOWED_DURATION);
    exam.setChapterId(examRequest.getChapterId());
    exam.setSubTopicId(examRequest.getSubTopicId());
    exam.setSubtopicName(subtopic.getName());
    exam.setSubtopicSlug(subtopic.getSlug());
    exam.setChapterName(subtopic.getChapterName());
    exam.setChapterSlug(subtopic.getChapterSlug());
    exam.setSubjectId(subtopic.getSubjectId());
    exam.setSubjectSlug(subtopic.getSubjectSlug());
    exam.setSubjectName(subtopic.getSubjectName());

    return examTransformer.mapExamToExamResponse(examRepository.save(exam));
  }

  public Exam save(Exam exam) {
    log.debug("Save called on Exam");
    return examRepository.save(exam);
  }

  /**
   * Complete Exam.
   *
   * @param examId examId
   * @return @{@link ExamDetails}
   */
  public ExamDetails completeExam(long examId) {
    log.debug("Complete Exam called on " + examId);
    var exam = this.findById(examId);
    var practiceExamInfo = studentAnswerRepository.getPracticeExamInfo(examId);
    exam.setNoOfQuestions(studentAnswerService.countNoOfQuestion(examId));
    exam.setCorrectAnswers(
        Objects.requireNonNullElse(practiceExamInfo.getCorrectAnswersCount(), 0));
    exam.setMarksScored(Objects.requireNonNullElse(practiceExamInfo.getMarksScored(), 0F));
    exam.setTotalMarks(Objects.requireNonNullElse(practiceExamInfo.getTotalMarks(), 0F));
    exam.setCorrected(!studentAnswerService.isSubjectiveQuestionExists(examId));
    var chapterDetails = strapiService.getChapterById(exam.getChapterId());

    if (exam.getEndTime() == null) {
      // This logic is required for handling multiple "Exam submit" calls from mobile
      // due to various reasons.
      var date = new Date();
      exam.setEndTime(new Timestamp(date.getTime()));
      exam.setCompleted(true);
      this.save(exam);
      taskInstService.updateTaskStatusIfPresent(exam, TaskStatus.COMPLETED);
      try {
        examActivityService.notifyTestCompletion(
            exam, studentAnswerService.getTestDefinitionService());
        examCompletionEventPublisher.publishExamCompletion(exam);
      } catch (Exception ex) {
        log.error("Error during publishing test completion message", ex);
        // log and move on for submission of test
      }
    }

    return ExamDetails.builder()
        .examId(examId)
        .noOfQuestions(exam.getNoOfQuestions())
        .noOfCorrectAnswer(practiceExamInfo.getCorrectAnswersCount())
        .chapter(chapterDetails.getName())
        .grade(contentService.getGradeById(chapterDetails.getGrade().getId()).getName())
        .build();
  }

  public StudentAnswerResponse correctExam(
      final long examId,
      final List<ExamQuestionsMarks> examQuestionsMarks,
      final String bearerToken) {
    log.debug("Finding Exam by Id : " + examId);
    var exam = examRepository.getById(examId);

    // Commenting out the below code to allow multiple rounds of re-correction
    //    if (exam.isCorrected()) {
    //      log.debug("Exam already corrected");
    //      return examResultByExamId(examId, bearerToken);
    //    }

    final List<ExamAnswer> examAnswers = studentAnswerRepository.findByExamId(examId);
    var marksScoredInExam = new AtomicReference<>(0F);
    examAnswers.forEach(
        examAnswer -> {
          final ExamQuestionsMarks examQuestionMarks =
              examQuestionsMarks.stream()
                  .filter(q -> examAnswer.getQuestionUuid().equals(q.getQuestionUuid()))
                  .findAny()
                  .orElse(null);
          if (Objects.nonNull(examQuestionMarks)) {
            examAnswer.setMarksScoredPerQuestion(examQuestionMarks.getMarksScored());
            examAnswer.setFeedback(examQuestionMarks.getFeedback());
            if (Objects.nonNull(examQuestionMarks.getAiMarks())
                && Objects.nonNull(examQuestionMarks.getAiAnalysis())) {
              examAnswer.setAiAnalysis(examQuestionMarks.getAiAnalysis());
              examAnswer.setAiMarks(examQuestionMarks.getAiMarks());
            }
            marksScoredInExam.updateAndGet(marks -> (marks + examQuestionMarks.getMarksScored()));
            studentAnswerRepository.save(examAnswer);
          }
        });
    exam.setMarksScored(marksScoredInExam.get());
    exam.setCorrected(
        Objects.isNull(
            examAnswers.stream()
                .filter(
                    examAnswer ->
                        QuestionType.SUBJECTIVE.getType().equalsIgnoreCase(examAnswer.getType())
                            && Objects.isNull(examAnswer.getMarksScoredPerQuestion()))
                .findAny()
                .orElse(null)));
    exam = examRepository.save(exam);
    if (checkIfPdfAnswerSheetAvailable(exam)) {
      mergePdfAnnotations(examId);
    }
    examActivityService.notifyTestCorrection(exam, studentAnswerService.getTestDefinitionService());
    taskCompletionEventPublisher.publishTaskCompletion(exam);
    return examResultByExamId(examId, bearerToken);
  }

  public StudentAnswerResponse examResultByExamId(long examId, String bearerToken) {
    var exam =
        examRepository
            .findById(examId)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST,
                        ERROR_EXAMID,
                        new String[] {Long.toString(examId)}));
    var user = authService.getUserDetails();
    var selectedAnswers = getSelectedAnswers(bearerToken, exam, user.getOrganization());
    double percentageSecured = getPercentageSecured(selectedAnswers);

    var hasPdfAnswerSheet = checkIfPdfAnswerSheetAvailable(exam);
    var testDefinition = exam.getTestDefinition();
    final VideoExplanationMetadata videoMetadata = getVideoMetadata(testDefinition);
    var filePath = uploadService.constructAnswerSheetFilePath(examId, "%s.pdf".formatted(examId));
    final var examDetails =
        ExamDetails.builder()
            .noOfCorrectAnswer(exam.getCorrectAnswers() == null ? 0 : exam.getCorrectAnswers())
            .testType(String.valueOf(testDefinition == null ? null : testDefinition.getType()))
            .hasPdfAnswerSheet(hasPdfAnswerSheet)
            .pdfAnswerSheetUrl(hasPdfAnswerSheet ? getPdfAnswerSheetUrl(exam) : "")
            .uploadCorrectedAnswerSheetUrl(
                hasPdfAnswerSheet ? storageService.generatePreSignedUrlForUpload(filePath) : null)
            .percentageSecured(percentageSecured)
            .isGoalAchieved(false)
            .examId(exam.getId())
            .totalMarks((long) Math.round(exam.getTotalMarks()))
            .noOfQuestions(exam.getNoOfQuestions())
            .corrected(exam.isCorrected())
            .predefinedPdfSolutionSheetUrl(getPredefinedPdfSolutionSheetUrl(exam))
            .pdfQuestionSheetUrl(getPdfQuestionSheetUrl(exam))
            .explanationVideoUrl(videoMetadata.getVideoLink())
            .explanationVideoSha(
                videoUrlEncryptor.convertEncrypted(videoMetadata.getAltVimeoLink()))
            .assetSlug(getAssetSlug(testDefinition))
            .studentName(exam.getStudentName())
            .build();

    populateExamDetailsForScheduledTest(exam, examDetails);
    try {
      populateExamDetailsForPractice(exam, examDetails);
      populateExamDetailsForWexlTest(exam, examDetails);
      populateExamDetailsForRemedialBucket(exam, examDetails);
    } catch (Exception ex) {
      log.error("Unable to populate exam details like subject, grade and chapter for practice", ex);
    }

    return StudentAnswerResponse.builder()
        .examQuestion(selectedAnswers)
        .examDetails(examDetails)
        .build();
  }

  public String getAssetSlug(TestDefinition testDefinition) {
    if (testDefinition == null) {
      return null;
    }
    return Objects.nonNull(testDefinition.getMetadata().getAssetSlug())
        ? testDefinition.getMetadata().getAssetSlug()
        : null;
  }

  private void populateExamDetailsForRemedialBucket(Exam exam, ExamDetails examDetails) {
    if (exam.getExamType() != REVISION_EXAM) {
      return;
    }
    Chapter chapterEntity = strapiService.getChapterById(exam.getChapterId());
    examDetails.setTestName(String.format("Revision Test - %s", chapterEntity.getName()));
    examDetails.setGrade(chapterEntity.getGrade().getName());
    examDetails.setGrade(chapterEntity.getGrade().getName());
    examDetails.setSubject(chapterEntity.getSubject().getName());
  }

  public VideoExplanationMetadata getVideoMetadata(TestDefinition testDefinition) {
    if (testDefinition == null) {
      return VideoExplanationMetadata.builder().videoLink("").altVimeoLink("").build();
    }
    final VideoExplanationMetadata videos = testDefinition.getMetadata().getExplanationVideo();
    if (videos == null) {
      return VideoExplanationMetadata.builder().videoLink("").altVimeoLink("").build();
    }
    return videos;
  }

  private String getPredefinedPdfSolutionSheetUrl(Exam exam) {
    if (exam == null) {
      return null;
    }
    return Optional.ofNullable(exam.getTestDefinition())
        .flatMap(definition -> Optional.ofNullable(definition.getSolutionPath()))
        .map(storageService::generatePreSignedUrlForFetch)
        .orElse(null);
  }

  private String getPdfQuestionSheetUrl(Exam exam) {
    if (exam == null) {
      return null;
    }
    return Optional.ofNullable(exam.getTestDefinition())
        .flatMap(definition -> Optional.ofNullable(definition.getQuestionPath()))
        .map(storageService::generatePreSignedUrlForFetch)
        .orElse(null);
  }

  private void populateExamDetailsForPractice(Exam examInfo, ExamDetails examDetails) {
    if (examInfo.getExamType() != PRACTICE_EXAM) {
      return;
    }
    Chapter chapterEntity = strapiService.getChapterById(examInfo.getChapterId());
    SubTopic subTopic = strapiService.getSubTopicById(examInfo.getSubTopicId());

    examDetails.setTestName(
        String.format("Practice - %s - %s", chapterEntity.getName(), subTopic.getName()));
    examDetails.setGrade(chapterEntity.getGrade().getName());
    examDetails.setSubject(chapterEntity.getSubject().getName());
  }

  private void populateExamDetailsForWexlTest(Exam examInfo, ExamDetails examDetails) {
    if (examInfo.getExamType() != TEST_EXAM) {
      return;
    }
    Chapter chapterEntity = strapiService.getChapterById(examInfo.getChapterId());

    examDetails.setTestName(String.format("WeXL Test - %s", chapterEntity.getName()));
    examDetails.setGrade(chapterEntity.getGrade().getName());
    examDetails.setSubject(chapterEntity.getSubject().getName());
  }

  private void populateExamDetailsForScheduledTest(Exam examInfo, ExamDetails examDetails) {
    final var testDefinition = getTestDefinitionForAnExam(examInfo);
    if (Objects.nonNull(testDefinition)) {
      examDetails.setTestName(testDefinition.getTestName());
      examDetails.setGrade(testDefinition.getGradeSlug());
      examDetails.setSubject(testDefinition.getSubjectSlug());
    }
  }

  public String getPdfAnswerSheetUrl(Exam examInfo) {
    String destPath = getPdfAnswerSheetPath(examInfo);
    return storageService.generatePreSignedUrlForFetch(destPath);
  }

  public boolean checkIfPdfAnswerSheetAvailable(Exam exam) {
    String destPath = getPdfAnswerSheetPath(exam);
    return storageService.isFileAvailable(destPath);
  }

  private String getPdfAnswerSheetPath(Exam exam) {
    return uploadService.constructAnswerSheetFilePath(exam, String.format("%s.pdf", exam.getId()));
  }

  public TestDefinition getTestDefinitionForAnExam(final Exam exam) {
    var testDefinition = exam.getTestDefinition();
    if (Objects.nonNull(testDefinition)) {
      return testDefinition;
    }

    return Optional.ofNullable(exam.getScheduleTest())
        .map(ScheduleTest::getTestDefinition)
        .orElse(null);
  }

  private Map<String, TestQuestion> getTestDefQuestions(final TestDefinition testDefinition) {
    Assert.notNull(testDefinition, "TestDefinition cannot be null!");

    final List<TestQuestion> testQuestionsForTestDefinition =
        testDefinitionService.getTestQuestionsForTestDefinition(testDefinition);

    if (Collections.isEmpty(testQuestionsForTestDefinition)) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.TestDefinition",
          new String[] {Long.toString(testDefinition.getId())});
    }

    var testDefQuestions = new HashMap<String, TestQuestion>();
    testQuestionsForTestDefinition.forEach(
        question -> testDefQuestions.put(question.getQuestionUuid(), question));

    return testDefQuestions;
  }

  public Map<String, TestQuestion> getTestDefQuestions(Exam exam) {
    return Optional.ofNullable(getTestDefinitionForAnExam(exam))
        .map(this::getTestDefQuestions)
        .orElse(new HashMap<>());
  }

  public int getAssignedMarksForTestQuestion(
      Map<String, TestQuestion> testQuestions, final QuestionDto.Question question) {
    final var testQuestion = testQuestions.get(question.uuid());
    return Objects.nonNull(testQuestion) ? testQuestion.getMarks() : question.marks();
  }

  private List<ExamQuestion> constructExamQuestionList(
      TestDefinition testDefinition, String bearerToken, Exam exam, String orgSlug) {

    final List<QuestionDto.SearchQuestionResponse> testDefQuestionsFlattened =
        testDefinitionService.getTestDefQuestionsResponses(testDefinition, bearerToken, orgSlug);
    final List<QuestionDto.Question> questions =
        testDefQuestionsFlattened.stream()
            .map(QuestionDto.SearchQuestionResponse::questions)
            .flatMap(List::stream)
            .toList();
    final List<ExamAnswer> examAnswers = exam.getExamAnswers();

    List<ExamQuestion> result = new ArrayList<>();
    questions.forEach(
        question -> {
          getExamAnswerForQuestion(examAnswers, question)
              .ifPresent(
                  examAnswer -> {
                    var examQuestion =
                        ExamQuestion.builder()
                            .examAnswerId(examAnswer.getId())
                            .questionDetails(studentAnswerService.transformToQuestion(question))
                            .questionsResponse(question)
                            .isCorrect(examAnswer.isCorrect())
                            .questionId(examAnswer.getQuestionId())
                            .questionUuid(examAnswer.getQuestionUuid())
                            .selectedAnswer(
                                Objects.nonNull(examAnswer.getSelectedOption())
                                    ? examAnswer.getSelectedOption()
                                    : 0)
                            .answer(examAnswer.getAnswer())
                            .aiMarks(examAnswer.getAiMarks())
                            .aiAnalysis(examAnswer.getAiAnalysis())
                            .subjectiveWrittenAnswer(examAnswer.getSubjectiveWrittenAnswer())
                            .type(examAnswer.getType())
                            .answerType(examAnswer.getAnswerType())
                            .marksScored(examAnswer.getMarksScoredPerQuestion())
                            .attachments(
                                examAnswer.getExamAnswerAttachments().parallelStream()
                                    .map(
                                        attachment ->
                                            storageService.generatePreSignedUrlForFetch(
                                                attachment.getPath()))
                                    .toList())
                            .marks(
                                getAssignedMarksForTestQuestion(
                                    getTestDefQuestions(testDefinition), question))
                            .feedback(examAnswer.getFeedback())
                            .build();
                    studentAnswerService.addElaborateAnswerIfMcqQuestion(
                        question, examQuestion, examAnswer);
                    result.add(examQuestion);
                  });
        });
    return result;
  }

  private Optional<ExamAnswer> getExamAnswerForQuestion(
      List<ExamAnswer> examAnswers, QuestionDto.Question question) {
    return examAnswers.stream()
        .filter(examAnswer -> examAnswer.getQuestionUuid().equals(question.uuid()))
        .findFirst();
  }

  private List<ExamQuestion> getSelectedAnswers(String bearerToken, Exam exam, String orgSlug) {
    final TestDefinition testDefinitionForAnExam = getTestDefinitionForAnExam(exam);
    if (testDefinitionForAnExam != null) {
      return constructExamQuestionList(testDefinitionForAnExam, bearerToken, exam, orgSlug);
    }

    var testDefQuestions = getTestDefQuestions(exam);
    var examAnswers = studentAnswerRepository.findByExamId(exam.getId());
    var examQuestionList = new ArrayList<ExamQuestion>();
    for (ExamAnswer examAnswer : examAnswers) {
      try {
        final var questionType = QuestionType.getByType(examAnswer.getType());
        var questionResponse =
            contentService.getQuestionsByUuid(
                bearerToken, questionType.toString(), examAnswer.getQuestionUuid(), orgSlug);
        if (questionResponse.questions().isEmpty()) {
          throw new ApiException(
              InternalErrorCodes.INVALID_REQUEST,
              "Question not found with uuid" + examAnswer.getQuestionUuid());
        }
        var question = questionResponse.questions().getFirst();
        var examQuestion =
            ExamQuestion.builder()
                .examAnswerId(examAnswer.getId())
                .questionDetails(studentAnswerService.transformToQuestion(question))
                .questionsResponse(question)
                .isCorrect(examAnswer.isCorrect())
                .questionId(examAnswer.getQuestionId())
                .questionUuid(examAnswer.getQuestionUuid())
                .selectedAnswer(examAnswer.getSelectedOption())
                .aiMarks(examAnswer.getAiMarks())
                .aiAnalysis(examAnswer.getAiAnalysis())
                .subjectiveWrittenAnswer(examAnswer.getSubjectiveWrittenAnswer())
                .answer(examAnswer.getAnswer())
                .type(examAnswer.getType())
                .answerType(examAnswer.getAnswerType())
                .marksScored(examAnswer.getMarksScoredPerQuestion())
                .attachments(
                    examAnswer.getExamAnswerAttachments().parallelStream()
                        .map(
                            attachment ->
                                storageService.generatePreSignedUrlForFetch(attachment.getPath()))
                        .toList())
                .marks(getAssignedMarksForTestQuestion(testDefQuestions, question))
                .feedback(examAnswer.getFeedback())
                .build();
        studentAnswerService.addElaborateAnswerIfMcqQuestion(question, examQuestion, examAnswer);
        examQuestionList.add(examQuestion);
      } catch (Exception e) {
        log.error(e.getMessage(), e);
      }
    }
    return examQuestionList;
  }

  private double getPercentageSecured(final List<ExamQuestion> selectedAnswers) {
    double marksScored = 0;
    double totalMarks = 0;
    for (ExamQuestion examQuestion : selectedAnswers) {
      if (Objects.nonNull(examQuestion.getMarks())) {
        totalMarks += examQuestion.getMarks();
      }
      if (Objects.nonNull(examQuestion.getMarksScored())) {
        marksScored += examQuestion.getMarksScored();
      }
    }
    return totalMarks == 0d ? 0 : ((marksScored / totalMarks) * 100);
  }

  public void validateIfTeacherCanViewExam(String teacherOrg, long examId) {
    validateIfExamExists(examId);

    String studentOrganizationForExam = examRepository.findStudentOrganizationForExam(examId);
    if (!teacherOrg.equals(studentOrganizationForExam)) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.Exam.Access");
    }
  }

  public void validateIfParentCanViewExam(String childAuthUserId, long examId) {
    validateIfExamExists(examId);

    var studentAuthIdForExam = examRepository.findStudentAuthIdForExam(examId);
    if (!childAuthUserId.equals(studentAuthIdForExam)) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.ExamResult.Access");
    }
  }

  public void validateIfExamExists(long examId) {
    var examCount = examRepository.findExamExists(examId);
    if (examCount != 1) {
      throw new ApiException(
          InternalErrorCodes.NO_RECORD_FOUND, ERROR_EXAMID, new String[] {Long.toString(examId)});
    }
  }

  public ActivitySummary getStudentActivitySummary(String userName) {
    var optionalUser = userRepository.findByAuthUserId(userName);
    if (optionalUser.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.CannotFindUser", new String[] {userName});
    }
    var user = optionalUser.get();
    long studentId = user.getStudentInfo().getId();
    String name = user.getFirstName() + " " + user.getLastName();
    long practiceCount = examRepository.findExamCountByStudentId(studentId, PRACTICE_EXAM);
    long testCount = examRepository.findExamCountByStudentId(studentId, TEST_EXAM);

    var grade = contentService.getGradeById(user.getStudentInfo().getClassId()).getName();
    var sections = List.of(user.getStudentInfo().getSection().getUuid().toString());
    long examQuestionCount = examRepository.getTotalExamQuestionCountNotCompleted(studentId);

    return ActivitySummary.builder()
        .authUserId(user.getAuthUserId())
        .name(name)
        .totalEventCount(0L)
        .totalTestCount(testCount)
        .totalPracticeCount(practiceCount)
        .sectionUuid(sections)
        .studentId(studentId)
        .grade(grade)
        .gradeId(user.getStudentInfo().getClassId())
        .boardId(user.getStudentInfo().getBoardId())
        .totalExamQuestionCountNotCompleted(examQuestionCount)
        .build();
  }

  public List<Map<String, Object>> trafficSignal(
      long chapterId, long subjectId, long examDifficultyLevelId) {
    long gradeId = authService.getStudentDetails().getStudentInfo().getClassId();
    long studentId = authService.getStudentDetails().getStudentInfo().getId();

    List<Map<String, Object>> subTopics = strapiService.getSubTopic();

    subTopics.forEach(
        subTopic -> {
          var subTopicId = Long.parseLong(subTopic.get("id").toString());
          subTopic.put(
              "isVisited",
              examRepository.findByStudentIdSubjectIdSubTopicIdChapterId(
                      studentId, subTopicId, examDifficultyLevelId)
                  != 0);
          subTopic.put(
              "isCompletedTextExam",
              examRepository.findByStudentIdSubjectIdExamTypeIdChapterId(
                      studentId, chapterId, examDifficultyLevelId)
                  > 70);
        });

    return subTopics;
  }

  @SneakyThrows
  public void mergePdfAnnotations(long examId) {
    var exam = examRepository.findById(examId);

    if (exam.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, ERROR_EXAMID, new String[] {Long.toString(examId)});
    }

    var destPath =
        uploadService.constructAnswerSheetFilePath(
            exam.get(), String.format("%s.pdf", exam.get().getId()));

    var annotationSaver = new WexlAnnotationSaver();
    var fileInputStream = storageService.getInputStream(destPath);
    var annotations = getAnnotationsAsJson(examId);

    try (PDDocument pdDoc = annotationSaver.applyAnnotations(fileInputStream, annotations)) {
      var outputStream = new ByteArrayOutputStream();
      pdDoc.save(outputStream);
      var inputStream = new ByteArrayInputStream(outputStream.toByteArray());
      storageService.uploadFile(inputStream, destPath, outputStream.size());
    } catch (Exception exp) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.PDFAnnotations", exp);
    }
  }

  private JSONArray getAnnotationsAsJson(long examId) throws JsonProcessingException {
    var pdfAnnotations = annotationService.getAnnotationsByDocId(String.valueOf(examId));
    var json = new ObjectMapper().writeValueAsString(pdfAnnotations);
    return new JSONArray((new JSONObject(json)).get("annotations").toString());
  }

  public List<UserUsageDetail> getStudentScreenTime(
      String studentAuthId, long fromDate, long toDate) {
    var fromTime = fromDate / 1000L;
    var toTime = toDate / 1000L;

    return examRepository.getExamsAttendedByStudent(studentAuthId, fromTime, toTime);
  }

  public ExamResponse createRevisionExam(ExamRequest examRequest) {
    log.debug("Revision Exam Api is called");
    var exam = examFactory.createRevision(examRequest.getUuid());
    exam.setNoOfQuestions(exam.getNoOfQuestions());
    return buildExamUsingChapter(exam, examRequest);
  }

  public ExamResponse createTaskPractice(Task task) {
    Exam exam = examFactory.createTaskPracticeExam(task.getId());
    exam.setExamDifficultyLevelId(1);
    exam.setAllowedDuration(Constants.ALLLOWED_DURATION);
    exam.setChapterId(task.getChapterId());
    exam.setSubTopicId(task.getSubtopicId());
    exam.setSubtopicName(task.getSubtopicName());
    exam.setSubtopicSlug(task.getSubtopicSlug());
    exam.setChapterName(task.getChapterName());
    exam.setChapterSlug(task.getChapterSlug());
    exam.setSubjectId(task.getSubjectId());
    exam.setSubjectSlug(task.getSubjectSlug());
    exam.setSubjectName(task.getSubjectName());
    exam.setNoOfQuestions(task.getQuestionCount());
    exam.setAssessmentId(
        Objects.nonNull(task.getTestDefinition()) ? (int) task.getTestDefinition().getId() : null);
    exam.setTotalMarks(
        Objects.nonNull(task.getTestDefinition())
            ? (float) task.getTestDefinition().getTotalMarks()
            : null);

    return examTransformer.mapExamToExamResponse(examRepository.save(exam));
  }

  public ExamResponse createTaskTestExam(Task task) {

    Exam exam = examFactory.createTaskTestExam(task.getId());
    exam.setNoOfQuestions(task.getQuestionCount());
    return buildExamUsingChapter(exam, buildExamRequest(task));
  }

  private ExamRequest buildExamRequest(Task task) {
    return ExamRequest.builder()
        .examDifficultyLevelId(1)
        .examType(TEST_EXAM)
        .chapterId(task.getChapterId())
        .isAllChapterSelected(false)
        .subjectId(task.getSubjectId())
        .build();
  }

  public List<ExamDto> findExamsByStudent(String orgSlug, String authUserId) {
    final User user = userRepository.findByAuthUserIdAndOrganization(authUserId, orgSlug);
    if (Objects.isNull(user)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound");
    }

    final List<Exam> exams =
        examRepository.findTop100ByStudentAndMarksScoredNotNullAndEndTimeNotNullOrderByEndTimeDesc(
            user.getStudentInfo());

    return exams.stream()
        .map(
            exam ->
                ExamDto.builder()
                    .examId(exam.getId())
                    .marksScored(exam.getMarksScored())
                    .scheduleId(
                        exam.getScheduleTest() != null ? exam.getScheduleTest().getId() : null)
                    .completedAt(
                        DateTimeUtil.convertIso8601ToEpoch(exam.getEndTime().toLocalDateTime()))
                    .subject(exam.getSubjectName())
                    .chapter(exam.getChapterName())
                    .percentage(
                        MathUtil.calculatePercentage(exam.getMarksScored(), exam.getTotalMarks()))
                    .totalMarks(exam.getTotalMarks())
                    .type(getExamType(exam.getExamType()))
                    .subtopic(exam.getSubtopicName())
                    .testName(
                        exam.getTestDefinition() != null
                            ? exam.getTestDefinition().getTestName()
                            : null)
                    .corrected(exam.isCorrected())
                    .build())
        .toList();
  }

  public ExamAnalysis.AnswerReAnalysis updateExamAnswers(
      ExamAnalysis.AnswerReAnalysis request, String orgSlug, Long examAnswerId) {
    try {
      if (request.answer() != null) {
        var newRequest = refreshAnalysis(request);
        var aiResponse = gptQuestionAnalysis.reAnalysis(newRequest);
        var examAnswer =
            studentAnswerRepository
                .findById(examAnswerId)
                .orElseThrow(
                    () ->
                        new ApiException(
                            InternalErrorCodes.NO_RECORD_FOUND,
                            "Exam answer not found for ID: " + examAnswerId));
        examAnswer.setAnswer(aiResponse.answer());
        if (aiResponse.annotatedAnswer() != null && !aiResponse.annotatedAnswer().isEmpty()) {
          examAnswer.setAiAnalysis(aiResponse.annotatedAnswer());
        }
        if (aiResponse.marks() != 0L) {
          examAnswer.setAiMarks(aiResponse.marks());
        }
        studentAnswerRepository.save(examAnswer);
        return ExamAnalysis.AnswerReAnalysis.builder()
            .answer(examAnswer.getAnswer())
            .annotatedAnswer(examAnswer.getAiAnalysis())
            .marks(examAnswer.getAiMarks())
            .build();
      } else {
        log.info("Not Attempted answer for examAnswerId: {}", examAnswerId);
        return ExamAnalysis.AnswerReAnalysis.builder().build();
      }
    } catch (Exception e) {
      log.error("Error updating exam answers for ID: {}", examAnswerId, e);
      throw e;
    }
  }

  public ExamAnalysis.AnswerReAnalysis refreshAnalysis(ExamAnalysis.AnswerReAnalysis request) {
    return ExamAnalysis.AnswerReAnalysis.builder().answer(request.answer()).build();
  }

  public String getExamType(Long examType) {
    return switch (examType.intValue()) {
      case (int) PRACTICE_EXAM -> "Practice";
      case (int) TEST_EXAM -> "Test";
      case (int) SCHOOL_SCHEDULED_TEST_EXAM -> "Scheduled Test";
      case (int) SCHOOL_SURPRISE_TEST_EXAM -> "Surprise Test";
      case (int) WORKSHEET -> "Worksheet";
      case (int) ASSIGNMENT_EXAM -> "Assignment";
      case (int) COURSE_ASSIGNMENT -> "Course Assignment";
      case (int) COURSE_TEST -> "Course Test";
      case (int) MOCK_TEST -> "Mock Test";
      case (int) LIVE_WORKSHEET -> "Live Worksheet";
      case (int) COURSE_MOCK_TEST -> "Course Mock Test";
      case (int) ELP_EXAM -> "ELP Exam";
      case (int) BET_EXAM -> "BET Exam";
      case (int) EBC_EXAM -> "EBC Exam";
      default -> "Revision";
    };
  }

  public void updateExamAnswersById(ExamAnswerDto.ExamAnswerUpdateRequest updateRequests) {
    var teacherUser = authService.getUserDetails();
    List<ExamAnswer> examAnswerList = new ArrayList<>();
    for (var request : updateRequests.updateRequest()) {
      if (QuestionType.SUBJECTIVE.equals(request.questionType())) {
        var examAnswerEntity = validateExamAnswer(request.examAnswerId());
        examAnswerEntity.setPrevAnswer(examAnswerEntity.getAnswer());
        examAnswerEntity.setAnswer(request.updatedAnswer());
        examAnswerEntity.setUpdatedBy(teacherUser.getAuthUserId());
        examAnswerList.add(examAnswerEntity);
      }
    }
    examAnswerRepository.saveAll(examAnswerList);
  }

  private ExamAnswer validateExamAnswer(Long id) {
    return examAnswerRepository
        .findById(id)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ExamAnswerNotFound"));
  }

  public void updateExamAnswersMarks(long examId, List<ExamQuestionsMarks> examQuestionsMarks) {
    final List<ExamAnswer> examAnswers = studentAnswerRepository.findByExamId(examId);
    if (!CollectionUtils.isEmpty(examAnswers)) {
      examAnswers.forEach(
          examAnswer -> {
            final ExamQuestionsMarks examQuestionMarks =
                examQuestionsMarks.stream()
                    .filter(q -> examAnswer.getQuestionUuid().equals(q.getQuestionUuid()))
                    .findAny()
                    .orElse(null);
            if (Objects.nonNull(examQuestionMarks)) {
              examAnswer.setMarksScoredPerQuestion(examQuestionMarks.getMarksScored());
              examAnswer.setAiMarks(examQuestionMarks.getMarksScored());
            }
          });
      studentAnswerRepository.saveAll(examAnswers);
    }
  }
}
