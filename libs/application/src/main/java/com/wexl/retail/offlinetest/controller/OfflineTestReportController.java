package com.wexl.retail.offlinetest.controller;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.commons.storage.S3FileUploadResult;
import com.wexl.retail.offlinetest.dto.OfflineTestScheduleDto;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplateType;
import com.wexl.retail.offlinetest.service.OfflineTestReportService;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.offlinetest.service.ReportCardTemplateService;
import com.wexl.retail.offlinetest.service.ReportCreationServiceHelper;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequiredArgsConstructor
public class OfflineTestReportController {

  private final OfflineTestReportService offlineTestReportService;
  private final OfflineTestScheduleService offlineTestScheduleService;
  private final ReportCreationServiceHelper reportCreationServiceHelper;
  private final ReportCardTemplateService reportCardTemplateService;
  private final UserRoleHelper userRoleHelper;
  private final AuthService authService;

  @GetMapping(
      value =
          "/orgs/{orgSlug}/offline-test-schedules/{offlineTestScheduleId}/students/{studentId}/reports",
      produces = "application/pdf")
  public byte[] getStudentReport(
      @PathVariable String orgSlug,
      @PathVariable Long offlineTestScheduleId,
      @PathVariable Long studentId) {
    return offlineTestReportService.generateSingleExamReport();
  }

  @PostMapping(
      value = "/orgs/{orgSlug}/report-card-templates/{reportCardTemplateId}",
      produces = "application/pdf")
  public byte[] getStudentReportCard(
      @PathVariable String orgSlug,
      @PathVariable Long reportCardTemplateId,
      @RequestBody ReportCardDto.Request request) {
    var user = authService.getUserDetails();
    if (userRoleHelper.isManager(user) && Objects.nonNull(request.childOrg())) {
      return offlineTestReportService.getStudentReportByOfflineTestDefinition(
          request.childOrg(), reportCardTemplateId, request);
    }
    return offlineTestReportService.getStudentReportByOfflineTestDefinition(
        orgSlug, reportCardTemplateId, request);
  }

  @GetMapping(
      value =
          "/orgs/{orgSlug}/sections/{sectionUuid}/report-card-templates/{reportCardTemplateId}/all-students")
  public void generateStudentsOverallEyReportCard(
      @PathVariable String orgSlug,
      @PathVariable String sectionUuid,
      @PathVariable Long reportCardTemplateId,
      ReportCardDto.Request request) {
    reportCardTemplateService.generateStudentsOverallEyReportCard(
        orgSlug, sectionUuid, reportCardTemplateId, request);
  }

  @PostMapping(
      value =
          "/orgs/{orgSlug}/sections/{sectionUuid}/report-card-templates/{reportCardTemplateId}/all-students")
  public void generateStudentsOverallAdmitCard(
      @PathVariable String orgSlug,
      @PathVariable Long reportCardTemplateId,
      @PathVariable String sectionUuid,
      @RequestParam(value = "offline_test_definition_id") Long offlineTestDefinitionId) {
    reportCardTemplateService.generateStudentsOverallAdmitCard(
        orgSlug, sectionUuid, reportCardTemplateId, offlineTestDefinitionId);
  }

  @GetMapping(value = "/orgs/{orgSlug}/overall-admit-card")
  public String getOverallAdmitCard(
      @PathVariable String orgSlug,
      @RequestParam(value = "offline_test_definition_id") Long offlineTestDefinitionId) {
    return reportCardTemplateService.getAllStudentsReportCard(orgSlug, offlineTestDefinitionId);
  }

  @PostMapping(value = "/public/report/pdf", produces = "application/pdf")
  public byte[] evaluateReportPdf(
      @RequestPart(value = "template") MultipartFile template,
      @RequestPart(value = "data") MultipartFile data)
      throws IOException {
    if (template == null || data == null) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidRequest");
    }

    return reportCreationServiceHelper.uploadAndProcess(template.getBytes(), data.getBytes());
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/orgs/{orgSlug}/teachers/{teacherAuthId}/report-cards")
  public List<OfflineTestScheduleDto.ReportCardResponse> reportCard(
      @RequestParam(value = "offline_test_definition_id", required = false)
          List<Long> offlineTestDefinitionId,
      @PathVariable String orgSlug,
      @RequestParam(value = "board_slug", required = false) String boardSlug,
      @RequestParam(value = "grade_slug", required = false) String gradeSlug,
      @RequestParam(value = "section_uuid", required = false) String sectionUuid,
      @RequestParam(value = "type", required = false) ReportCardTemplateType type,
      @RequestParam(value = "report_card_template", required = false) Long reportCardTemplate,
      @RequestParam(value = "child_org", required = false) String childOrgSlug) {
    return offlineTestScheduleService.reportCard(
        offlineTestDefinitionId,
        boardSlug,
        gradeSlug,
        sectionUuid,
        type,
        reportCardTemplate,
        Objects.isNull(childOrgSlug) ? orgSlug : childOrgSlug);
  }

  @GetMapping("/orgs/{orgSlug}/report-cards:cache")
  public S3FileUploadResult downloadOverallReportCard(
      @PathVariable String orgSlug,
      @RequestParam String sectionUuid,
      @RequestParam Long templateId) {
    return reportCardTemplateService.downloadReport(orgSlug, sectionUuid, templateId);
  }

  @GetMapping("/orgs/{orgSlug}/reportcard-history")
  public byte[] getStudentReportCards(
      @PathVariable String orgSlug,
      @RequestParam String studentAuthId,
      @RequestParam String academicYearSlug,
      @RequestParam Long templateId,
      @RequestParam(value = "otdId", required = false) Long otdId) {
    return offlineTestReportService.getStudentReportCardByAcademicYear(
        orgSlug, studentAuthId, academicYearSlug, templateId, otdId);
  }
}
