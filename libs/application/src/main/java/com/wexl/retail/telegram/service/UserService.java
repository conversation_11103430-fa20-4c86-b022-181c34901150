package com.wexl.retail.telegram.service;

import static com.wexl.retail.util.Constants.DEFAULT_COUNTRY_CODE;
import static org.apache.logging.log4j.util.Strings.EMPTY;

import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.mobile.repository.CountryCodeRepository;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.telegram.util.Constants;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {
  private final UserRepository userRepository;
  private final CountryCodeRepository countryCodeRepository;

  public static Map<String, Object> mapFromUser(User user) {

    Map<String, Object> result = new HashMap<>();

    result.put("User Name", user.getUserName());
    result.put("First Name", user.getFirstName());
    result.put("Last Name", user.getLastName());
    result.put("Auth UserID", user.getAuthUserId());
    result.put(Constants.EMAIL, user.getEmail());
    result.put("ID", user.getId());
    result.put("Roles", UserRoleHelper.get().getPermissionsForUser(user));
    if (AuthUtil.isStudent(user)) {
      Student student = user.getStudentInfo();
      result.put("Student ID", student.getId());
      result.put("Student Board", student.getBoardId());
      result.put("Student Grade", student.getClassId());
      String sections = student.getSection().getName();
      result.put("Student Section", sections);
    }

    return result;
  }

  public String getUserDetails(String authUserId) {
    User user = userRepository.getUserByAuthUserId(authUserId);
    if (user == null) {
      return "User with [" + authUserId + "] not found";
    }
    String userName = user.getUserName();
    String firstName = user.getFirstName();
    String lastName = user.getLastName();
    String email = user.getEmail();
    long id = user.getId();
    boolean isStudent = UserRoleHelper.get().isStudent(user);
    Student student = user.getStudentInfo();
    String basicDetails =
        add(key("User ID"), key("User Name"), key("First Name"), key("Last Name"), key("Email"))
            .formatted(id, userName, firstName, lastName, email);
    if (isStudent) {
      long studentId = student.getId();
      long boardId = student.getBoardId();
      String studentDetails = add(key("Student ID"), key("Board ID")).formatted(studentId, boardId);
      return basicDetails + studentDetails;
    }
    return basicDetails;
  }

  @SneakyThrows
  public User findUserById(long userId) {
    return userRepository
        .findById(userId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "errror.UserNotFound",
                    new String[] {Long.toString(userId)}));
  }

  private String key(String keyName) {
    return "<b>" + keyName + "</b>:%s\n";
  }

  private String add(String... items) {
    Optional<String> appended = Arrays.stream(items).reduce((a, b) -> a + b);
    return appended.orElse("");
  }

  public String getNameByUserInfo(User userInfo) {
    if (Objects.isNull(userInfo)) {
      return EMPTY;
    }
    String firstName = Objects.nonNull(userInfo.getFirstName()) ? userInfo.getFirstName() : "";
    String lastName = Objects.nonNull(userInfo.getLastName()) ? userInfo.getLastName() : "";

    return firstName + " " + lastName;
  }

  public List<String> getFullNamesByUsers(List<User> users) {
    return Objects.requireNonNull(users).isEmpty()
        ? new ArrayList<>()
        : users.stream().map(this::getNameByUserInfo).toList();
  }

  public String validateCountryCode(String countryCode) {
    if (Objects.nonNull(countryCode)) {
      var optionalCountryCode = countryCodeRepository.findByCode(countryCode);
      if (optionalCountryCode.isPresent()) {
        return optionalCountryCode.get().getCode();
      }
    }
    return DEFAULT_COUNTRY_CODE;
  }
}
