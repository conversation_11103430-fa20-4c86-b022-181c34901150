package com.wexl.retail.maths.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Entity
@Data
@Table(name = "ccss_domains")
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class CcssDomain extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String name;
  private String slug;
  @ManyToOne private CcssGrade ccssGrade;

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "ccssDomain", cascade = CascadeType.ALL)
  private List<CcssCluster> ccssClusters;
}
