package com.wexl.retail.metrics.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

public record WeeklyMlpReport() {
  @Builder
  public record MlpResponse(
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("mlp_name") String mlpName,
      @JsonProperty("chapter_name") String chapterName,
      @JsonProperty("subtopic_name") String subtopicName,
      @JsonProperty("total_students") long totalStudents,
      @JsonProperty("students_attempted") long studentsAttempted,
      @JsonProperty("average_score") double averageScore,
      @JsonProperty("average_attempted") long averageAttempted) {}
}
