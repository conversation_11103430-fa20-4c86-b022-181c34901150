package com.wexl.retail.proctoring.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.proctoring.model.ProctoringStatus;
import lombok.Builder;

public record ProctoringDto() {

  @Builder
  public record Response(
      Long proctoringSessionId, Long startTime, ProctoringStatus status, Long examId) {}

  public record Request(@JsonProperty("tss_uuid") String tssUuid) {}

  public record UpdateRequest(ProctoringStatus status) {}

  @Builder
  public record ReportStatusRequest(
      @JsonProperty("test_id") String testId, @JsonProperty("student_id") String studentId) {}

  public record ProctoringResult(
      @JsonProperty("Success") String success,
      @JsonProperty("Message") String message,
      @JsonProperty("Responsedata") ResponseData responseData) {}

  public record ResponseData(
      @JsonProperty("test_id") String testId,
      @JsonProperty("student_id") String studentId,
      String status,
      @JsonProperty("video_url") String videoUrl,
      String message) {}

  @Builder
  public record ProctoringSessionResponse(
      ProctoringStatus status,
      String url,
      @JsonProperty("end_time") Long endTime,
      String message) {}
}
