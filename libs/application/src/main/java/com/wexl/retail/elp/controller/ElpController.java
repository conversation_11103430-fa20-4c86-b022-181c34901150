package com.wexl.retail.elp.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.commons.security.annotation.IsTeacher;
import com.wexl.retail.elp.dto.DashBoardResponse;
import com.wexl.retail.elp.dto.ElpDto;
import com.wexl.retail.elp.dto.ElpExamResponse;
import com.wexl.retail.elp.dto.WordDto;
import com.wexl.retail.elp.service.ElpService;
import com.wexl.retail.speech.dto.SpeechEvaluation.SpeechResponse;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.dto.QuestionDto;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
public class ElpController {

  private final ElpService elpService;

  @IsOrgAdmin
  @PostMapping("/orgs/wexl-internal/elps-initialize")
  public void initializeElp(@RequestBody ElpDto.Initialize request) {
    elpService.initializeElp(request.orgSlug(), request.boardSlug());
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/orgs/{orgSlug}/elps")
  public List<ElpDto.Chapter> getTeacherElps(
      @PathVariable String orgSlug,
      @RequestParam("grade_slug") String gradeSlug,
      @RequestParam(required = false) String boardSlug) {
    return elpService.getElps(orgSlug, gradeSlug, boardSlug);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/orgs/{orgSlug}/elps")
  public void triggerElp(@PathVariable String orgSlug, @RequestBody ElpDto.TriggerRequest request) {
    elpService.triggerElp(orgSlug, request.gradeSlug(), request.taskIds());
  }

  @GetMapping("/orgs/{orgSlug}/students/{studentAuthId}/elps")
  public List<ElpDto.Chapter> getStudentElps(@PathVariable String studentAuthId) {
    return elpService.getStudentElps(studentAuthId);
  }

  @IsStudent
  @PostMapping("/orgs/{orgSlug}/task-insts/{taskInstId}/elp-exams")
  public ElpExamResponse startElpExam(@PathVariable String orgSlug, @PathVariable long taskInstId) {
    return elpService.startElpExam(taskInstId);
  }

  @IsStudent
  @PostMapping("/orgs/{orgSlug}/elp-exams/{examId}/questions")
  public QuestionDto.QuestionResponse getExamQuestionResponse(
      @PathVariable String orgSlug, @PathVariable long examId) {
    return elpService.getExamQuestionResponse(examId);
  }

  @IsStudent
  @PostMapping("/orgs/{orgSlug}/elp-exams:submit")
  public void submitElpTest(@RequestBody ElpDto.StudentAnswerRequest studentAnswerRequest) {
    elpService.submitElpTest(studentAnswerRequest);
  }

  @IsStudent
  @PostMapping("/orgs/{orgSlug}/elp-exam:answers")
  public QuestionDto.ValidateAnswerResponse saveExamAnswer(
      @RequestBody ElpDto.StudentExamAnswerRequest examAnswerRequest,
      @PathVariable String orgSlug) {
    return elpService.saveExamAnswer(examAnswerRequest, orgSlug);
  }

  @GetMapping("/orgs/{orgSlug}/elps-tasks")
  public ElpDto.TaskResponse getTaskDetails(@RequestParam("taskId") List<Long> taskIds) {
    return elpService.getTaskDetails(taskIds);
  }

  @GetMapping("/orgs/{orgSlug}/elp-exams/{examId}/results")
  public QuestionDto.StudentResultsResponse getExamResult(@PathVariable long examId) {
    return elpService.getExamResult(examId);
  }

  @GetMapping("/orgs/{orgSlug}/elp-exams/words/{word}")
  public WordDto.WordResponse getWordMeaning(@PathVariable String word) {
    return elpService.getWordMeaning(word);
  }

  @PostMapping("/orgs/{orgSlug}/elp-exams/{examId}/speech-evaluation")
  public SpeechResponse evaluateSpeakingTest(
      @PathVariable String orgSlug,
      @PathVariable long examId,
      @RequestParam("question_uuid") String questionUuid) {
    return elpService.evaluateSpeakingTest(examId, questionUuid);
  }

  @DeleteMapping("/orgs/{orgSlug}/elp-exams/{examId}/question/{questionUuid}/speech-evaluation")
  public void deleteSpeakingEntry(@PathVariable Long examId, @PathVariable String questionUuid) {
    elpService.deleteSpeakingEntry(examId, questionUuid);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/orgs/{orgSlug}/task-insts/{taskInstId}/remarks")
  public void addRemarks(@RequestBody ElpDto.Remarks request, @PathVariable long taskInstId) {
    elpService.addRemarks(taskInstId, request);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/orgs/{orgSlug}/config-elp")
  public TestDefinition configElp(
      @RequestBody ElpDto.ConfigElp request, @PathVariable String orgSlug) {
    return elpService.configElp(request, orgSlug);
  }

  @IsStudent
  @PostMapping("/orgs/{orgSlug}/elp-student:switch")
  public void switchStudent(
      @Valid @RequestBody ElpDto.SwitchElpStudentRequest request, @PathVariable String orgSlug) {
    elpService.switchElpStudent(request, orgSlug);
  }

  @IsStudent
  @GetMapping("/orgs/{orgSlug}/students/{studentAuthId}/dashboard")
  public DashBoardResponse.DashBoardDetails getStudentElpAndMyTestDashboard(
      @PathVariable String orgSlug, @PathVariable String studentAuthId) {
    return elpService.getStudentElpDashboard(orgSlug, studentAuthId);
  }

  @PutMapping("/orgs/{orgSlug}/elp-tasks/migration")
  public void migrationElpTasks(
      @PathVariable String orgSlug, @RequestBody ElpDto.ElpTaskMigrationRequest request) {
    elpService.migrateElpTasks(orgSlug, request);
  }

  @IsTeacher
  @GetMapping("/orgs/{orgSlug}/teachers/{teacherAuthUserId}/teacher-elps")
  public List<ElpDto.TeacherElps> getTeacherElps(
      @PathVariable String orgSlug, @PathVariable String teacherAuthUserId) {
    return elpService.getTeacherElps(orgSlug, teacherAuthUserId);
  }

  @GetMapping("/orgs/{orgSlug}/phonetics")
  public ElpDto.PhoneticsResponseList getPhonetics(@RequestParam("type") String type) {
    List<ElpDto.PhoneticsResponse> phonetics = elpService.getPhonetics(type);
    return new ElpDto.PhoneticsResponseList(phonetics);
  }

  @GetMapping("/orgs/{orgSlug}/phonetics/{type}/sounds")
  public ElpDto.PhoneticsData getPhoneticsSounds(
      @PathVariable("type") String type, @RequestParam("sound") String sound) {
    return elpService.getPhoneticsSounds(type, sound);
  }

  @PostMapping("org/{orgSlug}/migrate-phonetics")
  public void migratePhonetics() {
    elpService.migratePhonetics();
  }
}
