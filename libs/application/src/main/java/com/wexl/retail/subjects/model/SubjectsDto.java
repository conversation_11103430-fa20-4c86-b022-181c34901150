package com.wexl.retail.subjects.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;

public record SubjectsDto() {

  public record BulkRequest(
      @NotNull @JsonProperty("username") String username,
      @NotNull @JsonProperty("subjects") List<String> subjects) {}

  public record Request(
      String name,
      @JsonProperty("type") SubjectsTypeEnum type,
      @JsonProperty("category") SubjectsCategoryEnum subjectsCategoryEnum,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("wexl_subject_slug") String wexlSubjectSlug,
      @JsonProperty("seq_no") Long SeqNo,
      Boolean status) {}

  @Builder
  public record Response(
      Long id,
      @JsonProperty("display_name") String displayName,
      String name,
      @JsonProperty("type") SubjectsTypeEnum type,
      @JsonProperty("category") SubjectsCategoryEnum subjectsCategoryEnum,
      @JsonProperty("board_name") String boardName,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_id") Integer gradeId,
      @JsonProperty("grade_order") Integer gradeOrder,
      @JsonProperty("wexl_subject_slug") String wexlSubjectSlug,
      @JsonProperty("seq_no") Long SeqNo,
      @JsonProperty("student_associated") Boolean studentsAssociated,
      @JsonProperty("status") Boolean status) {}

  public record StudentsRequest(@JsonProperty("student_id") List<Long> studentIdsList) {}

  @Builder
  public record StudentsResponse(
      @JsonProperty("summary") Response response, List<Students> students) {}

  @Builder
  public record Students(
      @JsonProperty("id") Long id,
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("name") String name,
      @JsonProperty("roll_number") String rollNumber,
      @JsonProperty("class_roll_number") String classRollNumber,
      @JsonProperty("auth_user_id") String authUserId,
      @JsonProperty("section_name") String sectionName) {}

  @Builder
  public record StudentSubjectMetadataResponse(
      @JsonProperty("student_name") String studentName,
      @JsonProperty("grade") String grade,
      @JsonProperty("section") String section,
      @JsonProperty("subject_metadata") List<SubjectMetadataResponse> subjects) {}

  @Builder
  public record SubjectMetadataResponse(String subject, Boolean isAssigned) {}
}
