package com.wexl.retail.student.worksheets.controller;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.commons.security.annotation.LegacyApi;
import com.wexl.retail.student.worksheets.dto.WorksheetResponse;
import com.wexl.retail.student.worksheets.dto.WorksheetSummaryResponse;
import com.wexl.retail.student.worksheets.service.WorksheetService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@IsStudent
@RequestMapping("/orgs/{org}/worksheets/{provider_slug}")
@RequiredArgsConstructor
public class WorksheetController {

  private final AuthService authService;
  private final WorksheetService worksheetService;

  @GetMapping("/chapters")
  @LegacyApi
  public List<WorksheetResponse> getChapterWorksheets(
      @PathVariable("provider_slug") String providerSlug,
      @RequestParam("slug") String chapterSlug) {
    long studentId = authService.getStudentDetails().getStudentInfo().getId();
    return worksheetService.getChapterWorksheets(providerSlug, chapterSlug, studentId);
  }

  @GetMapping("/subjects")
  @LegacyApi
  public List<WorksheetSummaryResponse> getChapterWiseWorksheetSummary(
      @PathVariable("provider_slug") String providerSlug,
      @RequestParam("slug") String subject,
      @RequestParam String grade,
      @RequestParam String board) {
    long studentId = authService.getStudentDetails().getStudentInfo().getId();
    return worksheetService.getChapterWiseWorksheetSummary(
        providerSlug, subject, grade, board, studentId);
  }
}
