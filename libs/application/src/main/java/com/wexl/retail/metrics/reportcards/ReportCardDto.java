package com.wexl.retail.metrics.reportcards;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;
import lombok.Builder;

public record ReportCardDto() {

  @Builder
  public record ReportCardStudents(
      @JsonProperty("name") String name,
      @JsonProperty("section") String section,
      @JsonProperty("academic_year") String academicYear,
      @JsonProperty("organization") String organization,
      @JsonProperty("auth_user_id") String authUserId,
      @JsonProperty("roll_number") String rollNumber,
      @JsonProperty("admission_number") String admissionNumber,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("total_marks") Long totalMarks,
      @JsonProperty("exam_type") String examType,
      @JsonProperty("exam_id") Long examId,
      @JsonProperty("offline_test_schedule_student") Long offlineTestScheduleStudent,
      @JsonProperty("subject_details") Map<String, String> subjectDetails) {}

  @Builder
  public record SubjectWiseTopper(
      @JsonProperty("student_name") String name,
      @JsonProperty("admission_number") String admissionNumber,
      @JsonProperty("section") String section,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("exam_name") String examName,
      @JsonProperty("exam_id") Long examId,
      @JsonProperty("marks") Long marks) {}

  @Builder
  public record SubjectWiseMarks(
      @JsonProperty("student_name") String name,
      @JsonProperty("roll_number") String rollNumber,
      @JsonProperty("section") String section,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("exam_details") List<ExamDetails> ExamDetails) {}

  @Builder
  public record ExamDetails(
      @JsonProperty("exam_name") String examName,
      @JsonProperty("exam_id") Long examId,
      @JsonProperty("test_schedule_id") Long testScheduleId,
      @JsonProperty("marks") String marks) {}

  @Builder
  public record ClassMean(
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("section") String section,
      @JsonProperty("class_average") Long classAverage) {}

  @Builder
  public record SubjectMean(
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("exam_details") Map<String, Object> examDetails) {}

  @Builder
  public record StudentReportCard(
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("total_marks") Long totalMarks,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("my_marks") String myMarks,
      @JsonProperty("class_mean") String classMean,
      @JsonProperty("topper_marks") String topperMarks) {}
}
