package com.wexl.retail.student.profile;

import com.wexl.retail.model.Gender;
import com.wexl.retail.student.registration.dto.StudentAttributeData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
@AllArgsConstructor
public class StudentProfileResponse {
  private String studentFirstName;
  private String studentLastName;
  private String studentUserName;
  private String schoolName;
  private String gradeName;
  private String sectionName;
  private String studentEmail;
  private String orgSlug;
  private int boardId;
  private int gradeId;
  private String parentEmail;
  private String parentFirstName;
  private String parentLastName;
  private String parentMobileNumber;
  private String profileImageUrl;
  private Boolean isDefaultParent;
  private Gender gender;
  private StudentAttributeData attributes;
}
