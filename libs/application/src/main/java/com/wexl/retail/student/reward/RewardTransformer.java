package com.wexl.retail.student.reward;

import org.springframework.stereotype.Component;

@Component("RewardStudentTransformer")
public class RewardTransformer {

  protected RewardTransaction mapRewardRequestToTransaction(
      RewardTransactionRequest rewardTransactionRequest) {
    var rewardTransaction = new RewardTransaction();
    rewardTransaction.setPoints(rewardTransaction.getPoints());
    return rewardTransaction;
  }

  protected RewardTransactionResponse mapTransactionToResponse(
      RewardTransaction rewardTransaction) {
    return RewardTransactionResponse.builder().points(rewardTransaction.getPoints()).build();
  }
}
