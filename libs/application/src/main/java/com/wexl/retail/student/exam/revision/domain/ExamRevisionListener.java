package com.wexl.retail.student.exam.revision.domain;

import com.wexl.retail.student.answer.ExamRevisionEvent;
import com.wexl.retail.student.exam.revision.service.ExamRevisionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
public class ExamRevisionListener implements ApplicationListener<ExamRevisionEvent> {

  @Autowired private ExamRevisionService examRevisionService;

  @Value("${app.switchOffExamRevision}")
  private Boolean switchOffExamRevision;

  @Override
  public void onApplicationEvent(ExamRevisionEvent event) {
    Object source = event.getSource();

    if (source instanceof ExamRevisionRequest examRevisionRequest && !switchOffExamRevision) {
      examRevisionService.addQuestionToExamRevision((examRevisionRequest));
    }
  }
}
