package com.wexl.retail.task.dto;

import java.time.LocalDateTime;

public interface StudentScheduleResult {

  Long getClassroomId();

  String getClassroomName();

  Long getScheduleId();

  Long getScheduleInstId();

  String getScheduleInstName();

  LocalDateTime getStartTime();

  LocalDateTime getEndTime();

  String getStatus();

  Long getMeetingId();

  String getMeetingName();

  String getDisplayName();

  String getMeetingLink();

  String getDayOfWeek();

  String getAttendanceStatus();
}
