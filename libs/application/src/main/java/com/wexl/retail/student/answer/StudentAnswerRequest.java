package com.wexl.retail.student.answer;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StudentAnswerRequest {
  private int examId;

  @JsonProperty("examQuestion")
  private List<ExamQuestion> examQuestions;

  public List<ExamQuestion> getExamQuestions() {
    if (Objects.isNull(examQuestions)) {
      return new ArrayList<>();
    }
    return examQuestions;
  }
}
