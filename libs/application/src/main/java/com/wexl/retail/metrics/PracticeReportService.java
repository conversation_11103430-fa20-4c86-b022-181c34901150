package com.wexl.retail.metrics;

import static com.wexl.retail.commons.util.DateTimeUtil.getEpochFromStringDate;

import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.dto.PracticeReportData;
import com.wexl.retail.util.Constants;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PracticeReportService {

  @Autowired private PracticeReportRepository practiceReportRepository;

  public List<GenericMetricResponse> getPracticeTestByInstitute(
      String org, List<String> subject, List<String> gradeList, Integer timePeriod) {
    var date = LocalDate.now().minusWeeks(timePeriod);
    var practiceTestReportsList =
        practiceReportRepository.getPracticeTestReport(
            org, date, subject, gradeList, Constants.PRACTICE_EXAM);

    return practiceTestReportsList.stream()
        .map(
            practiceTestReport ->
                GenericMetricResponse.builder()
                    .date(getEpochFromStringDate(date.toString()))
                    .summary(summary(org, subject, gradeList, practiceTestReport.getSection()))
                    .data(data(practiceTestReport))
                    .build())
        .toList();
  }

  private Map<String, Object> summary(
      String org, List<String> subjects, List<String> gradeList, String sections) {
    Map<String, Object> map = new HashMap<>();
    map.put("org", org);
    map.put("subject_list", subjects);
    map.put("grade_list", gradeList);
    map.put("section_list", sections);
    return map;
  }

  private Map<String, Object> data(PracticeReportData practiceTestData) {
    Map<String, Object> map = new HashMap<>();
    map.put("grade_name", practiceTestData.getGradeName());
    map.put("subject_name", practiceTestData.getSubject());
    map.put("section_name", practiceTestData.getSection());
    map.put("chapter_name", practiceTestData.getChapter());
    map.put("subtopic_name", practiceTestData.getSubTopic());
    map.put("questions_attempted", practiceTestData.getQuestionAttempted());
    map.put("exam_attempted", practiceTestData.getExamAttempted());
    map.put("average", practiceTestData.getAverage());
    return map;
  }
}
