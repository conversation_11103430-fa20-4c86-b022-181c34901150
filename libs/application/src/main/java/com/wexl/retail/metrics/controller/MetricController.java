package com.wexl.retail.metrics.controller;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.organization.dashboard.DashboardService;
import com.wexl.retail.organization.dto.ServicesCount;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/orgs/{orgSlug}")
public class MetricController {
  private final DashboardService dashboardService;

  @IsOrgAdmin
  @GetMapping("/activity")
  public ServicesCount getTotalServicesUsed(
      @PathVariable String orgSlug,
      @RequestParam("from_date") long fromDate,
      @RequestParam("to_date") long toDate) {
    try {
      return dashboardService.getServicesUsage(orgSlug, fromDate, toDate);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.services.organization", e);
    }
  }
}
