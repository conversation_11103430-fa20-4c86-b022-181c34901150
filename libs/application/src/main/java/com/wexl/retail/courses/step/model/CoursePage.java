package com.wexl.retail.courses.step.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "course_page")
public class CoursePage extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "course-page-sequence-generator")
  @SequenceGenerator(
      name = "course-page-sequence-generator",
      sequenceName = "course_page_seq",
      allocationSize = 1)
  private long id;

  private String title;

  @Column(columnDefinition = "TEXT")
  private String content;

  @Column(name = "org_slug")
  private String orgSlug;
}
