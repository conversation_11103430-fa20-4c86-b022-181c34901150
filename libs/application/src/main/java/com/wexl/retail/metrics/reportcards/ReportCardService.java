package com.wexl.retail.metrics.reportcards;

import static com.wexl.retail.guardian.model.GuardianRole.*;
import static com.wexl.retail.offlinetest.service.MarksUtil.toMarksLong;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceDetailRepository;
import com.wexl.retail.guardian.dto.GuardianDto;
import com.wexl.retail.guardian.model.Guardian;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.reportcards.dto.StudentHistoricalReportRepository;
import com.wexl.retail.model.Student;
import com.wexl.retail.offlinetest.model.*;
import com.wexl.retail.offlinetest.repository.*;
import com.wexl.retail.offlinetest.repository.StudentReportCard;
import com.wexl.retail.offlinetest.service.MarksUtil;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.repository.StudentAttributeValueRepository;
import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.term.repository.TermRepository;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.util.ValidationUtils;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class ReportCardService {

  private final OfflineTestDefinitionRepository offlineTestDefinitionRepository;
  private final OfflineTestScheduleRepository offlineTestScheduleRepository;
  private final ValidationUtils validationUtils;
  private final OfflineTestScheduleStudentRepository offlineTestScheduleStudentRepository;
  private final UserService userService;
  private final OfflineTestScheduleService offlineTestScheduleService;
  private final StudentAttributeValueRepository studentAttributeValueRepository;
  private final StorageService storageService;
  private final SectionAttendanceDetailRepository sectionAttendanceDetailRepository;
  private final StudentHistoricalReportRepository studentHistoricalReportRepository;
  private final StrapiService strapiService;
  private final DateTimeUtil dateTimeUtil;
  private static final String YEAR_SLUG = "23-24";
  private static final String EVS_SCIENCE = "EVS - Science";
  private static final String EVS_SOCIAL = "EVS - Social";
  private static final String DOB = "date_of_birth";
  private final List<Integer> higherGradeIds = List.of(1, 4, 5, 6, 7);
  private final PointScaleEvaluator pointScaleEvaluator;
  private final TermRepository termRepository;
  private final List<String> order =
      Arrays.asList(
          "english",
          "english-i",
          "english-ii",
          "english-bbp",
          "tamil",
          "hindi",
          "fl-hindi",
          "fl-hindi-mgcv",
          "sl-hindi",
          "sanskrit",
          "mathematics",
          "mathematics-i",
          "mathematics-ii",
          "mathematics-iii",
          "mathematics-mgcv",
          "science",
          "evs-science",
          "physics",
          "chemistry",
          "biology",
          "social",
          "evs-social",
          "history",
          "civics-and-economics",
          "geography",
          "computers");

  private final List<String> lOrder =
      Arrays.asList(
          "english-bbp", "tamil", "fl-hindi-mgcv", "mathematics-mgcv", "evs-science", "evs-social");

  private final List<String> examData = new ArrayList<>();
  private final Map<String, String> subjectData = new HashMap<>();

  public List<GenericMetricResponse> getReportCard(
      String orgSlug, List<String> gradeSlug, List<String> section, List<String> year) {
    List<GenericMetricResponse> responseList = new ArrayList<>();
    var organization = strapiService.getOrganizationBySlug(orgSlug);
    var testDefinition =
        offlineTestDefinitionRepository.getOfflineTestScheduleForReportCard(
            orgSlug, gradeSlug, section, year);
    if (testDefinition.isEmpty()) {
      return Collections.emptyList();
    }
    testDefinition.sort(Comparator.comparing(OfflineTestDefinition::getExamStartDate).reversed());
    testDefinition.forEach(
        testDef -> {
          Map<String, Object> summary = new HashMap<>();
          Map<String, Object> data = new HashMap<>();
          if (!testDef.getOfflineTestScheduleSchedule().isEmpty()) {
            List<String> subjectNames =
                testDef.getOfflineTestScheduleSchedule().stream()
                    .map(OfflineTestSchedule::getSubjectsMetaData)
                    .filter(Objects::nonNull)
                    .map(SubjectsMetaData::getName)
                    .toList();
            summary.put("title", testDef.getTitle());
            summary.put("test_definition_id", testDef.getId());
            summary.put("subjects", subjectNames);
            var students =
                testDef.getOfflineTestScheduleSchedule().stream()
                    .map(OfflineTestSchedule::getOfflineTestScheduleStudents)
                    .flatMap(List::stream)
                    .map(OfflineTestScheduleStudent::getStudentId)
                    .collect(Collectors.toSet());
            var studentData = buildStudentDetails(students, testDef, organization.getName());
            if (studentData.isEmpty()) {
              return;
            }
            data.put("student_details", studentData);
            responseList.add(GenericMetricResponse.builder().data(data).summary(summary).build());
          }
        });

    return responseList.stream().distinct().toList();
  }

  private static final String ADMISSION_NO = "admission_no";

  private List<ReportCardDto.ReportCardStudents> buildStudentDetails(
      Set<Long> students, OfflineTestDefinition testDef, String organization) {
    List<ReportCardDto.ReportCardStudents> studentsList = new ArrayList<>();

    students.forEach(
        student -> {
          var studentDetails = validationUtils.isStudentValid(student);
          var studentUser = studentDetails.getUserInfo();
          var studentSection = studentDetails.getSection();

          if (studentUser == null) {
            return;
          }
          Optional<StudentAttributeValueModel> admissionNo =
              getStudentAttributeValue(studentDetails, ADMISSION_NO);
          studentsList.add(
              ReportCardDto.ReportCardStudents.builder()
                  .name(studentUser.getFirstName() + " " + studentUser.getLastName())
                  .authUserId(studentUser.getAuthUserId())
                  .academicYear(studentDetails.getAcademicYearSlug())
                  .organization(organization)
                  .section(studentSection.getName())
                  .gradeSlug(studentSection.getGradeSlug())
                  .rollNumber(studentDetails.getClassRollNumber())
                  .admissionNumber(
                      admissionNo.map(StudentAttributeValueModel::getValue).orElse(null))
                  .gradeName(studentSection.getGradeName())
                  .subjectDetails(buildStudentSubjects(testDef, student))
                  .build());
        });
    studentsList.sort(
        Comparator.comparing(
            student ->
                StringUtils.isNumeric(student.rollNumber())
                    ? Long.valueOf(student.rollNumber())
                    : null,
            Comparator.nullsLast(Comparator.naturalOrder())));
    return studentsList.stream().filter(x -> !x.subjectDetails().isEmpty()).toList();
  }

  private Map<String, String> buildStudentSubjects(OfflineTestDefinition testDef, Long student) {
    Map<String, String> subjects = new HashMap<>();
    AtomicLong marks = new AtomicLong(0L);
    testDef.getOfflineTestScheduleSchedule().stream()
        .filter(testSchedule -> testSchedule.getSubjectsMetaData() != null)
        .forEach(
            testSchedule -> {
              if (testSchedule.getPublishedAt() != null) {
                var scheduleStudent =
                    testSchedule.getOfflineTestScheduleStudents().stream()
                        .filter(x -> x.getStudentId().equals(student))
                        .findFirst();
                if (scheduleStudent.isPresent()) {
                  marks.set(marks.get() + testSchedule.getMarks());
                  var totalMarks = "(" + testSchedule.getMarks().toString() + ")";
                  if (testSchedule.getSubjectsMetaData().getWexlSubjectSlug().equals("sl-hindi")
                      || testSchedule
                          .getSubjectsMetaData()
                          .getWexlSubjectSlug()
                          .equals("fl-hindi")) {
                    testSchedule.setSubjectName("Hindi");
                  }
                  subjects.put(
                      testSchedule.getSubjectsMetaData().getName() + totalMarks,
                      getMarks(testSchedule, scheduleStudent.get()));
                  subjectData.put(
                      testSchedule.getSubjectsMetaData().getName() + totalMarks,
                      testSchedule.getSubjectsMetaData().getWexlSubjectSlug());
                }
              }
            });
    var sortSub = sortBySubjectOrder(subjects);
    if (sortSub.keySet().contains(EVS_SOCIAL) && subjects.keySet().contains(EVS_SCIENCE)) {
      sortSub.put("Evs - EVS Science + EVS Social", calculateEvsTotal(subjects));
    }
    sortSub.put("Total Marks" + "(" + marks + ")", calculateTotal(subjects));
    return sortSub;
  }

  private Map<String, String> sortBySubjectOrder(Map<String, String> subjects) {

    Map<String, String> sortedSubjects = new LinkedHashMap<>();

    order.forEach(
        subject -> {
          for (Map.Entry<String, String> entry : subjectData.entrySet()) {
            if (subject.equals(entry.getValue())) {
              for (Map.Entry<String, String> entry1 : subjects.entrySet()) {
                if (entry.getKey().equals(entry1.getKey())) {
                  sortedSubjects.put(entry.getKey(), entry1.getValue());
                }
              }
            }
          }
        });
    return sortedSubjects;
  }

  private String calculateEvsTotal(Map<String, String> subjects) {
    var evsSocialValue = subjects.get(EVS_SOCIAL);
    var evsScienceValue = subjects.get(EVS_SCIENCE);
    Long evsSocial =
        (evsSocialValue == null
                || Objects.equals(evsSocialValue, "NA")
                || Objects.equals(evsSocialValue, "A"))
            ? 0L
            : Long.parseLong(evsSocialValue);
    Long evsScience =
        (evsScienceValue == null
                || Objects.equals(evsScienceValue, "NA")
                || Objects.equals(evsScienceValue, "A"))
            ? 0L
            : Long.parseLong(evsScienceValue);
    return String.valueOf(evsSocial + evsScience);
  }

  private String calculateTotal(Map<String, String> subjects) {
    double total =
        subjects.values().stream()
            .mapToDouble(
                value -> {
                  try {
                    return Double.parseDouble(value);
                  } catch (NumberFormatException e) {
                    return 0;
                  }
                })
            .sum();

    return String.valueOf(total);
  }

  public List<GenericMetricResponse> getSubjectTopper(
      String orgSlug,
      List<String> gradeList,
      List<String> section,
      List<String> examType,
      List<String> subjectSlug) {
    var testSchedules =
        offlineTestScheduleRepository.getOfflineTestScheduleByOrgSlug(
            orgSlug, gradeList, section, examType, subjectSlug);
    List<GenericMetricResponse> response = new ArrayList<>();
    Map<String, Object> data = new HashMap<>();
    if (testSchedules.isEmpty()) {
      return Collections.emptyList();
    }
    List<OfflineTestScheduleStudent> top5Students = findTopStudents(testSchedules);
    var sortedByDesc =
        top5Students.stream()
            .sorted(
                Comparator.comparing(
                    OfflineTestScheduleStudent::getMarks, Comparator.reverseOrder()))
            .toList();
    data.put("subjectWiseTopper", buildSubjectMarksWiseStudentResponse(sortedByDesc));
    response.add(GenericMetricResponse.builder().data(data).build());
    return response;
  }

  private List<OfflineTestScheduleStudent> findTopStudents(List<OfflineTestSchedule> schedules) {
    List<OfflineTestScheduleStudent> tss = new ArrayList<>();
    schedules.forEach(
        schedule -> {
          var top5Marks = getTop5Marks(schedule.getOfflineTestScheduleStudents());
          tss.addAll(filterStudentsByMarks(top5Marks, schedule.getOfflineTestScheduleStudents()));
        });
    return tss;
  }

  public List<GenericMetricResponse> getSubjectFailure(
      String orgSlug,
      List<String> gradeList,
      List<String> section,
      List<String> examType,
      List<String> subjectSlug) {
    Map<String, Object> data = new HashMap<>();
    List<GenericMetricResponse> response = new ArrayList<>();
    var testSchedules =
        offlineTestScheduleRepository.getOfflineTestScheduleByOrgSlug(
            orgSlug, gradeList, section, examType, subjectSlug);

    if (testSchedules.isEmpty()) {
      return Collections.emptyList();
    }
    List<OfflineTestScheduleStudent> failureStudents = findFailureStudents(testSchedules);
    var sortedByDesc =
        failureStudents.stream()
            .sorted(
                Comparator.comparing(
                    OfflineTestScheduleStudent::getMarks, Comparator.naturalOrder()))
            .toList();

    data.put("subjectWiseFailures", buildSubjectMarksWiseStudentResponse(sortedByDesc));
    response.add(GenericMetricResponse.builder().data(data).build());
    return response;
  }

  private List<OfflineTestScheduleStudent> findFailureStudents(
      List<OfflineTestSchedule> schedules) {
    List<OfflineTestScheduleStudent> tss = new ArrayList<>();
    schedules.forEach(
        schedule -> tss.addAll(filterFailureStudents(schedule.getOfflineTestScheduleStudents())));
    return tss;
  }

  private List<OfflineTestScheduleStudent> filterFailureStudents(
      List<OfflineTestScheduleStudent> tss) {
    return new ArrayList<>();
  }

  private List<OfflineTestScheduleStudent> filterStudentsByMarks(
      List<Long> marks, List<OfflineTestScheduleStudent> studentList) {
    List<OfflineTestScheduleStudent> tss = new ArrayList<>();
    studentList.forEach(
        student -> {
          if (marks.contains(student.getMarks())) {
            tss.add(student);
          }
        });

    return tss;
  }

  private List<Long> getTop5Marks(List<OfflineTestScheduleStudent> tss) {
    return tss.stream()
        .filter(x -> x.getMarks() != null)
        .distinct()
        .sorted(
            Comparator.comparing(OfflineTestScheduleStudent::getMarks, Comparator.reverseOrder()))
        .toList()
        .stream()
        .map(OfflineTestScheduleStudent::getMarks)
        .map(MarksUtil::toMarksLong)
        .distinct()
        .limit(5)
        .toList();
  }

  private List<ReportCardDto.SubjectWiseTopper> buildSubjectMarksWiseStudentResponse(
      List<OfflineTestScheduleStudent> filterStudents) {
    List<ReportCardDto.SubjectWiseTopper> subjectWiseStudentList = new ArrayList<>();
    filterStudents.forEach(
        student -> {
          var studentDetails = validationUtils.isStudentValid(student.getStudentId());
          var studentUser = studentDetails.getUserInfo();
          var studentSection = studentDetails.getSection();
          if (studentUser == null) {
            return;
          }
          Optional<StudentAttributeValueModel> admissionNo =
              getStudentAttributeValue(studentDetails, ADMISSION_NO);
          subjectWiseStudentList.add(
              ReportCardDto.SubjectWiseTopper.builder()
                  .admissionNumber(admissionNo.isPresent() ? admissionNo.get().getValue() : null)
                  .subjectName(
                      student.getOfflineTestScheduleDetails().getSubjectsMetaData().getName())
                  .subjectSlug(
                      student
                          .getOfflineTestScheduleDetails()
                          .getSubjectsMetaData()
                          .getWexlSubjectSlug())
                  .gradeSlug(studentSection.getGradeSlug())
                  .gradeName(studentSection.getGradeName())
                  .marks(toMarksLong(student.getMarks()))
                  .section(studentSection.getName())
                  .examName(
                      student.getOfflineTestScheduleDetails().getOfflineTestDefinition().getTitle())
                  .examId(
                      student.getOfflineTestScheduleDetails().getOfflineTestDefinition().getId())
                  .name(studentUser.getFirstName() + " " + studentUser.getLastName())
                  .build());
        });
    return subjectWiseStudentList;
  }

  private String getMarks(
      OfflineTestSchedule testSchedule, OfflineTestScheduleStudent offlineTestScheduleStudent) {
    var scheduleStudentsMarks =
        testSchedule.getOfflineTestScheduleStudents().stream()
            .filter(x -> x.getMarks() != null)
            .count();
    if (scheduleStudentsMarks > 0) {
      if (Boolean.FALSE.equals(offlineTestScheduleStudent.getIsAttended())) {
        return "A";
      } else {
        return offlineTestScheduleStudent.getMarks() == null
            ? "-"
            : offlineTestScheduleStudent.getMarks().toString();
      }
    }
    return "";
  }

  public List<GenericMetricResponse> getClassTopper(
      String orgSlug, List<String> gradeSlug, List<String> section, List<String> examType) {
    List<GenericMetricResponse> responseList = new ArrayList<>();
    List<ReportCardDto.ReportCardStudents> studentsWithTop5Marks = new ArrayList<>();
    var testDefinition =
        offlineTestDefinitionRepository.getOfflineTestScheduleByOrgSlug(
            orgSlug, gradeSlug, section, examType);
    Map<String, Object> data = new HashMap<>();
    List<ReportCardDto.ReportCardStudents> studentsList = new ArrayList<>();
    testDefinition.forEach(
        testDef -> {
          if (!testDef.getOfflineTestScheduleSchedule().isEmpty()) {
            var students =
                testDef.getOfflineTestScheduleSchedule().get(0).getOfflineTestScheduleStudents();
            var studentData = buildClassTopperStudentDetails(students, testDef);
            studentsList.addAll(studentData);
          }
          List<Long> top5Marks =
              studentsList.stream()
                  .distinct()
                  .sorted(
                      Comparator.comparing(
                          ReportCardDto.ReportCardStudents::totalMarks, Comparator.reverseOrder()))
                  .toList()
                  .stream()
                  .map(ReportCardDto.ReportCardStudents::totalMarks)
                  .distinct()
                  .limit(5)
                  .toList();
          studentsList.forEach(
              student -> {
                if (top5Marks.contains(student.totalMarks())) {
                  studentsWithTop5Marks.add(student);
                }
              });
        });

    var sortedByDesc =
        studentsWithTop5Marks.stream()
            .distinct()
            .sorted(
                Comparator.comparing(
                    ReportCardDto.ReportCardStudents::totalMarks, Comparator.reverseOrder()))
            .toList();
    data.put("class_topper", sortedByDesc);
    responseList.add(GenericMetricResponse.builder().data(data).build());
    return responseList.stream().distinct().toList();
  }

  private List<ReportCardDto.ReportCardStudents> buildClassTopperStudentDetails(
      List<OfflineTestScheduleStudent> students, OfflineTestDefinition testDef) {
    List<ReportCardDto.ReportCardStudents> studentsList = new ArrayList<>();

    students.forEach(
        student -> {
          var studentDetails = validationUtils.isStudentValid(student.getStudentId());
          var studentUser = studentDetails.getUserInfo();
          var studentSection = studentDetails.getSection();
          Optional<StudentAttributeValueModel> admissionNo =
              getStudentAttributeValue(studentDetails, ADMISSION_NO);
          if (studentUser == null) {
            return;
          }
          studentsList.add(
              ReportCardDto.ReportCardStudents.builder()
                  .name(studentUser.getFirstName() + " " + studentUser.getLastName())
                  .section(studentSection.getName())
                  .gradeSlug(studentSection.getGradeSlug())
                  .gradeName(studentSection.getGradeName())
                  .admissionNumber(admissionNo.isPresent() ? admissionNo.get().getValue() : null)
                  .rollNumber(studentDetails.getRollNumber())
                  .totalMarks(getTotalMarks(testDef, student))
                  .examType(
                      student.getOfflineTestScheduleDetails().getOfflineTestDefinition().getTitle())
                  .examId(
                      student.getOfflineTestScheduleDetails().getOfflineTestDefinition().getId())
                  .build());
        });
    return studentsList;
  }

  private Long getTotalMarks(OfflineTestDefinition testDef, OfflineTestScheduleStudent student) {
    AtomicReference<Long> totalMarks = new AtomicReference<>(0L);
    testDef
        .getOfflineTestScheduleSchedule()
        .forEach(
            testSchedule -> {
              var scheduleStudent =
                  testSchedule.getOfflineTestScheduleStudents().stream()
                      .filter(x -> x.getStudentId().equals(student.getStudentId()))
                      .findFirst();
              scheduleStudent.ifPresent(
                  studentData -> {
                    if (studentData.getMarks() != null) {
                      totalMarks.set(totalMarks.get() + toMarksLong(studentData.getMarks()));
                    }
                  });
            });
    return totalMarks.get();
  }

  public List<GenericMetricResponse> getSubjectWiseMarks(
      String orgSlug,
      List<String> gradeSlug,
      List<String> section,
      List<String> subjectSlug,
      List<String> examType) {
    List<GenericMetricResponse> responseList = new ArrayList<>();
    List<ReportCardDto.SubjectWiseMarks> subjectWiseMarks = new ArrayList<>();
    var testDefinitions =
        offlineTestDefinitionRepository.getExamTypes(
            orgSlug, gradeSlug, section, subjectSlug, examType);
    if (testDefinitions.isEmpty()) {
      return Collections.emptyList();
    }
    var sections =
        testDefinitions.stream().map(OfflineTestDefinition::getSectionUuid).distinct().toList();
    Map<String, Object> data = new HashMap<>();
    sections.forEach(
        sec -> {
          var td = testDefinitions.stream().filter(x -> x.getSectionUuid().equals(sec)).toList();
          var testSchedules = filterTestSchedules(td, subjectSlug);
          var students = testSchedules.getFirst().getOfflineTestScheduleStudents();
          subjectWiseMarks.addAll(buildSubjectWiseMarksStudentDetails(students, testSchedules));
        });
    data.put("subject_wise_marks", sortSubjectWiseMarks(subjectWiseMarks));
    responseList.add(GenericMetricResponse.builder().data(data).build());
    return responseList.stream().distinct().toList();
  }

  private List<ReportCardDto.SubjectWiseMarks> sortSubjectWiseMarks(
      List<ReportCardDto.SubjectWiseMarks> subjectWiseMarks) {
    List<ReportCardDto.SubjectWiseMarks> sortedData = new ArrayList<>();
    List<ReportCardDto.SubjectWiseMarks> subjectWiseMarksData = new ArrayList<>(subjectWiseMarks);
    order.forEach(
        subjectName ->
            subjectWiseMarks.forEach(
                studentReportCard -> {
                  if (studentReportCard.subjectSlug().equals(subjectName)) {
                    sortedData.add(studentReportCard);
                    subjectWiseMarksData.remove(studentReportCard);
                  }
                }));

    sortedData.addAll(subjectWiseMarksData);
    return sortedData;
  }

  private List<OfflineTestSchedule> filterTestSchedules(
      List<OfflineTestDefinition> testDefinitions, List<String> subjectSlug) {
    List<OfflineTestSchedule> ots = new ArrayList<>();
    if (subjectSlug.isEmpty()) {
      testDefinitions.forEach(
          t -> ots.addAll(t.getOfflineTestScheduleSchedule().stream().toList()));
    } else {
      testDefinitions.forEach(
          t ->
              subjectSlug.forEach(
                  slug ->
                      ots.addAll(
                          t.getOfflineTestScheduleSchedule().stream()
                              .filter(
                                  x -> x.getSubjectsMetaData().getWexlSubjectSlug().equals(slug))
                              .toList())));
    }
    return ots;
  }

  private List<ReportCardDto.SubjectWiseMarks> buildSubjectWiseMarksStudentDetails(
      List<OfflineTestScheduleStudent> students, List<OfflineTestSchedule> testSchedules) {
    List<ReportCardDto.SubjectWiseMarks> studentsList = new ArrayList<>();
    students.forEach(
        student -> {
          var studentDetails = validationUtils.isStudentValid(student.getStudentId());
          var studentUser = studentDetails.getUserInfo();
          var studentSection = studentDetails.getSection();

          if (studentUser == null) {
            return;
          }
          List<ReportCardDto.SubjectWiseMarks> subjectDetails =
              testSchedules.stream()
                  .map(
                      testSchedule ->
                          ReportCardDto.SubjectWiseMarks.builder()
                              .name(studentUser.getFirstName() + " " + studentUser.getLastName())
                              .section(studentSection.getName())
                              .rollNumber(studentDetails.getRollNumber())
                              .gradeSlug(studentSection.getGradeSlug())
                              .subjectName(testSchedule.getSubjectsMetaData().getName())
                              .subjectSlug(testSchedule.getSubjectsMetaData().getWexlSubjectSlug())
                              .gradeName(studentSection.getGradeName())
                              .ExamDetails(
                                  buildExamDetails(
                                      testSchedules,
                                      student,
                                      testSchedule.getSubjectsMetaData().getWexlSubjectSlug()))
                              .build())
                  .toList();
          studentsList.addAll(subjectDetails);
        });
    return studentsList.stream()
        .sorted(
            Comparator.comparing(
                ReportCardDto.SubjectWiseMarks::rollNumber,
                Comparator.nullsLast(Comparator.naturalOrder())))
        .distinct()
        .toList();
  }

  private List<ReportCardDto.ExamDetails> buildExamDetails(
      List<OfflineTestSchedule> testSchedules,
      OfflineTestScheduleStudent student,
      String subjectSlug) {
    List<ReportCardDto.ExamDetails> examDetails = new ArrayList<>();
    testSchedules.forEach(
        testSchedule -> {
          var scheduleStudent =
              testSchedule.getOfflineTestScheduleStudents().stream()
                  .filter(
                      x ->
                          x.getStudentId().equals(student.getStudentId())
                              && x.getOfflineTestScheduleDetails()
                                  .getSubjectsMetaData()
                                  .getWexlSubjectSlug()
                                  .equals(subjectSlug))
                  .findFirst();
          if (scheduleStudent.isEmpty()
              || testSchedule.getOfflineTestDefinition().getTitle() == null) {
            return;
          }
          var getMarks =
              calculateMarks(
                  scheduleStudent.get().getMarks(), scheduleStudent.get().getIsAttended());
          examDetails.add(
              ReportCardDto.ExamDetails.builder()
                  .examId(testSchedule.getOfflineTestDefinition().getId())
                  .testScheduleId(testSchedule.getId())
                  .examName(testSchedule.getOfflineTestDefinition().getTitle())
                  .marks(getMarks)
                  .build());
        });

    return examDetails.stream().distinct().toList();
  }

  private String calculateMarks(BigDecimal studentMarks, Boolean isAttended) {
    if (studentMarks == null) {
      if (Boolean.FALSE.equals(isAttended)) {
        return "A";
      } else {
        return "NA";
      }
    }
    return studentMarks.toString();
  }

  public List<GenericMetricResponse> getClassMean(
      String orgSlug, List<String> gradeSlug, List<String> section, List<String> examType) {
    List<GenericMetricResponse> responseList = new ArrayList<>();
    var testDefinition =
        offlineTestDefinitionRepository.getOfflineTestScheduleByOrgSlug(
            orgSlug, gradeSlug, section, examType);
    Map<String, Object> data = new HashMap<>();
    var sectionsList =
        testDefinition.stream().map(OfflineTestDefinition::getSectionUuid).distinct().toList();
    List<ReportCardDto.ClassMean> classMeanList = new ArrayList<>();
    sectionsList.forEach(
        sec -> {
          var testDefinitionList =
              testDefinition.stream().filter(x -> Objects.equals(x.getSectionUuid(), sec)).toList();
          if (testDefinitionList.isEmpty()
              || testDefinitionList.getFirst().getSectionUuid() == null) {
            return;
          }
          var td = testDefinitionList.getFirst();
          var sectionData = validationUtils.findSectionByUuid(td.getSectionUuid());
          classMeanList.add(
              ReportCardDto.ClassMean.builder()
                  .gradeSlug(td.getGradeSlug())
                  .gradeName(td.getGradeName())
                  .section(sectionData.getName())
                  .classAverage(buildClassAverage(testDefinitionList))
                  .build());
        });
    data.put("class_mean", classMeanList);
    responseList.add(GenericMetricResponse.builder().data(data).build());

    return responseList;
  }

  private Long buildClassAverage(List<OfflineTestDefinition> testDefinitionList) {
    AtomicReference<Long> average = new AtomicReference<>(0L); // Initialize AtomicReference

    testDefinitionList.forEach(
        x ->
            x.getOfflineTestScheduleSchedule()
                .forEach(
                    testSchedule -> {
                      if (testSchedule.getPublishedAt() != null) {
                        var students = testSchedule.getOfflineTestScheduleStudents();
                        if (students.isEmpty()) {
                          return;
                        }
                        var filStudents =
                            students.stream().filter(y -> y.getMarks() != null).toList();
                        if (filStudents.isEmpty()) {
                          return;
                        }
                        var sum = calculateTotalMarks(filStudents);
                        var count = (long) filStudents.size();
                        var avg = Math.round((double) sum / count);
                        average.updateAndGet(v -> v + avg);
                      }
                    }));

    return average.get();
  }

  private Long calculateTotalMarks(List<OfflineTestScheduleStudent> students) {
    return students.stream()
        .map(OfflineTestScheduleStudent::getMarks)
        .mapToLong(MarksUtil::toMarksLong)
        .sum();
  }

  public List<GenericMetricResponse> getSubjectMean(
      String orgSlug, List<String> gradeSlug, List<String> section, List<String> year) {
    List<GenericMetricResponse> responseList = new ArrayList<>();
    var testDefinition =
        offlineTestDefinitionRepository.getOfflineTestScheduleForReportCard(
            orgSlug, gradeSlug, section, year);
    Map<String, Object> data = new HashMap<>();
    data.put("subject_mean", buildSubjectMean(testDefinition));
    responseList.add(GenericMetricResponse.builder().data(data).build());
    return responseList;
  }

  private List<ReportCardDto.SubjectMean> buildSubjectMean(
      List<OfflineTestDefinition> testDefinitionList) {
    List<ReportCardDto.SubjectMean> subjectMarks = new ArrayList<>();
    List<OfflineTestSchedule> offlineTestSchedules = new ArrayList<>();
    testDefinitionList.forEach(
        testDefinition ->
            offlineTestSchedules.addAll(
                testDefinition.getOfflineTestScheduleSchedule().stream()
                    .filter(x -> x.getPublishedAt() != null)
                    .toList()));
    var testDefinitions =
        offlineTestSchedules.stream()
            .map(OfflineTestSchedule::getOfflineTestDefinition)
            .distinct()
            .toList();
    var testDefNames =
        testDefinitions.stream().map(OfflineTestDefinition::getTitle).distinct().toList();
    var subjectSlugList =
        offlineTestSchedules.stream()
            .filter(testSchedule -> testSchedule.getSubjectsMetaData() != null)
            .map(x -> x.getSubjectsMetaData().getWexlSubjectSlug())
            .distinct()
            .toList();
    subjectSlugList.forEach(
        subject -> {
          var testSchedulesBySubject =
              offlineTestSchedules.stream()
                  .filter(
                      x ->
                          Optional.ofNullable(x.getSubjectsMetaData())
                              .map(SubjectsMetaData::getWexlSubjectSlug)
                              .orElse("")
                              .equals(subject))
                  .toList();
          subjectMarks.add(buildSubjectMeanForSubject(testSchedulesBySubject, testDefNames));
        });
    return sortSubjectMarks(subjectMarks);
  }

  private List<ReportCardDto.SubjectMean> sortSubjectMarks(
      List<ReportCardDto.SubjectMean> subjectMarks) {
    List<ReportCardDto.SubjectMean> sortedData = new ArrayList<>();
    List<ReportCardDto.SubjectMean> subjectMarksData = new ArrayList<>(subjectMarks);
    order.forEach(
        subjectName ->
            subjectMarks.forEach(
                studentReportCard -> {
                  if (studentReportCard.subjectSlug().equals(subjectName)) {
                    sortedData.add(studentReportCard);
                    subjectMarksData.remove(studentReportCard);
                  }
                }));

    sortedData.addAll(subjectMarksData);
    return sortedData;
  }

  private ReportCardDto.SubjectMean buildSubjectMeanForSubject(
      List<OfflineTestSchedule> testSchedulesBySubject, List<String> testDefTitle) {
    return ReportCardDto.SubjectMean.builder()
        .subjectName(testSchedulesBySubject.getFirst().getSubjectsMetaData().getName())
        .subjectSlug(testSchedulesBySubject.getFirst().getSubjectsMetaData().getWexlSubjectSlug())
        .examDetails(buildMarks(testSchedulesBySubject, testDefTitle))
        .build();
  }

  private Map<String, Object> buildMarks(
      List<OfflineTestSchedule> testSchedules, List<String> testNames) {
    Map<String, Object> data = new LinkedHashMap<>();
    for (String test : testNames) {
      var averageMarks = calculateAverageMarks(testSchedules, test);
      data.put(test, averageMarks);
    }
    return data;
  }

  private Object calculateAverageMarks(List<OfflineTestSchedule> testSchedules, String test) {

    var testSchedule =
        testSchedules.stream()
            .filter(schedule -> schedule.getOfflineTestDefinition().getTitle().equals(test))
            .findFirst();

    if (testSchedule.isEmpty() || testSchedule.get().getOfflineTestScheduleStudents().isEmpty()) {
      return null;
    }

    var students = testSchedule.get().getOfflineTestScheduleStudents();
    var filStudents = students.stream().filter(student -> student.getMarks() != null).toList();
    if (filStudents.isEmpty()) {
      return null;
    }

    var sum = filStudents.stream().map(a -> toMarksLong(a.getMarks())).mapToLong(a -> a).sum();
    var count = filStudents.size();
    return Math.round((double) sum / count);
  }

  public List<GenericMetricResponse> getExamTypes(
      String orgSlug,
      List<String> gradeSlug,
      List<String> section,
      List<String> subjectSlug,
      List<String> examType) {
    Map<String, Object> data = new HashMap<>();
    List<GenericMetricResponse> responseList = new ArrayList<>();
    var testDefinition =
        offlineTestDefinitionRepository.getExamTypes(
            orgSlug, gradeSlug, section, subjectSlug, examType);
    var examDetails = buildExamTypes(testDefinition);

    data.put("exam_types", examDetails);
    responseList.add(GenericMetricResponse.builder().data(data).build());

    return responseList;
  }

  private List<ReportCardDto.ExamDetails> buildExamTypes(
      List<OfflineTestDefinition> testDefinition) {

    List<ReportCardDto.ExamDetails> examDetails = new ArrayList<>();
    testDefinition.forEach(
        td ->
            examDetails.add(
                ReportCardDto.ExamDetails.builder()
                    .examId(td.getId())
                    .examName(td.getTitle())
                    .build()));
    return examDetails;
  }

  public List<GenericMetricResponse> getStudentReportCard(
      String orgSlug,
      String authUserId,
      List<String> examType,
      List<String> gradeSlug,
      List<String> section,
      String academicYear) {
    List<GenericMetricResponse> responseList = new ArrayList<>();
    Map<String, Object> data = new HashMap<>();
    Map<String, Object> summary = new HashMap<>();
    List<ReportCardDto.StudentReportCard> studentReportCardList = new ArrayList<>();
    if (examType.isEmpty()) {
      return Collections.emptyList();
    }
    var user = validationUtils.isValidUser(authUserId);
    var student = user.getStudentInfo();
    if (academicYear != null && academicYear.contains(YEAR_SLUG)) {

      var oldReport =
          studentHistoricalReportRepository
              .getReportByOrgSlugAndReportTypeIdAndStudentId(
                  orgSlug, student.getId(), Long.parseLong(examType.getFirst()))
              .orElseThrow();
      var s3Path = storageService.generatePreSignedUrlForFetch(oldReport.getPath());
      return List.of(GenericMetricResponse.builder().data(Map.of("report_url", s3Path)).build());
    }
    Optional<StudentAttributeValueModel> admissionNo =
        getStudentAttributeValue(student, ADMISSION_NO);
    var testDefinition =
        offlineTestDefinitionRepository.getOfflineTestScheduleByOrgSlug(
            orgSlug, gradeSlug, section, examType);
    if (testDefinition.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Invalid exam");
    }
    testDefinition
        .getFirst()
        .getOfflineTestScheduleSchedule()
        .forEach(
            ts ->
                studentReportCardList.add(
                    ReportCardDto.StudentReportCard.builder()
                        .subjectName(ts.getSubjectsMetaData().getName())
                        .totalMarks(ts.getMarks())
                        .subjectSlug(ts.getSubjectsMetaData().getWexlSubjectSlug())
                        .myMarks(getStudentMarks(ts, student.getId()))
                        .classMean(getClassMean(ts))
                        .topperMarks(getTopperMarks(ts))
                        .build()));

    double totalMarks =
        studentReportCardList.stream()
            .filter(x -> x.myMarks() != null && !"NA".equals(x.myMarks()))
            .mapToDouble(
                marks -> {
                  try {
                    return Double.parseDouble(marks.myMarks());
                  } catch (NumberFormatException e) {
                    return 0.0;
                  }
                })
            .sum();

    long averageMarks =
        Math.round(
            studentReportCardList.stream()
                .filter(
                    x ->
                        x.myMarks() != null
                            && !x.myMarks().equals("NA")
                            && !x.myMarks().equals("A"))
                .mapToDouble(
                    marks -> {
                      try {
                        return Double.parseDouble(marks.myMarks());
                      } catch (NumberFormatException e) {
                        return 0;
                      }
                    })
                .average()
                .orElse(Double.NaN));
    List<ReportCardDto.StudentReportCard> sortedData;
    if (higherGradeIds.contains(student.getSection().getGradeId())) {
      sortedData = sortData(studentReportCardList, order);
    } else {
      sortedData = sortData(studentReportCardList, lOrder);
    }
    var filterNotApplicableData =
        sortedData.stream().filter(x -> x.myMarks() != null && !"NA".equals(x.myMarks())).toList();
    var guardians = getGuardians(student);
    Optional<StudentAttributeValueModel> dob = getStudentAttributeValue(student, DOB);
    summary.put("exam_name", testDefinition.getFirst().getTitle());
    summary.put("student_name", userService.getNameByUserInfo(student.getUserInfo()));
    summary.put(DOB, dob.<Object>map(StudentAttributeValueModel::getValue).orElse(null));
    summary.put("father_name", guardians.fatherName());
    summary.put(
        ADMISSION_NO, admissionNo.<Object>map(StudentAttributeValueModel::getValue).orElse(null));
    summary.put("academic_year", ("20" + testDefinition.getFirst().getAcademicYearSlug()));
    summary.put("grade_name", student.getSection().getGradeName());
    summary.put("section_name", student.getSection().getName());
    summary.put("total_marks", totalMarks);
    summary.put("average_marks", averageMarks);
    summary.put(
        "standard_subject_marks",
        testDefinition.getFirst().getOfflineTestScheduleSchedule().getFirst().getMarks());
    data.put("summary", summary);
    data.put("student_report_card", filterNotApplicableData);
    responseList.add(GenericMetricResponse.builder().data(data).build());
    return responseList;
  }

  private List<ReportCardDto.StudentReportCard> sortData(
      List<ReportCardDto.StudentReportCard> studentReportCardList, List<String> order) {
    List<ReportCardDto.StudentReportCard> sortedData = new ArrayList<>();
    List<ReportCardDto.StudentReportCard> studentReportCardData =
        new ArrayList<>(studentReportCardList);
    order.forEach(
        subjectName ->
            studentReportCardList.forEach(
                studentReportCard -> {
                  if (studentReportCard.subjectSlug().equals(subjectName)) {
                    sortedData.add(studentReportCard);
                    studentReportCardData.remove(studentReportCard);
                  }
                }));

    sortedData.addAll(studentReportCardData);
    return sortedData;
  }

  private String getClassMean(OfflineTestSchedule ts) {
    var filStudents =
        ts.getOfflineTestScheduleStudents().stream().filter(y -> y.getMarks() != null).toList();
    if (filStudents.isEmpty()) {
      return null;
    }
    var sum = calculateTotalMarks(filStudents);
    var count = (long) filStudents.size();
    var avg = Math.round((double) sum / count);
    return String.valueOf(avg);
  }

  private String getTopperMarks(OfflineTestSchedule ts) {
    var topMarks =
        ts.getOfflineTestScheduleStudents().stream()
            .filter(student -> student != null && student.getMarks() != null)
            .max(Comparator.comparing(OfflineTestScheduleStudent::getMarks));
    return topMarks.isEmpty() ? null : topMarks.get().getMarks().toString();
  }

  public String getStudentMarks(OfflineTestSchedule ts, Long studentId) {
    var student =
        ts.getOfflineTestScheduleStudents().stream()
            .filter(x -> x.getStudentId().equals(studentId))
            .findFirst();
    if (student.isEmpty()) {
      return null;
    }
    return calculateMarks(student.get().getMarks(), student.get().getIsAttended());
  }

  public List<GenericMetricResponse> getStudentExamTypes(
      String orgSlug, String userAuthId, Boolean isPublished, String academicYear) {
    Map<String, Object> data = new HashMap<>();
    List<GenericMetricResponse> responseList = new ArrayList<>();
    var user = validationUtils.isValidUser(userAuthId);
    var student = user.getStudentInfo();
    List<OfflineTestDefinition> testDefinition = new ArrayList<>();
    if ("23-24".equals(academicYear)) {
      Long studentId = student.getPrevStudentId();
      if (studentId != null) {
        testDefinition =
            offlineTestDefinitionRepository.getStudentExamTypesByPrevStudentId(
                orgSlug, studentId, academicYear);
      } else {
        log.warn("Previous student ID is null for user: {}", userAuthId);
      }
    } else {
      if (Boolean.FALSE.equals(isPublished)) {
        testDefinition =
            offlineTestDefinitionRepository.getStudentAdmitCardExamTypes(
                orgSlug, student.getId(), student.getSection().getUuid().toString(), true);
      } else {
        testDefinition =
            offlineTestDefinitionRepository.getStudentExamTypes(
                orgSlug, student.getId(), student.getSection().getUuid().toString());
      }
    }
    var examDetails = buildExamTypes(testDefinition);

    data.put("student_exam_types", examDetails);
    responseList.add(GenericMetricResponse.builder().data(data).build());

    return responseList;
  }

  public List<GenericMetricResponse> detailsByStudent(String authUserId, String fromDate) {
    List<GenericMetricResponse> responseList = new ArrayList<>();

    var user = validationUtils.isValidUser(authUserId);
    var student = user.getStudentInfo();

    responseList.add(
        GenericMetricResponse.builder()
            .data(getStudentsSubjectsData(student))
            .summary(buildSummary(student, fromDate))
            .build());
    return responseList;
  }

  private Map<String, Object> getStudentsSubjectsTestData(Student student) {
    var testSchedules = offlineTestScheduleStudentRepository.getStudentReportCard(student.getId());
    var examNames = testSchedules.stream().map(StudentReportCard::getExamName).distinct().toList();

    Map<String, Object> examName = new HashMap<>();
    Map<String, Object> sumData = new HashMap<>();
    Map<String, Object> averageData = new HashMap<>();
    Map<String, Object> sortedMap = new TreeMap<>(Collections.reverseOrder());
    examNames.forEach(
        exam -> {
          String name = null;
          for (Map.Entry<String, String> entry : subjectData.entrySet()) {
            if (entry.getKey().equals(exam)) {
              name = entry.getValue();
            }
          }
          if (name != null) {
            var sum =
                testSchedules.stream()
                    .filter(x -> x.getExamName().equals(exam) && x.getMarksScored() != null)
                    .mapToDouble(x -> Double.parseDouble((x.getMarksScored())))
                    .sum();
            sumData.put(name, sum);
            var average =
                testSchedules.stream()
                    .filter(x -> x.getExamName().equals(exam) && x.getMarksScored() != null)
                    .mapToDouble(x -> Double.parseDouble(x.getMarksScored()))
                    .average();
            averageData.put(name, average.isEmpty() ? 0 : Math.round(average.getAsDouble()));
          }
        });
    examName.put("sum", sumData);
    examName.put("average", averageData);
    sortedMap.putAll(examName);
    return sortedMap;
  }

  private Map<String, Object> buildSummary(Student student, String fromDate) {
    Optional<StudentAttributeValueModel> admissionNo =
        getStudentAttributeValue(student, ADMISSION_NO);
    Optional<StudentAttributeValueModel> address =
        getStudentAttributeValue(student, "residental_address");
    Optional<StudentAttributeValueModel> dob = getStudentAttributeValue(student, DOB);
    var testScheduleStudent =
        offlineTestScheduleStudentRepository.findByStudentIdOrderByIdDesc(student.getId());
    var testSchedule =
        offlineTestScheduleService.validateOfflineTestSchedule(
            testScheduleStudent.getFirst().getOfflineTestScheduleDetails().getId());
    var testDefinition =
        offlineTestScheduleService.validateOfflineTestDefinition(
            testSchedule.getOfflineTestDefinition().getId());
    Map<String, Object> summary = new HashMap<>();
    var guardians = getGuardians(student);
    Long fDate = Long.valueOf(dateTimeUtil.convertEpocToIntegerFormat(Long.valueOf(fromDate)));
    summary.put("student_name", userService.getNameByUserInfo(student.getUserInfo()));
    summary.put(
        ADMISSION_NO, admissionNo.<Object>map(StudentAttributeValueModel::getValue).orElse(null));
    summary.put("father_name", guardians.fatherName());
    summary.put("mother_name", guardians.motherName());
    summary.put("guardian_name", guardians.guardianName());
    summary.put("mobile_number", guardians.mobileNumber());
    summary.put("academics_session", ("20" + testDefinition.getAcademicYearSlug()));
    summary.put("section_name", student.getSection().getName());
    summary.put("address", address.<Object>map(StudentAttributeValueModel::getValue).orElse(null));
    summary.put(DOB, dob.<Object>map(StudentAttributeValueModel::getValue).orElse(null));
    summary.put(
        "total_working_days",
        getTotalDays(student, fDate, student.getUserInfo().getOrganization()));
    summary.put(
        "present_days", getPresentDays(student, fDate, student.getUserInfo().getOrganization()));
    summary.put(
        "attendance",
        getAttendance(
            getPresentDays(student, fDate, student.getUserInfo().getOrganization()),
            getTotalDays(student, fDate, student.getUserInfo().getOrganization())));
    return summary;
  }

  public Integer getPresentDays(Student student, Long fromDate, String orgSlug) {
    Date currentDate = new Date();
    Long toDate = Long.valueOf(dateTimeUtil.convertToIntegerFormat(currentDate));
    return sectionAttendanceDetailRepository.getTotalWorkingDays(
        orgSlug, student.getId(), fromDate, toDate);
  }

  public Integer getTotalDays(Student student, Long fromDate, String orgSlug) {

    Date currentDate = new Date();
    Long toDate = Long.valueOf(dateTimeUtil.convertToIntegerFormat(currentDate));
    return sectionAttendanceDetailRepository.getPresentDays(
        orgSlug, student.getId(), fromDate, toDate);
  }

  private Float getAttendance(int presentDays, int totalDays) {
    return totalDays == 0 ? 0.0F : (float) ((presentDays / totalDays) * 100);
  }

  private Map<String, Object> getStudentsSubjectsData(Student student) {
    Map<String, Object> data = new HashMap<>();

    var testSchedules = offlineTestScheduleStudentRepository.getStudentReportCard(student.getId());

    var subjectsSlugs =
        testSchedules.stream().map(StudentReportCard::getSubjectSlug).distinct().toList();

    subjectsSlugs.forEach(
        slug -> {
          var studentReportCard =
              testSchedules.stream()
                  .filter(x -> x.getSubjectSlug().equals(slug))
                  .sorted(Comparator.comparing(StudentReportCard::getExamStartDate))
                  .toList();
          Map<String, Object> examDetails = new LinkedHashMap<>();

          studentReportCard.forEach(
              sub -> {
                var totalExamMarks =
                    testSchedules.stream()
                        .filter(x -> x.getExamName().equals(sub.getExamName()))
                        .mapToLong(StudentReportCard::getTotalMarks)
                        .sum();

                var subName = sub.getExamName() + " (" + totalExamMarks + ")";
                examDetails.put(subName, sub.getMarksScored());
                subjectData.put(sub.getExamName(), subName);
              });
          data.put(studentReportCard.get(0).getSubjectSlug(), examDetails);
        });

    Map<String, Object> sortedData = new LinkedHashMap<>();
    if (higherGradeIds.contains(student.getSection().getGradeId())) {
      sortedData.putAll(sortDataBySubject(data, order, testSchedules));
    } else {
      sortedData.putAll(sortDataBySubject(data, lOrder, testSchedules));
    }
    sortedData.putAll(getStudentsSubjectsTestData(student));
    return sortedData;
  }

  private Map<String, Object> sortDataBySubject(
      Map<String, Object> data, List<String> sortOrder, List<StudentReportCard> testSchedules) {
    Map<String, Object> sortedData = new LinkedHashMap<>();
    Map<String, Object> data1 = new LinkedHashMap<>(data);
    sortOrder.forEach(
        subjectSlug ->
            data.forEach(
                (key, value) -> {
                  if (subjectSlug.equals(key)) {
                    sortedData.put(key, value);
                    data1.remove(key, value);
                  }
                }));
    sortedData.putAll(data1);
    return updateSubjectNames(sortedData, testSchedules);
  }

  private Map<String, Object> updateSubjectNames(
      Map<String, Object> data, List<StudentReportCard> testSchedules) {
    Map<String, Object> sortedData = new LinkedHashMap<>();
    data.forEach(
        (key, value) -> {
          var testSchedule =
              testSchedules.stream().filter(x -> x.getSubjectSlug().equals(key)).toList();
          sortedData.put(testSchedule.getFirst().getSubjectName(), value);
        });
    return sortedData;
  }

  private GuardianDto.StudentGuardianResponse getGuardians(Student student) {
    var primaryGuardian = !student.getGuardians().isEmpty() ? getPrimaryGuardian(student) : null;
    return GuardianDto.StudentGuardianResponse.builder()
        .mobileNumber(Objects.nonNull(primaryGuardian) ? primaryGuardian.getMobileNumber() : null)
        .fatherName(getParentDetails(FATHER, student.getGuardians()))
        .motherName(getParentDetails(MOTHER, student.getGuardians()))
        .guardianName(getParentDetails(GUARDIAN, student.getGuardians()))
        .build();
  }

  public Guardian getPrimaryGuardian(Student student) {
    var primaryGuardian = student.getGuardians().stream().filter(Guardian::getIsPrimary).findAny();
    return primaryGuardian.orElseGet(
        () -> !student.getGuardians().isEmpty() ? student.getGuardians().get(0) : null);
  }

  private String getParentDetails(GuardianRole role, List<Guardian> guardians) {
    if (guardians.isEmpty()) {
      return null;
    }
    var guardian = guardians.stream().filter(x -> x.getRelationType().equals(role)).findFirst();
    return guardian.isEmpty() ? null : getGuardianName(guardian.get());
  }

  public String getGuardianName(Guardian guardian) {
    if (Objects.isNull(guardian)) {
      return null;
    }
    var firstName =
        Objects.nonNull(guardian.getFirstName()) ? guardian.getFirstName() : StringUtils.EMPTY;
    var lastName =
        Objects.nonNull(guardian.getFirstName()) ? guardian.getLastName() : StringUtils.EMPTY;
    return firstName.concat(StringUtils.SPACE + lastName);
  }

  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String value) {
    var attributes = studentAttributeValueRepository.findAllByStudentId(student.getId());
    if (attributes.isEmpty()) {
      return Optional.empty();
    }
    return attributes.stream()
        .filter(attrib -> attrib.getAttributeDefinition().getName().equals(value))
        .findAny();
  }
}
