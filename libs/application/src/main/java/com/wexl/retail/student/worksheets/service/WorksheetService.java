package com.wexl.retail.student.worksheets.service;

import com.wexl.retail.student.worksheets.dto.WorksheetResponse;
import com.wexl.retail.student.worksheets.dto.WorksheetSummaryResponse;
import com.wexl.retail.student.worksheets.repository.WorksheetRepository;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class WorksheetService {

  @Autowired private WorksheetRepository worksheetRepository;

  public List<WorksheetResponse> getChapterWorksheets(
      String providerSlug, String chapterSlug, long studentId) {
    return worksheetRepository.getChapterWorksheets(providerSlug, chapterSlug, studentId);
  }

  public List<WorksheetSummaryResponse> getChapterWiseWorksheetSummary(
      String providerSlug, String subject, String grade, String board, long studentId) {
    return worksheetRepository.getChapterWiseWorksheetSummary(
        providerSlug, subject, grade, board, studentId);
  }
}
