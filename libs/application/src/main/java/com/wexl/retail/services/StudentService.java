package com.wexl.retail.services;

import static com.wexl.retail.globalprofile.model.AppTemplate.ADMIN;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.classroom.core.dto.ClassRoomDto;
import com.wexl.retail.classroom.core.model.Classroom;
import com.wexl.retail.classroom.core.repository.ClassroomRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.mlp.model.Mlp;
import com.wexl.retail.mlp.model.MlpInst;
import com.wexl.retail.mlp.model.MlpItemStatus;
import com.wexl.retail.mlp.repository.MlpInstRepository;
import com.wexl.retail.mlp.repository.MlpRepository;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.telegram.service.UserService;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class StudentService {

  private final AuthService authService;
  private final StudentRepository studentRepository;
  private final UserRoleHelper userRoleHelper;

  private final OrganizationRepository organizationRepository;
  private final ClassroomRepository classroomRepository;
  private final UserRepository userRepository;

  private final UserService userService;
  private final TeacherRepository teacherRepository;
  private final MlpRepository mlpRepository;
  private final MlpInstRepository mlpInstRepository;

  @SneakyThrows
  public Student fetchStudentUsingAuthToken() {
    var studentId = authService.getStudentDetails().getStudentInfo().getId();
    return findStudentById(studentId);
  }

  @SneakyThrows
  public Student findStudentById(long studentId) {
    return studentRepository
        .findById(studentId)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "error.StudentFind.StudentId",
                    new String[] {Long.toString(studentId)}));
  }

  public Student getStudentById(long id) {
    return studentRepository.getById(id);
  }

  public Student findByUserInfo(User userInfo) {
    return studentRepository
        .findByUserInfo(userInfo)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.StudentFind.User"));
  }

  public Student save(final Student student) {
    return studentRepository.save(student);
  }

  public Set<Student> getStudentsBySections(Set<Section> sections) {
    Set<Student> students = new HashSet<>();
    sections.forEach(
        section ->
            students.addAll(studentRepository.getStudentsBySectionAndDeletedAtIsNull(section)));
    return students;
  }

  public List<Student> getStudentsByIdsAndOrgSlug(
      String orgSlug, List<Long> studentIds, boolean throwException) {
    List<Student> students = getStudentsByIds(orgSlug, studentIds);

    if (students.isEmpty() && throwException) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.StudentUnauthorized.Org",
          new String[] {orgSlug});
    }
    return students;
  }

  public List<Student> getStudentsByIds(String orgSlug, List<Long> studentIds) {

    List<Student> students;
    var user = authService.getUserDetails();
    if (AuthUtil.isStudent(user)) {
      students = studentRepository.findAllByIdsAndOrgSlug(List.of(orgSlug), studentIds);
    } else if (userRoleHelper.isManager(user)) {
      List<String> childOrgSlugs = organizationRepository.getAllChildOrgSlugs(orgSlug);
      childOrgSlugs.add(orgSlug);
      students = studentRepository.findAllByIdsAndOrgSlug(childOrgSlugs, studentIds);
    } else {
      students = studentRepository.findAllByIdsAndOrgSlug(List.of(orgSlug), studentIds);
    }

    return students;
  }

  public List<Student> getStudentsBySectionUuidsAndOrgSlug(
      List<String> sectionUuids, String orgSlug) {

    try {
      var sections = sectionUuids.stream().map(UUID::fromString).toList();
      return studentRepository.getStudentsBySectionUuidsAndOrgSlug(sections, orgSlug);
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.StudentsBySections.Organization",
          new String[] {orgSlug});
    }
  }

  public List<ClassRoomDto.ClassRoomResponse> getClassRoomsByStudentAuthId(String studentId) {

    var studentClassRooms =
        classroomRepository.findByStudentsAndDeletedAtIsNullOrderByCreatedAtDesc(
            getStudentByAuthId(studentId));
    return buildResponse(studentClassRooms);
  }

  public Student getStudentByAuthId(String studentAuthId) {
    User user =
        userRepository
            .findByAuthUserId(studentAuthId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound"));

    return studentRepository
        .findByUserInfo(user)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentNotFound"));
  }

  private List<ClassRoomDto.ClassRoomResponse> buildResponse(List<Classroom> studentClassRooms) {
    return studentClassRooms.stream()
        .map(
            student ->
                ClassRoomDto.ClassRoomResponse.builder()
                    .classRoomName(student.getName())
                    .teacherId(
                        student.getTeachers().isEmpty()
                            ? null
                            : student.getTeachers().stream().map(Teacher::getId).toList())
                    .teacherName(
                        student.getTeachers().isEmpty()
                            ? null
                            : userService.getFullNamesByUsers(
                                student.getTeachers().stream().map(Teacher::getUserInfo).toList()))
                    .id(student.getId())
                    .scheduleCount(student.getSchedules().size())
                    .build())
        .toList();
  }

  public List<Student> getActiveStudents(List<Student> students) {
    if (students.isEmpty()) {
      return Collections.emptyList();
    }
    return students.stream().filter(student -> Objects.isNull(student.getDeletedAt())).toList();
  }

  public void assignPreviousMlpToStudents(String orgSlug, Student student) {
    var mlpList = mlpRepository.findByOrgSlugAndGrade(orgSlug, student.getId());
    mlpList.forEach(mlp -> saveMlpInst(student, mlp));
  }

  private void saveMlpInst(Student student, Mlp selectedMlp) {
    List<MlpInst> mlpInstList = new ArrayList<>();
    MlpInst studentRecord = new MlpInst();
    studentRecord.setStudent(student);
    studentRecord.setMlp(selectedMlp);
    studentRecord.setSynopsisStatus(MlpItemStatus.NOT_STARTED);
    studentRecord.setVideoStatus(MlpItemStatus.NOT_STARTED);
    mlpInstList.add(studentRecord);
    mlpInstRepository.save(studentRecord);
  }

  public List<Long> getStudentIdsByAuthUserId(List<String> students, String orgSlug) {
    return studentRepository.getStudentIdsByAuthUserId(students, orgSlug);
  }

  public Long getStudentCount(String orgSlug, String teacherAuthId) {
    User user =
        userRepository
            .findByAuthUserId(teacherAuthId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound"));
    var teacher =
        teacherRepository
            .findByUserInfo(user)
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TeacherNotFound"));

    var roleTemplate = teacher.getRoleTemplate();
    if (ADMIN == roleTemplate.getTemplate()) {
      return studentRepository.getStudentCountByOrg(orgSlug);
    } else {
      var sect = teacher.getSections();
      var sectionUuids = sect.stream().map(section -> section.getUuid().toString()).toList();
      var count = getStudentsBySectionUuidsAndOrgSlug(sectionUuids, orgSlug).size();
      return (long) count;
    }
  }
}
