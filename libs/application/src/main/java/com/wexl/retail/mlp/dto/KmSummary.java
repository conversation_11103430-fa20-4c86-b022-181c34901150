package com.wexl.retail.mlp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public interface KmSummary {
  @JsonProperty("grade_slug")
  String getGradeSlug();

  @JsonProperty("attendance_percentage")
  Double getAttendancePercentage();

  @JsonProperty("knowledge_percentage")
  Double getKnowledgePercentage();

  @JsonProperty("name")
  String getName();

  @JsonProperty("subject")
  String getSubjectSlug();

  @JsonProperty("subject_name")
  String getSubjectName();

  @JsonProperty("chapter_name")
  String getChapterName();

  @JsonProperty("subtopic_name")
  String getSubtopicName();

  @JsonProperty("slug")
  String getSubtopicSlug();

  @JsonProperty("chapter_slug")
  String getChapterSlug();
}
