package com.wexl.retail.whatsapp.interakt.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record Request() {

  @Builder
  public record SendMessageRequest(
      String countryCode, String phoneNumber, String type, WhatsappMessageTemplate template) {}

  @Builder
  public record WhatsappMessageTemplate(
      String name, String languageCode, List<String> bodyValues) {}

  @Builder
  public record Recipient(
      String mobileNumber,
      String name,
      String date,
      String orgName,
      String orgSlug,
      String reportCardLink,
      String sectionName) {}

  public record SendMessageResponse(String result, String message, String id) {}

  @Builder
  public record BulkWhatsAppResponse(
      String status,
      @JsonProperty("hasError") String hasError,
      String data,
      Object errors,
      @JsonProperty("request_id") String requestId) {}
}
