package com.wexl.retail.v2.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "sc_answer_sheet_templates")
public class ScAnswerSheetTemplates extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "template_id")
  private Long templateId;

  @Column(name = "template_name")
  private String templateName;

  @Column(name = "xml_file_name")
  private String xmlFileName;
}
