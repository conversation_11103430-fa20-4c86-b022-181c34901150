package com.wexl.retail.student.attributes.repository;

import com.wexl.retail.model.Student;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface StudentAttributeValueRepository
    extends JpaRepository<StudentAttributeValueModel, Long> {

  Optional<StudentAttributeValueModel> findByStudentAndAttributeDefinitionId(
      Student student, Long id);

  @Query(
      value =
          """
                          select sav.* from student_attribute_definitions sad
                          join student_attribute_values sav on sav.attribute_definition_id = sad.id
                          where sad.org_slug = :orgSlug and (sad."name" ilike 'bet_corp_schedule_%' or sad."name" ilike 'institute_name') and sav.student_id = :studentId
                          order by sad.id desc
                          """,
      nativeQuery = true)
  List<StudentAttributeValueModel> findByStudentAndOrgSlug(String orgSlug, Long studentId);

  List<StudentAttributeValueModel> findAllByStudentId(Long studentId);

  @Query(
      value =
          """
                          select * from student_attribute_values where student_id in (:students)
                          """,
      nativeQuery = true)
  List<StudentAttributeValueModel> findAllByStudentIds(List<Long> students);

  @Query(
      value =
          """
                            SELECT sav.* FROM test_schedule_student tss
                            JOIN test_schedule ts ON ts.id = tss.schedule_test_id
                            JOIN student_attribute_values sav ON CAST(sav.value AS BIGINT) = ts.id
                            JOIN student_attribute_definitions sad ON sad.id = sav.attribute_definition_id
                            WHERE tss.student_id = :studentId
                            AND tss.status = :status
                            AND sad."name" ILIKE :attributeName
                  """,
      nativeQuery = true)
  List<StudentAttributeValueModel> findByStudentAndScheduleTestInAndStatus(
      Long studentId, String status, String attributeName);
}
