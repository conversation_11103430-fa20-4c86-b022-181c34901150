package com.wexl.retail.test.schedule.domain;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(
    name = "test_schedule_student",
    uniqueConstraints =
        @UniqueConstraint(columnNames = {"schedule_test_id", "status", "student_id"}))
public class ScheduleTestStudent extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "test_schedule_student_id_seq")
  @SequenceGenerator(name = "test_schedule_student_id_seq")
  private long id;

  @ManyToOne private ScheduleTest scheduleTest;
  @ManyToOne private User student;
  private String status;

  @Column(name = "failure_reason", columnDefinition = "VARCHAR(1000)")
  private String failureReason;

  private String uuid;
  private LocalDateTime startTime;
  private LocalDateTime endTime;
  private LocalDateTime allowedEndTime;

  @Column(name = "result_processing_time")
  private LocalDateTime resultProcessingTime;

  private Integer questionResponseSet;

  @Column(name = "image_path")
  private String omrImagePath;

  private String resultPath;

  private String personalizedWorkSheet;
}
