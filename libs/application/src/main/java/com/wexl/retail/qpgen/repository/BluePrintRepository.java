package com.wexl.retail.qpgen.repository;

import com.wexl.retail.qpgen.model.BluePrint;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface BluePrintRepository extends JpaRepository<BluePrint, Long> {
  List<BluePrint> findAllByOrgSlugAndDeletedAtIsNullOrderByCreatedAtDesc(String orgSlug);

  BluePrint findByIdAndOrgSlug(Long bluePrintId, String orgSlug);
}
