package com.wexl.retail.section.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.section.domain.SectionStatus;
import com.wexl.retail.section.dto.ConnectedTeacher;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SectionResponse {

  private long id;
  private String name;
  private SectionStatus status;
  private UUID uuid;
  private Integer grade;
  private String board;
  private List<ConnectedTeacher> teachers;

  @JsonProperty("org_slug")
  private String orgSlug;

  @JsonProperty("org_name")
  private String orgName;
}
