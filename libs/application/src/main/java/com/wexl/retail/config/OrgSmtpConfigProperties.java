package com.wexl.retail.config;

import java.util.Map;
import lombok.Builder;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "app.email")
public class OrgSmtpConfigProperties {
  private Map<String, SmtpConfig> smtpConfig;

  @Data
  @Builder
  public static class SmtpConfig {
    private String host;
    private int port;
    private String username;
    private String password;
    private String fromEmail;
    private String authKey;
    private String integratedNumber;
  }
}
