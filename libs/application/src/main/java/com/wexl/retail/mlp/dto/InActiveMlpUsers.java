package com.wexl.retail.mlp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.model.UserDetails;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class InActiveMlpUsers {

  @JsonProperty("org_name")
  private String orgName;

  @JsonProperty("principal_name")
  private String principalName;

  @JsonProperty("total_students")
  private Integer totalStudents;

  @JsonProperty("total_teachers")
  private Integer totalTeachers;

  private String date;

  @JsonProperty("mlps_assigned_teachers")
  private Integer mlpsAssignedTeachers;

  @JsonProperty("mlps_attended_students")
  private Integer mlpsAttendedStudents;

  @JsonProperty("mlps_not_assigned_teachers")
  private List<UserDetails> mlpsNotAssignedTeachers;

  @JsonProperty("mlps_not_attended_students")
  private List<UserDetails> mlpsNotAttendedStudents;
}
