package com.wexl.retail.student.answer;

import static com.wexl.retail.util.Constants.AUTHORIZATION_HEADER;

import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.commons.storage.S3FileUploadResult;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.util.UploadService;
import java.util.List;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@RequestMapping
@IsStudent
@RequiredArgsConstructor
public class StudentAnswerController {

  private final UploadService uploadService;
  private final StudentAnswerService studentAnswerService;
  private final ContentService contentService;

  @GetMapping("/orgs/{orgSlug}/students/{studentAuthId}/exam/{examId}/uploadAnswerSheet")
  public S3FileUploadResult generatePresignedUrlToUploadAnswerSheet(@PathVariable long examId) {
    return uploadService.generatePresignedUrlToUploadAnswerSheet(examId);
  }

  @GetMapping("/orgs/{orgSlug}/students/{studentAuthId}/exam/answer")
  public List<StudentAnswerResponse> getExamAnswers(
      @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "10") int size,
      @RequestParam(defaultValue = "id") String sort,
      @RequestParam(defaultValue = "ASC") Sort.Direction direction) {
    return studentAnswerService.getExamAnswers(page, size, Sort.by(direction, sort));
  }

  @PostMapping("/orgs/{orgSlug}/students/{studentAuthId}/exam/{examId}/answers:submit")
  public StudentAnswerResponse submitExamAnswers(
      @RequestBody StudentAnswerRequest studentAnswerRequest,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {

    return studentAnswerService.submitExamAnswers(studentAnswerRequest, bearerToken);
  }

  @PostMapping("/orgs/{orgSlug}/students/{studentAuthId}/practice/{examId}/answer:submit")
  public ResponseEntity<StudentAnswerPracticeResponse> submitPracticeAnswer(
      @RequestBody StudentAnswerPracticeRequest studentAnswerRequest,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {

    return new ResponseEntity<>(
        studentAnswerService.submitPracticeAnswer(studentAnswerRequest, bearerToken),
        HttpStatus.OK);
  }

  /**
   * @deprecated By {@link #getExamAnswers(int, int, String, Direction)}
   *     <p>standardized Api so that it can be filtered by OrgValidator and StudentValidator.
   * @param page The default value to display page is 0.
   * @param size The default value to display number of items in a page is 10.
   * @param sort The default value to sort items is by id.
   * @param direction The default value of sorting order is ASCENDING.
   * @return List of {@link StudentAnswerResponse}
   */
  @Deprecated(forRemoval = true)
  @GetMapping("/student/exam/answer")
  public List<StudentAnswerResponse> getAllAnswers(
      @RequestParam(defaultValue = "0") int page,
      @RequestParam(defaultValue = "10") int size,
      @RequestParam(defaultValue = "id") String sort,
      @RequestParam(defaultValue = "ASC") Sort.Direction direction) {
    return studentAnswerService.getExamAnswers(page, size, Sort.by(direction, sort));
  }

  /**
   * @deprecated By {@link #submitExamAnswers(StudentAnswerRequest, String)}
   *     <p>standardized Api so that it can be filtered by OrgValidator and StudentValidator.
   * @param studentAnswerRequest 1. examId 2. List of {@link ExamQuestion}
   * @param bearerToken The Authorization token that is evaluated in content-service
   * @return {@link StudentAnswerResponse}
   * @throws ApiException INVALID_REQUEST (400): If any erros occurred while submitting test answers
   */
  @Deprecated(forRemoval = true)
  @PostMapping("/student/exam/answer/test")
  public StudentAnswerResponse addAnswerTest(
      @RequestBody StudentAnswerRequest studentAnswerRequest,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {

    return studentAnswerService.submitExamAnswers(studentAnswerRequest, bearerToken);
  }

  /**
   * @deprecated By {@link #submitPracticeAnswer(StudentAnswerPracticeRequest, String)}
   *     <p>standardized Api so that it can be filtered by OrgValidator and StudentValidator.
   * @param studentAnswerRequest 1. examId 2. List of {@link ExamQuestion}
   * @param bearerToken The Authorization token that is evaluated in content-service
   * @return ResponseEntity of {@link StudentAnswerPracticeResponse}
   */
  @Deprecated(forRemoval = true)
  @PostMapping("/student/exam/answer/practice")
  public ResponseEntity<StudentAnswerPracticeResponse> addAnswerPractice(
      @RequestBody StudentAnswerPracticeRequest studentAnswerRequest,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {

    return new ResponseEntity<>(
        studentAnswerService.submitPracticeAnswer(studentAnswerRequest, bearerToken),
        HttpStatus.OK);
  }

  /**
   * @deprecated By {@link #generatePresignedUrlToUploadAnswerSheet(long)}
   *     <p>standardized Api so that it can be filtered by OrgValidator and StudentValidator. <br>
   *     will upload file directly from client machine to S3 bucket instead of routing it through
   *     retail-service by providing S3PresignedUrl of <code>PUT</code> operation.
   * @param examId The Id of current exam.
   * @param multipartFile The file to be uploaded to S3 bucket.
   * @return {@link S3FileUploadResult}
   */
  @Deprecated(forRemoval = true)
  @PostMapping("/student/exam/answer/{examId}/upload/answer-sheet")
  public S3FileUploadResult uploadPdfAnswerSheet(
      @NonNull @PathVariable Long examId,
      @RequestPart(value = "file") MultipartFile multipartFile) {

    return uploadService.uploadPdfAnswerSheets(examId, multipartFile);
  }

  /**
   * @deprecated By {@link #generatePresignedUrlToUploadAnswerSheet(long)}
   *     <p>standardized Api so that it can be filtered by OrgValidator and StudentValidator. <br>
   *     will upload file directly from client machine to S3 bucket instead of routing it through
   *     retail-service by providing S3PresignedUrl of <code>PUT</code> operation.
   * @param examId The Id of current exam.
   * @param multipartFiles List of files to be uploaded to S3 bucket.
   * @return List of {@link S3FileUploadResult}
   */
  @Deprecated(forRemoval = true)
  @PostMapping("/student/exam/answer/{examId}/upload")
  public List<S3FileUploadResult> uploadAnswerSheet(
      @NonNull @PathVariable Long examId,
      @RequestPart(value = "files") List<MultipartFile> multipartFiles) {

    return uploadService.uploadAnswerSheets(examId, multipartFiles);
  }

  @GetMapping("/orgs/{orgSlug}/elp-answers")
  public List<QuestionDto.SearchQuestionByUuidResponse> getAnswersByUuid(
      QuestionDto.SearchQuestionByUuid searchQuestionByUuid, @PathVariable String orgSlug) {
    return contentService.getAnswersByUuid(searchQuestionByUuid, orgSlug);
  }
}
