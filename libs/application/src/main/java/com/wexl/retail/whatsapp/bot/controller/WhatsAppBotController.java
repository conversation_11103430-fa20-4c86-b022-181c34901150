package com.wexl.retail.whatsapp.bot.controller;

import com.wexl.retail.whatsapp.bot.dto.WhatsAppBotDto;
import com.wexl.retail.whatsapp.bot.service.WhatsAppBotService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("/public/whatsapp")
public class WhatsAppBotController {
  private final WhatsAppBotService whatsAppBotService;

  @PostMapping(value = "/users")
  public void checkUser(@RequestBody WhatsAppBotDto.UserCheckRequest userCheckRequest) {
    whatsAppBotService.checkUser(userCheckRequest);
  }

  @PostMapping("/passwords")
  public void updatePassword(@RequestBody WhatsAppBotDto.UserCheckRequest request) {
    whatsAppBotService.updatePassword(request);
  }

  @PostMapping("/subjective-correction:pdf")
  public void subjectiveCorrection(@RequestBody WhatsAppBotDto.UserCheckRequest userCheckRequest) {
    whatsAppBotService.subjectiveCorrection(userCheckRequest);
  }
}
