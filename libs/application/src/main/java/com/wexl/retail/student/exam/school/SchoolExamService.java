package com.wexl.retail.student.exam.school;

import static com.wexl.retail.test.schedule.service.ScheduleTestService.ACTIVE;
import static com.wexl.retail.test.school.domain.TestType.*;
import static com.wexl.retail.test.school.service.TestDefinitionService.randomNumber;
import static com.wexl.retail.util.Constants.WEXL_INTERNAL;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.model.Entity;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.courses.enrollment.model.CourseScheduleInstStatus;
import com.wexl.retail.courses.enrollment.model.CourseScheduleItemInst;
import com.wexl.retail.courses.enrollment.model.CourseScheduleItemInstStatus;
import com.wexl.retail.courses.enrollment.repository.CourseScheduleInstRepository;
import com.wexl.retail.courses.step.model.CourseItemType;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.answer.StudentAnswerRequest;
import com.wexl.retail.student.answer.StudentAnswerResponse;
import com.wexl.retail.student.answer.StudentAnswerService;
import com.wexl.retail.student.exam.*;
import com.wexl.retail.task.domain.TaskStatus;
import com.wexl.retail.task.service.TaskInstService;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.domain.ScheduleTestMetadata;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.test.school.domain.ChapterMetadata;
import com.wexl.retail.test.school.domain.SubTopicMetadata;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.test.school.dto.TestDefinitionResponse;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.StrapiService;
import java.time.LocalDateTime;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class SchoolExamService {
  private final CourseScheduleInstRepository courseScheduleInstRepository;

  private static final int SURPRISE_TEST_DURATION = 60;
  private static final int DEFAULT_MCQ_ANSWER = 0;

  private final AuthService authService;
  private final ExamFactory examFactory;
  private final ExamService examService;
  private final DateTimeUtil dateTimeUtil;
  private final StrapiService strapiService;
  private final StorageService storageService;
  private final ExamRepository examRepository;
  private final ExamTransformer examTransformer;
  private final ScheduleTestService scheduleTestService;
  private final StudentAnswerService studentAnswerService;
  private final TestDefinitionService testDefinitionService;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final TestDefinitionRepository testDefinitionRepository;
  private final ScheduleTestRepository scheduleTestRepository;
  private final UserRepository userRepository;
  private static final String VOWELS = "vowels";
  private static final String CONSONANTS = "consonants";
  private static final Long VOWELS_TEST_DEFINITION_ID = 9202741L;
  private static final Long CONSONANTS_TEST_DEFINITION_ID = 9202740L;
  private static final Long DIPHTHONGS_TEST_DEFINITION_ID = 9202742L;

  private final TaskInstService taskInstService;

  public List<SchoolScheduledTestResponse> getAllScheduledTests() {
    long studentUserId = authService.getUserDetails().getId();
    return scheduleTestStudentRepository
        .findAllTestsForStudent(
            studentUserId, List.of(SCHOOL_TEST.name(), MOCK_TEST.name(), SCHEDULED_TEST.name()))
        .stream()
        .map(this::buildScheduleTestResponse)
        .toList();
  }

  public List<SchoolScheduledTestResponse> getScheduledAssignments() {
    long studentUserId = authService.getUserDetails().getId();
    return scheduleTestStudentRepository.getScheduledAssignments(studentUserId).stream()
        .map(this::buildScheduleTestResponse)
        .toList();
  }

  public SchoolScheduledTestResponse buildScheduleTestResponse(AllScheduledTests scheduleTest) {
    var testDefinition =
        testDefinitionRepository.findById(scheduleTest.getTestDefinitionId()).orElseThrow();
    return SchoolScheduledTestResponse.builder()
        .testDefinitionId(scheduleTest.getTestDefinitionId())
        .scheduleTestId(scheduleTest.getScheduleTestId())
        .startDate(dateTimeUtil.convertIso8601ToEpoch(scheduleTest.getStartDate()))
        .endDate(dateTimeUtil.convertIso8601ToEpoch(scheduleTest.getEndDate()))
        .status(
            scheduleTestService.getScheduledTestStatus(
                scheduleTest.getStartDate(), scheduleTest.getEndDate()))
        .subjectName(
            StringUtils.isEmpty(scheduleTest.getSubjectSlug())
                ? null
                : strapiService.getSubjectBySlug(scheduleTest.getSubjectSlug()).getName())
        .testName(scheduleTest.getTestName())
        .testState(scheduleTest.getTestState())
        .testType(getTestType(scheduleTest.getTestType()))
        .scheduleTestUuid(scheduleTest.getScheduleTestUuid())
        .category(testDefinition.getCategory().getValue())
        .build();
  }

  private TestType getTestType(String testType) {
    if (StringUtils.isBlank(testType)) {
      return SCHOOL_TEST;
    }
    return TestType.fromValue(testType);
  }

  public ExamResponse createScheduledTestExam(ScheduledExamRequest examRequest) {
    var scheduleTest = scheduleTestService.findTestScheduleById(examRequest.getScheduleTestId());

    boolean isScheduledTestCompleted =
        isScheduledTestCompleted(authService.getUserDetails().getId(), scheduleTest.getId());

    if (isScheduledTestCompleted) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Exam.Completed");
    }

    Optional<ScheduleTestStudent> scheduleTestStudent = getScheduleTestStudent(scheduleTest);
    if (scheduleTestStudent.isEmpty()) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.ScheduledTest.Unauthorized");
    }
    if (scheduleTestStudent.get().getStatus().equals(Constants.PENDING)) {
      scheduleTestStudent.get().setStatus(Constants.STARTED);
      scheduleTestStudentRepository.save(scheduleTestStudent.get());
    }

    return createExam(scheduleTest, examFactory.createScheduledTest(scheduleTest));
  }

  private Optional<ScheduleTestStudent> getScheduleTestStudent(ScheduleTest scheduleTest) {
    var userDetails = authService.getUserDetails();
    return scheduleTest.getScheduleTestStudent().stream()
        .filter(
            scheduleTestStudent ->
                scheduleTestStudent
                    .getStudent()
                    .getAuthUserId()
                    .equals(userDetails.getAuthUserId()))
        .findFirst();
  }

  public ExamResponse createExam(ScheduleTest scheduleTestDetails, Exam exam) {
    long testTakenCount =
        examRepository.getScheduleTestTakenCountByStudentId(
            authService.getStudentDetails().getStudentInfo().getId(), scheduleTestDetails.getId());
    if (testTakenCount > 0) {
      log.debug("Schedule Test is already taken");
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Test.Taken");
    }
    ChapterMetadata chapterMetadata =
        testDefinitionService.getChapterMetadata(scheduleTestDetails.getTestDefinition());
    SubTopicMetadata subTopicMetadata =
        testDefinitionService.getSubTopicMetadata(scheduleTestDetails.getTestDefinition());
    exam.setTestDefinition(scheduleTestDetails.getTestDefinition());
    exam.setScheduleTest(scheduleTestDetails);
    exam.setAllowedDuration(scheduleTestDetails.getDuration());
    if (!MOCK_TEST.equals(scheduleTestDetails.getTestDefinition().getType())) {
      Entity subjectBySlug =
          strapiService.getSubjectBySlug(scheduleTestDetails.getTestDefinition().getSubjectSlug());
      exam.setSubjectId(subjectBySlug.getId());
      exam.setSubjectSlug(subjectBySlug.getSlug());
      exam.setSubjectName(subjectBySlug.getName());
    }
    exam.setChapterName(chapterMetadata.getName());
    exam.setChapterSlug(chapterMetadata.getSlug());
    exam.setNoOfQuestions(scheduleTestDetails.getTestDefinition().getNoOfQuestions());
    exam.setSubtopicSlug(subTopicMetadata.getSlug());
    exam.setSubtopicName(subTopicMetadata.getName());
    exam = examRepository.save(exam);

    var examReponse = examTransformer.mapExamToExamResponse(exam);

    return updateQuestionPreviewDetails(examReponse, scheduleTestDetails);
  }

  private ExamResponse updateQuestionPreviewDetails(
      ExamResponse examResponse, ScheduleTest scheduleTest) {

    if (scheduleTest.getTestDefinition().getQuestionPath() == null) {
      return examResponse;
    }

    var presignedQuestionPreviewUrl =
        storageService.generatePreSignedUrlForFetch(
            scheduleTest.getTestDefinition().getQuestionPath());

    examResponse.setQuestionPreviewUrl(presignedQuestionPreviewUrl);
    return examResponse;
  }

  public ExamResponse createSchoolTestExam(SchoolExamRequest examRequest) {
    var testDefinition =
        scheduleTestService.findTestDefinitionById(examRequest.getTestDefinitionId());

    checkIfSchoolTestTaken(testDefinition);

    var exam = examFactory.createSchoolSurpriseTest(examRequest.getRef());
    return examTransformer.mapExamToExamResponse(saveSchoolTest(testDefinition, exam));
  }

  public ExamResponse createDirectAssignment(Long testDefinitionId, Long taskId, String taskType) {
    var testDefinition = scheduleTestService.findTestDefinitionById(testDefinitionId);

    var exam = examFactory.createAssignmentTask(taskId, taskType);
    return examTransformer.mapExamToExamResponse(saveSchoolTest(testDefinition, exam));
  }

  public ExamResponse createExamOfTypeTestDefinition(SchoolExamRequest examRequest, Exam exam) {
    var testDefinition =
        scheduleTestService.findTestDefinitionById(examRequest.getTestDefinitionId());

    exam.setChapterId(examRequest.getChapterId());
    exam.setCorrected(false);
    return examTransformer.mapExamToExamResponse(saveSchoolTest(testDefinition, exam));
  }

  private void checkIfSchoolTestTaken(TestDefinition testDefinition) {
    long testTakenCount =
        examRepository.getSurpriseTestTakenByStudentId(
            authService.getStudentDetails().getStudentInfo().getId(), testDefinition.getId());
    if (testTakenCount > 0) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Test.Taken");
    }
  }

  private Exam saveSchoolTest(TestDefinition testDefinition, Exam exam) {
    Entity subjectBySlug = strapiService.getSubjectBySlug(testDefinition.getSubjectSlug());
    ChapterMetadata chapterMetadata = testDefinitionService.getChapterMetadata(testDefinition);
    SubTopicMetadata subTopicMetadata = testDefinitionService.getSubTopicMetadata(testDefinition);
    exam.setAllowedDuration(SURPRISE_TEST_DURATION);
    exam.setSubjectId(subjectBySlug.getId());
    exam.setSubjectName(subjectBySlug.getName());
    exam.setChapterSlug(chapterMetadata.getSlug());
    exam.setChapterName(chapterMetadata.getName());
    exam.setSubtopicName(subTopicMetadata.getName());
    exam.setSubtopicSlug(subTopicMetadata.getSlug());
    exam.setNoOfQuestions(testDefinition.getNoOfQuestions());
    exam.setTestDefinition(testDefinition);
    exam.setSubjectSlug(subjectBySlug.getSlug());
    return examRepository.save(exam);
  }

  private Exam saveMockTest(TestDefinition testDefinition, Exam exam) {
    exam.setAllowedDuration(SURPRISE_TEST_DURATION);
    exam.setNoOfQuestions(testDefinition.getNoOfQuestions());
    exam.setTestDefinition(testDefinition);
    exam.setSubjectSlug(testDefinition.getSubjectSlug());
    return examRepository.save(exam);
  }

  public TestDefinitionResponse getAllSchoolTestQuestions(String bearerToken, long examId) {
    var examDetails = examRepository.getReferenceById(examId);
    TestDefinition testDefinition;
    if (examDetails.getScheduleTest() != null) {
      testDefinition = examDetails.getScheduleTest().getTestDefinition();
    } else if (examDetails.getTestDefinition() != null) {
      testDefinition = examDetails.getTestDefinition();
    } else {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Exam.Config");
    }

    if (testDefinition.getQuestionV1() != null && !testDefinition.getQuestionV1().isEmpty()) {
      return testDefinitionService.getQuestionsPreconfigured(testDefinition, randomNumber());
    }
    // Fallback to legacy way if the questions are not available
    return testDefinitionService.getTestDefinitionById(bearerToken, testDefinition.getId());
  }

  @Transactional
  public StudentAnswerResponse submitSchoolTest(
      StudentAnswerRequest studentAnswerRequest, String bearerToken) {
    var exam = examService.findById(studentAnswerRequest.getExamId());
    if (exam.isCompleted()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Exam.Completed");
    }

    studentAnswerRequest
        .getExamQuestions()
        .forEach(
            examQuestion -> {
              if (QuestionType.MCQ.equals(QuestionType.getByType(examQuestion.getType()))
                  && examQuestion.getSelectedAnswer() == DEFAULT_MCQ_ANSWER) {
                examQuestion.setSelectedAnswer(
                    Objects.nonNull(examQuestion.getAnswer()) && !examQuestion.getAnswer().isBlank()
                        ? Integer.parseInt(examQuestion.getAnswer())
                        : DEFAULT_MCQ_ANSWER);
              }
            });

    var studentAnswerResponse =
        studentAnswerService.submitExamAnswers(studentAnswerRequest, bearerToken);
    updateScheduleTestStatus(exam.getScheduleTest());
    if (Constants.COURSE_TEST == exam.getExamType()
        || Constants.COURSE_ASSIGNMENT == exam.getExamType()
        || Constants.SCHOOL_SCHEDULED_TEST_EXAM == exam.getExamType()) {
      taskInstService.updateTaskStatusIfPresent(exam, TaskStatus.COMPLETED);
    } else {
      taskInstService.updateTaskStatusIfPresent(exam, TaskStatus.PENDING);
    }
    handleCourseExam(exam);
    return studentAnswerResponse;
  }

  private void handleCourseExam(Exam exam) {
    if (exam.getCourseScheduleItemInst() == null) {
      return;
    }
    final CourseScheduleItemInst courseScheduleItemInst = exam.getCourseScheduleItemInst();
    courseScheduleItemInst.setStatus(CourseScheduleItemInstStatus.COMPLETED);
    courseScheduleItemInst.getCourseScheduleInst().setStatus(CourseScheduleInstStatus.IN_PROGRESS);
    courseScheduleInstRepository.save(courseScheduleItemInst.getCourseScheduleInst());
  }

  public void updateScheduleTestStatus(ScheduleTest scheduleTest) {
    if (scheduleTest == null) {
      return;
    }

    var studentId = authService.getUserDetails().getId();
    scheduleTestStudentRepository
        .findByTestScheduleIdStudentId(studentId, scheduleTest.getId())
        .ifPresentOrElse(
            scheduleTestStudent -> {
              scheduleTestStudent.setStatus(Constants.COMPLETED);
              scheduleTestStudentRepository.save(scheduleTestStudent);
            },
            () -> {
              throw new ApiException(
                  InternalErrorCodes.INVALID_REQUEST,
                  "error.StudentAssigned.Test",
                  new String[] {Long.toString(scheduleTest.getId()), Long.toString(studentId)});
            });
  }

  private boolean isScheduledTestCompleted(long studentId, long scheduleTestId) {
    Optional<ScheduleTestStudent> scheduledTest =
        scheduleTestStudentRepository.completedScheduledTest(studentId, scheduleTestId);
    return scheduledTest.isPresent();
  }

  public ExamResponse createDirectAssignmentForCourse(
      Long testDefinitionId, CourseScheduleItemInst courseScheduleItemInst) {
    var testDefinition = scheduleTestService.findTestDefinitionById(testDefinitionId);
    var courseItem = courseScheduleItemInst.getCourseItem();

    var exam = new Exam();
    if (CourseItemType.ASSIGNMENT.name().equals(courseItem.getItemType().name())) {
      exam = examFactory.createAssignmentTaskForCourse(courseScheduleItemInst);
      return examTransformer.mapExamToExamResponse(saveSchoolTest(testDefinition, exam));
    } else if (CourseItemType.SCHOOL_TEST.name().equals(courseItem.getItemType().name())) {
      exam = examFactory.createSchoolTaskForCourse(courseScheduleItemInst);
      return examTransformer.mapExamToExamResponse(saveSchoolTest(testDefinition, exam));
    } else if (CourseItemType.MOCK_TEST.name().equals(courseItem.getItemType().name())) {
      exam = examFactory.createMockTaskForCourse(courseScheduleItemInst);
      return examTransformer.mapExamToExamResponse(saveMockTest(testDefinition, exam));
    } else {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.invalidInput");
    }
  }

  public ExamResponse createLiveWorksheetExam(String orgSlug, long id) {
    var testDefinition = testDefinitionService.getTestDefinitionByIdAndOrgSlug(id, orgSlug);
    return examTransformer.mapExamToExamResponse(
        saveSchoolTest(testDefinition, examFactory.createLiveWorksheet()));
  }

  public SchoolScheduledTestResponse.ScheduleResponse schedulePhoneticsTest(
      String studentAuthId, String type) {
    var user = userRepository.getUserByAuthUserId(studentAuthId);
    TestDefinition testDefinition;
    if (VOWELS.equalsIgnoreCase(type)) {
      testDefinition =
          testDefinitionService.getTestDefinitionByIdAndOrgSlug(
              VOWELS_TEST_DEFINITION_ID, WEXL_INTERNAL);
    } else if (CONSONANTS.equalsIgnoreCase(type)) {
      testDefinition =
          testDefinitionService.getTestDefinitionByIdAndOrgSlug(
              CONSONANTS_TEST_DEFINITION_ID, WEXL_INTERNAL);
    } else {
      testDefinition =
          testDefinitionService.getTestDefinitionByIdAndOrgSlug(
              DIPHTHONGS_TEST_DEFINITION_ID, WEXL_INTERNAL);
    }

    List<ScheduleTestStudent> scheduleTestStudent =
        scheduleTestStudentRepository.findByTestDefinitionAndStudentId(
            user.getId(), testDefinition.getId());
    if (!scheduleTestStudent.isEmpty()) {
      return buildScheduleResponseIfPending(scheduleTestStudent.getFirst());
    }
    var scheduleTest = buildScheduleForStudent(user, testDefinition, new ScheduleTest());
    scheduleTest.setScheduleTestStudent(
        scheduleTestService.getScheduleTestStudents(
            Collections.singleton(user.getId()), scheduleTest));
    var scheduleTestById = scheduleTestRepository.save(scheduleTest);
    scheduleTestService.saveStudentScheduleTestAnswers(scheduleTestById);
    return SchoolScheduledTestResponse.ScheduleResponse.builder()
        .testScheduleId(scheduleTest.getId())
        .tssUuid(scheduleTest.getScheduleTestStudent().getFirst().getUuid())
        .testDefinitionId(testDefinition.getId())
        .build();
  }

  private SchoolScheduledTestResponse.ScheduleResponse buildScheduleResponseIfPending(
      ScheduleTestStudent scheduleTestStudent) {
    var scheduleTest = scheduleTestStudent.getScheduleTest();
    if (Constants.PENDING.equals(scheduleTestStudent.getStatus())
        || Constants.STARTED.equals(scheduleTestStudent.getStatus())) {
      return SchoolScheduledTestResponse.ScheduleResponse.builder()
          .testScheduleId(scheduleTest.getId())
          .tssUuid(scheduleTestStudent.getUuid())
          .testDefinitionId(scheduleTest.getTestDefinition().getId())
          .build();
    }
    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Test already attempted");
  }

  private ScheduleTest buildScheduleForStudent(
      User user, TestDefinition testDefinition, ScheduleTest scheduleTest) {
    Section section = user.getStudentInfo().getSection();
    scheduleTest.setStartDate(LocalDateTime.now());
    scheduleTest.setEndDate(LocalDateTime.now().plusMonths(6));
    scheduleTest.setStatus(ACTIVE);
    scheduleTest.setMessage("All The Best!");
    scheduleTest.setDuration(15);
    scheduleTest.setAllStudents(false);
    scheduleTest.setTestDefinition(testDefinition);
    scheduleTest.setMetadata(
        buildScheduleTestMetaData(Collections.singletonList(section.getName()), section));
    scheduleTest.setTeacher(scheduleTestService.getAdminTeacher());
    scheduleTest.setPublished("false");
    scheduleTest.setType(Constants.DEFAULT_EXAM_SCHEDULETYPE);
    scheduleTest.setOrgSlug(user.getStudentInfo().getUserInfo().getOrganization());
    return scheduleTest;
  }

  private ScheduleTestMetadata buildScheduleTestMetaData(List<String> sections, Section section) {
    return ScheduleTestMetadata.builder()
        .board(section.getBoardSlug())
        .grade(section.getGradeSlug())
        .sections(sections)
        .build();
  }
}
