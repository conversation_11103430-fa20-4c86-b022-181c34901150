package com.wexl.retail.teacher.question;

import static com.wexl.retail.util.Constants.WEXL_INTERNAL;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsTeacherOrStudent;
import com.wexl.retail.liveworksheet.dto.LiveWorkSheetDto;
import com.wexl.retail.liveworksheet.service.LiveWorkSheetService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RequiredArgsConstructor
@RestController
public class QuestionsController {

  private final LiveWorkSheetService liveWorkSheetService;

  @IsTeacherOrStudent
  @PostMapping("/question/media/upload")
  public List<LiveWorkSheetDto.ImageQuestionResponse> uploadQuestionMedia(
      @RequestPart(value = "files") List<MultipartFile> multipartFiles) {
    if (multipartFiles == null || multipartFiles.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidRequest");
    }
    return List.of(
        liveWorkSheetService.uploadImageQuestion(WEXL_INTERNAL, multipartFiles.getFirst()));
  }
}
