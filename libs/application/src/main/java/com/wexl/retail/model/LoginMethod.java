package com.wexl.retail.model;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum LoginMethod {
  MOBILE_NUMBER("mobile_number"),
  USERNAME_PASSWORD("username_password"),
  SYSTEM_CREATED("system_created"),
  GOOGLE("google");
  private final String value;

  public static LoginMethod fromValue(String value) {
    if (value == null || value.isEmpty()) {
      throw new IllegalArgumentException("Value cannot be null or empty!");
    }

    for (LoginMethod enumEntry : LoginMethod.values()) {
      if (enumEntry.toString().equalsIgnoreCase(value)) {
        return enumEntry;
      }
    }

    throw new IllegalArgumentException("Do not understand the Type " + value);
  }

  @Override
  public String toString() {
    return this.value;
  }
}
