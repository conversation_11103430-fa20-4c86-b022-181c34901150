package com.wexl.retail.util;

import java.text.DecimalFormat;
import java.util.List;

public class Constants {

  public static final int ALLLOWED_DURATION = 60;
  public static final long TEST_EXAM = 2L;
  public static final long PRACTICE_EXAM = 1L;
  public static final long SCHOOL_SCHEDULED_TEST_EXAM = 4L;
  public static final long SCHOOL_SURPRISE_TEST_EXAM = 5L;
  public static final long WORKSHEET = 6L;
  public static final long ASSIGNMENT_EXAM = 7L;
  public static final long COURSE_TEST = 10L;
  public static final long COURSE_ASSIGNMENT = 9L;
  public static final long MOCK_TEST = 11L;
  public static final long LIVE_WORKSHEET = 12L;
  public static final long ELP_EXAM = 13L;
  public static final long COURSE_MOCK_TEST = 14L;
  public static final long BET_EXAM = 15L;
  public static final long EBC_EXAM = 16L;
  public static final String REJECTED = "REJECTED";
  public static final String ACCEPTED = "ACCEPTED";
  public static final String FAILED = "FAILED";
  public static final String PENDING = "PENDING";
  public static final String SUCCESS = "SUCCESS";
  public static final String COMPLETED = "COMPLETED";
  public static final String SUBMITTED = "SUBMITTED";
  public static final String DATE_FORMAT = "dd-MM-yyyy";

  public static final String STARTED = "STARTED";

  public static final String ERRORINPROCESSING = "ERROR_IN_PROCESSING";
  public static final String NOTIFICATION_MESSAGE = "Your teacher has assigned you a new %s ";

  public static final String NOTEVALUATED = "NOT_EVALUATED";
  public static final long DAY = 1000L * 60 * 60 * 24;
  public static final String WORKSHEETS = "worksheets";
  public static final String READ = "READ";
  public static final String TRANSCTION_TYPE_SUBSCRIPTION = "SUBSCRIPTION";
  public static final String TRANSCTION_TYPE_BUYPOINT = "BUY_POINT";
  public static final String TRANSCTION_IN_PROGRESS = "IN_PROGRESS";
  public static final String GIFTCARD_OTP_REFERENCE = "GIFT_CARD_PURCHASE";
  public static final String EMAIL_OTP_REFERENCE = "EMAIL_VERIFICATION";
  public static final String STUDENT_ACCOUNT_OTP_REFERENCE = "STUDENT_ACCOUNT_VERIFICATION";
  public static final boolean BEGIN_EXAM_COMPLETION_STATUS = false;
  public static final String AUTHORIZATION_HEADER = "Authorization";

  public static final String BACK_SLASH = "/";
  public static final String COMMA_SEPERATOR = ",";
  public static final String TEST_EVENT_SQL = "TEST_EVENT_QUERY";

  public static final String TEACHER_ZOOM_PATH = "/teacher/#/zoom/";
  public static final String STUDENT_ZOOM_PATH = "/student/#/zoom/";
  public static final String ZOOM_STANDARD_WEB_URL = "https://us04web.zoom.us/j/%s";
  public static final String ZOOM_STANDARD_WEB_URL_WITH_PWD = "https://us04web.zoom.us/j/%s?pwd=%s";
  public static final String EMAIL_VERIFICATION_REFERENCE = "EMAIL_VERIFICATION";
  public static final String MOBILE_VERIFICATION_REFERENCE = "MOBILE_VERIFICATION";

  public static final String LOCAL_PROFILE = "local";
  public static final String DEV_PROFILE = "dev";
  public static final String ND_PROD_PROFILE = "ndprod";
  public static final String ND_DEV_PROFILE = "nddev";
  public static final String TEST_PROFILE = "test";

  //  public static final String GOD_FATHER_EMAIL = "<EMAIL>";

  public static final String DEFAULT_EXAM_SCHEDULETYPE = "institution";
  public static final String DEFAULT_GROUP_EXAM_SCHEDULETYPE = "institution_group";
  public static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("#.##");

  public static final long REVISION_EXAM = 8L;

  public static final String DEFAULT_QUESTION_UUID = "74a04c43-**************-03c2a1844ca3";
  public static final String DEFAULT_SUBJECT_SLUG = "mathematics";
  public static final long DEFAULT_SUBJECT_ID = 1;

  public static final String DEFAULT_COUNTRY_CODE = "+91";

  public static final String MOBILE_NUMBER_SIGNUP = "mobile_number_signup";
  public static final String DART_USER_AGENT = "Dart";
  public static final String MOBILE_USER_AGENT = "mobile";

  public static final String ZIPPED = "zipped";
  public static final String DOCUMENTS = "documents";

  public static final String WEXL_INTERNAL = "wexl-internal";

  public static final String PENDING_STATUS = "PENDING";

  public static final String TEST_DEFINITION_QUESTIONS_STRING_PATH =
      "%s/test-definition-questions/%s/%s.json";

  public static final String LIVE_WORKSHEET_PATH = "%s/live-worksheets/%s.json";
  public static final String API_KEY =
      "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";

  public static final String TEST_DEFINITION_V2_QUESTIONS_STRING_PATH =
      "%s/test-definition-v2-questions/%s/%s.json";

  public static final List<String> ACCEPTED_ORGS_FOR_TXT_LOCAL =
      List.of("dps688668", "del765517", "del909850", "del189476", "del217242", "nal635825");

  private Constants() {
    throw new IllegalStateException("Utility class");
  }
}
