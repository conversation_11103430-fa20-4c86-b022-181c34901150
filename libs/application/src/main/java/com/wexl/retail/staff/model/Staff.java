package com.wexl.retail.staff.model;

import com.wexl.retail.globalprofile.model.RoleTemplate;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "staff_details")
public class Staff extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToMany(fetch = FetchType.EAGER)
  @JoinTable(
      name = "staff_details_role",
      joinColumns = @JoinColumn(name = "staff_id"),
      inverseJoinColumns = @JoinColumn(name = "role_id"))
  private List<RoleTemplate> role;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "designation_id")
  private Designation designation;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "department_id")
  private Department department;

  private String joiningDate;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id")
  private User user;
}
