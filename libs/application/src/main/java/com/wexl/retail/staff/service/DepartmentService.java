package com.wexl.retail.staff.service;

import com.wexl.retail.staff.dto.StaffDto;
import com.wexl.retail.staff.model.Department;
import com.wexl.retail.staff.repository.DepartmentRepository;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DepartmentService {

  private final DepartmentRepository departmentRepository;
  private final StaffService staffService;

  public StaffDto.DepartmentResponse createDepartment(
      StaffDto.DepartmentRequest request, String orgSlug) {
    Optional<Department> existingDepartment = departmentRepository.findByName(request.name());
    if (existingDepartment.isPresent()) {
      throw new IllegalArgumentException(
          "Department with name " + request.name() + " already exists");
    }
    Department department =
        Department.builder()
            .name(request.name())
            .description(request.description())
            .orgSlug(orgSlug)
            .is_active(request.active())
            .build();

    Department savedDepartment = departmentRepository.save(department);
    return staffService.mapToDepartmentResponse(savedDepartment);
  }

  public StaffDto.DepartmentResponse getDepartmentById(Long id, String orgSlug) {
    Department department =
        departmentRepository
            .findByIdAndOrgSlug(id, orgSlug)
            .orElseThrow(() -> new IllegalArgumentException("Department not found with id: " + id));
    return staffService.mapToDepartmentResponse(department);
  }

  public List<StaffDto.DepartmentResponse> getAllDepartments() {
    return departmentRepository.findAll().stream()
        .map(staffService::mapToDepartmentResponse)
        .collect(Collectors.toList());
  }

  public StaffDto.DepartmentResponse updateDepartment(Long id, StaffDto.DepartmentRequest request) {
    Department department =
        departmentRepository
            .findById(id)
            .orElseThrow(() -> new IllegalArgumentException("Department not found with id: " + id));

    department.setName(request.name());
    department.setDescription(request.description());
    department.setIs_active(request.active());

    Department updatedDepartment = departmentRepository.save(department);
    return staffService.mapToDepartmentResponse(updatedDepartment);
  }

  public void deleteDepartment(Long id) {
    if (!departmentRepository.existsById(id)) {
      throw new IllegalArgumentException("Department not found with id: " + id);
    }
    departmentRepository.deleteById(id);
  }
}
