package com.wexl.retail.student.home;

import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ActivitySummary {
  private String authUserId;
  private String name;
  private long totalPracticeCount;
  private long totalEventCount;
  private long totalTestCount;
  private List<String> sectionUuid;
  private long studentId;
  private String grade;
  private long boardId;
  private long gradeId;
  private long totalExamQuestionCountNotCompleted;
}
