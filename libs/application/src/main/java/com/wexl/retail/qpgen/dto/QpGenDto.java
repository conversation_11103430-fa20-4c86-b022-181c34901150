package com.wexl.retail.qpgen.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.content.model.QuestionType;
import java.util.List;
import lombok.Builder;

public record QpGenDto() {

  @Builder
  public record Request(
      String title,
      Long marks,
      Long duration,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("board_name") String boardName,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("qp_type") QpType type,
      List<Sections> sections) {}

  @Builder
  public record SectionsResponse(
      @JsonProperty("test_definitions") List<Long> testDefinitionId,
      @JsonProperty("title") String title,
      @JsonProperty("marks") Long marks,
      @JsonProperty("duration") Long duration,
      @JsonProperty("board_name") String boardName,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("subject_name") String subjectName,
      List<Sections> sections) {}

  @Builder
  public record Sections(
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("questions") List<Questions> questions) {}

  @Builder
  public record ContentRequest(List<Sections> sections) {}

  @Builder
  public record Questions(
      @JsonProperty("msq_questions") List<QuestionDetails> msqQuestions,
      @JsonProperty("speech_questions") List<QuestionDetails> speechQuestions,
      @JsonProperty("nat_questions") List<QuestionDetails> natQuestions,
      @JsonProperty("fbq_questions") List<QuestionDetails> fbqQuestions,
      @JsonProperty("true_false_questions") List<QuestionDetails> trueFalseQuestions,
      @JsonProperty("pbq_questions") List<QuestionDetails> pbqQuestions,
      @JsonProperty("mcq_questions") List<QuestionDetails> mcqQuestions,
      @JsonProperty("subjective_questions") List<QuestionDetails> subjectiveQuestions) {}

  @Builder
  public record QuestionDetails(
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("chapter_name") String chapterName,
      Long marks,
      @JsonProperty("question_tags") String questionTags,
      @JsonProperty("available_questions_count") Long availableQuestionsCount,
      @JsonProperty("required_questions_count") Long requiredQuestionsCount) {}

  @Builder
  public record QpGenResponse(
      Long sectionId, String sectionName, List<QuestionsResponse> questionsList) {}

  public record QuestionsResponse(
      Long marks,
      QuestionType type,
      String uuid,
      String answer,
      String chapterName,
      String chapterSlug,
      String category,
      String questionTag,
      String categorySlug,
      String complexity,
      String complexitySlug) {}

  @Builder
  public record Response(
      String title,
      Long marks,
      Long duration,
      @JsonProperty("created_at") Long createdAt,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("qp_type") QpType type,
      Long id,
      String status) {}
}
