package com.wexl.retail.student.registration.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SignupFactory {

  private final CommerceCustomerSignup commerceCustomerSignup;

  @Value("${app.orgs.allowRegistrationForCommerce}")
  private List<String> allowRegistrationForCommerceOrgs;

  public DefaultStudentSignup signupBean(String orgSlug) {
    if (allowRegistrationForCommerceOrgs.contains(orgSlug)) {
      return commerceCustomerSignup;
    }
    throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SignupError");
  }
}
