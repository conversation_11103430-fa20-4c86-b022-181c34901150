package com.wexl.retail.metrics;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@Data
@ConfigurationProperties(prefix = "app.metrics")
public class MetricConfig {
  private List<Config> config;

  @Data
  public static class Config {
    private int id;
    private String name;
    private String title;
    private String namespace;
  }
}
