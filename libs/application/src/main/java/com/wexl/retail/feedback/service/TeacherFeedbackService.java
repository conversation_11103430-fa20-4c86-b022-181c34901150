package com.wexl.retail.feedback.service;

import com.wexl.retail.classroom.core.repository.ClassroomRepository;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.feedback.dto.TeacherFeedbackDto;
import com.wexl.retail.feedback.repository.TeacherFeedBackCounts;
import com.wexl.retail.feedback.repository.TeacherFeedbackCountDetails;
import com.wexl.retail.feedback.repository.TeacherFeedbackDetails;
import com.wexl.retail.messagetemplate.model.MessageTemplate;
import com.wexl.retail.messagetemplate.repository.MessageTemplateRepository;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.studentfeedback.model.FeedbackType;
import com.wexl.retail.studentfeedback.repository.StudentFeedbackRepository;
import com.wexl.retail.studentfeedback.service.StudentFeedbackService;
import com.wexl.retail.task.domain.TaskType;
import com.wexl.retail.util.ValidationUtils;
import java.time.LocalDateTime;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TeacherFeedbackService {
  private final DateTimeUtil dateTimeUtil;
  private final StudentFeedbackRepository studentFeedbackRepository;
  private final TeacherRepository teacherRepository;
  private final ValidationUtils validationUtils;

  private final StudentFeedbackService studentFeedbackService;
  private final MessageTemplateRepository messageTemplateRepository;
  private final StudentRepository studentRepository;
  private final ClassroomRepository classroomRepository;

  public List<GenericMetricResponse> getFeedbackCounts(
      String orgSlug, Long fromDateInEpoch, Long toDateInEpoch) {
    List<GenericMetricResponse> genericMetricResponse = new ArrayList<>();
    LocalDateTime fromDate = dateTimeUtil.convertEpochToIso8601Legacy(fromDateInEpoch);
    LocalDateTime toDate = dateTimeUtil.convertEpochToIso8601Legacy(toDateInEpoch);
    var teacherDetails = teacherRepository.getAllClassroomTeacherId(orgSlug);
    var assignmentFeedbackCount =
        studentFeedbackRepository.getFeedbackCountsByTeacher(
            fromDate, toDate, TaskType.ASSIGNMENT.name());
    var testFeedbackCount =
        studentFeedbackRepository.getFeedbackCountsByTeacher(
            fromDate, toDate, TaskType.TEST.name());
    var overAllFeedbackCount = studentFeedbackRepository.getOverallFeedbackCounts(fromDate, toDate);

    teacherDetails.forEach(
        teacher ->
            genericMetricResponse.add(
                GenericMetricResponse.builder()
                    .data(
                        buildResponse(
                            teacher,
                            assignmentFeedbackCount,
                            testFeedbackCount,
                            overAllFeedbackCount))
                    .build()));

    return genericMetricResponse;
  }

  private Map<String, Object> buildResponse(
      TeacherFeedbackDetails teacher,
      List<TeacherFeedBackCounts> assignmentFeedbackData,
      List<TeacherFeedBackCounts> testFeedbackData,
      List<TeacherFeedBackCounts> overAllFeedbackData) {
    Map<String, Object> map = new HashMap<>();

    var getAssignmentFeedback =
        getAssignmentFeedbackCounts(assignmentFeedbackData, teacher.getTeacherId());
    var getOverAllFeedback = getOverAllFeedbackCounts(overAllFeedbackData, teacher.getTeacherId());
    var getTestFeedback = getTestFeedbackCounts(testFeedbackData, teacher.getTeacherId());
    map.put("assignment_feedback_count", getAssignmentFeedback.assignmentFeedbackCount());
    map.put(
        "assignment_feedback_pending_count",
        getAssignmentFeedback.assignmentFeedbackPendingCount());
    map.put("test_feedback_count", getTestFeedback.testFeedbackCount());
    map.put("test_feedback_pending_count", getTestFeedback.testFeedbackPendingCount());
    map.put("overall_feedback_count", getOverAllFeedback.overAllFeedbackCount());
    map.put("overall_feedback_pending_count", getOverAllFeedback.overAllFeedbackPendingCount());
    map.put("tutor_id", teacher.getTeacherId());
    map.put("tutor_name", teacher.getFullName());
    map.put("tutor_auth_id", teacher.getAuthUserId());

    return map;
  }

  private TeacherFeedbackDto.TestCount getTestFeedbackCounts(
      List<TeacherFeedBackCounts> testFeedbackData, Long teacherId) {
    var testFeedback =
        testFeedbackData.stream()
            .filter(
                data ->
                    data.getTeacherId().equals(teacherId)
                        && data.getFeedBack().equals(Boolean.TRUE))
            .findFirst();
    var testFeedbackPending =
        testFeedbackData.stream()
            .filter(
                data ->
                    data.getTeacherId().equals(teacherId)
                        && data.getFeedBack().equals(Boolean.FALSE))
            .findFirst();
    return TeacherFeedbackDto.TestCount.builder()
        .testFeedbackCount(testFeedback.isEmpty() ? 0 : testFeedback.get().getTaskCount())
        .testFeedbackPendingCount(
            testFeedbackPending.isEmpty() ? 0 : testFeedbackPending.get().getTaskCount())
        .build();
  }

  private TeacherFeedbackDto.AssignmentCount getAssignmentFeedbackCounts(
      List<TeacherFeedBackCounts> assignmentFeedbackData, Long teacherId) {
    var assignmentFeedback =
        assignmentFeedbackData.stream()
            .filter(
                data ->
                    data.getTeacherId().equals(teacherId)
                        && data.getFeedBack().equals(Boolean.TRUE))
            .findFirst();
    var assignmentPending =
        assignmentFeedbackData.stream()
            .filter(
                data ->
                    data.getTeacherId().equals(teacherId)
                        && data.getFeedBack().equals(Boolean.FALSE))
            .findFirst();
    return TeacherFeedbackDto.AssignmentCount.builder()
        .assignmentFeedbackCount(
            assignmentFeedback.isEmpty() ? 0 : assignmentFeedback.get().getTaskCount())
        .assignmentFeedbackPendingCount(
            assignmentPending.isEmpty() ? 0 : assignmentPending.get().getTaskCount())
        .build();
  }

  private TeacherFeedbackDto.OverAllCount getOverAllFeedbackCounts(
      List<TeacherFeedBackCounts> overAllFeedbackCount, Long teacherId) {
    var overallFeedback =
        overAllFeedbackCount.stream()
            .filter(data -> data.getTeacherId().equals(teacherId))
            .findFirst();
    return TeacherFeedbackDto.OverAllCount.builder()
        .overAllFeedbackCount(
            overallFeedback.isEmpty() ? 0 : overallFeedback.get().getOverAllFeedBackGivenCount())
        .overAllFeedbackPendingCount(
            overallFeedback.isEmpty() ? 0 : overallFeedback.get().getOverAllFeedBackNotGivenCount())
        .build();
  }

  public TeacherFeedbackDto.TeacherFeedbackSummary getFeedbackSummary(
      String orgSlug, Long fromDate, Long toDate, String teacherAuthId, String feedbackType) {
    var teacherUser = validationUtils.isValidUser(teacherAuthId);
    LocalDateTime fDate = dateTimeUtil.convertEpochToIso8601Legacy(fromDate);
    LocalDateTime tDate = dateTimeUtil.convertEpochToIso8601Legacy(toDate);
    List<TeacherFeedbackCountDetails> teacherFeedbackCountDetails;
    if (FeedbackType.SUMMARY.name().equals(feedbackType)) {
      teacherFeedbackCountDetails =
          studentFeedbackRepository.getOverallFeedbackDetails(
              orgSlug,
              teacherUser.getTeacherInfo().getId(),
              FeedbackType.SUMMARY.name(),
              fDate,
              tDate);
    } else {
      teacherFeedbackCountDetails =
          studentFeedbackRepository.getFeedbackDetails(
              orgSlug, teacherUser.getTeacherInfo().getId(), fDate, tDate, feedbackType);
    }

    return TeacherFeedbackDto.TeacherFeedbackSummary.builder()
        .summary(
            buildSummary(teacherUser, teacherFeedbackCountDetails, fromDate, toDate, feedbackType))
        .data(buildData(teacherUser, teacherFeedbackCountDetails, feedbackType))
        .build();
  }

  private List<TeacherFeedbackDto.Data> buildData(
      User teacherUser,
      List<TeacherFeedbackCountDetails> teacherFeedbackCountDetails,
      String feedBackType) {
    List<TeacherFeedbackDto.Data> data = new ArrayList<>();
    teacherFeedbackCountDetails.forEach(
        feedback -> {
          var student = validationUtils.isStudentValid(feedback.getStudentId());
          var user = student.getUserInfo();
          data.add(
              TeacherFeedbackDto.Data.builder()
                  .studentAuthId(user.getAuthUserId())
                  .studentName(user.getFirstName() + " " + user.getLastName())
                  .studentId(feedback.getStudentId())
                  .classroomName(feedback.getClassroomName())
                  .subjectName(feedback.getSubjectName())
                  .subjectSlug(feedback.getSubjectSlug())
                  .chapterName(feedback.getChapterName())
                  .chapterSlug(feedback.getChapterSlug())
                  .subtopicSlug(feedback.getSubtopicSlug())
                  .subtopicName(feedback.getSubtopicName())
                  .taskInstId(feedback.getTaskInstId())
                  .activityName(feedback.getTaskName())
                  .feedBackMessageTemplates(
                      buildFeedBackMessage(student, feedback.getTaskInstId(), feedBackType))
                  .feedbackMessage(
                      FeedbackType.SUMMARY.name().equals(feedBackType)
                          ? feedback.getMessage()
                          : concatFeedbackMessage(student, feedback.getTaskInstId()))
                  .feedbackId(feedback.getId())
                  .build());
        });
    if (FeedbackType.SUMMARY.name().equals(feedBackType)) {
      var students =
          studentRepository.getClassroomStudentByTeacher(
              teacherUser.getTeacherInfo().getId(), teacherUser.getOrganization());

      List<Long> givenStudents =
          teacherFeedbackCountDetails.stream()
              .map(TeacherFeedbackCountDetails::getStudentId)
              .toList();
      List<Student> pendingStudentList =
          students.stream().filter(student -> !givenStudents.contains(student.getId())).toList();
      pendingStudentList.forEach(
          student -> {
            List<String> classrooms =
                studentRepository.getClassroomByTeacherAndByStudent(
                    teacherUser.getTeacherInfo().getId(),
                    teacherUser.getOrganization(),
                    student.getId());

            for (String classroom : classrooms) {
              data.add(
                  TeacherFeedbackDto.Data.builder()
                      .studentId(student.getId())
                      .classroomName(classroom)
                      .studentAuthId(student.getUserInfo().getAuthUserId())
                      .studentName(
                          student.getUserInfo().getFirstName()
                              + " "
                              + student.getUserInfo().getLastName())
                      .build());
            }
          });
    }
    return data;
  }

  private List<TeacherFeedbackDto.MessageTemplateResponse> buildFeedBackMessage(
      Student student, Long taskInstId, String type) {
    if (FeedbackType.SUMMARY.name().equals(type)) {
      return Collections.emptyList();
    }
    List<TeacherFeedbackDto.MessageTemplateResponse> messageList = new ArrayList<>();
    var studentFeedback = studentFeedbackRepository.findByStudentAndTaskInst(student, taskInstId);
    if (studentFeedback.isEmpty()) {
      return Collections.emptyList();
    }
    var messageTemplates =
        messageTemplateRepository.findAllById(
            studentFeedback.get().getMessageTemplates().messageTemplate());
    messageTemplates.forEach(
        template ->
            messageList.add(
                TeacherFeedbackDto.MessageTemplateResponse.builder()
                    .id(template.getId())
                    .message(template.getMessage())
                    .build()));
    return messageList;
  }

  private TeacherFeedbackDto.Summary buildSummary(
      User teacherUser,
      List<TeacherFeedbackCountDetails> teacherFeedbackCountDetails,
      Long fromDate,
      Long toDate,
      String feedbackType) {

    return TeacherFeedbackDto.Summary.builder()
        .fromDate(fromDate)
        .toDate(toDate)
        .name(teacherUser.getFirstName() + " " + teacherUser.getLastName())
        .pendingFeedback(
            FeedbackType.SUMMARY.name().equals(feedbackType)
                ? getSummaryPendingFeedBackCount(teacherUser, teacherFeedbackCountDetails)
                : getPendingFeedBackCount(teacherFeedbackCountDetails, Boolean.FALSE, feedbackType))
        .totalFeedbackGiven(
            getPendingFeedBackCount(teacherFeedbackCountDetails, Boolean.TRUE, feedbackType))
        .feedbackType(feedbackType)
        .build();
  }

  private Long getPendingFeedBackCount(
      List<TeacherFeedbackCountDetails> teacherFeedbackCountDetails,
      Boolean isFeedBackGiven,
      String type) {
    if (FeedbackType.SUMMARY.name().equals(type)) {
      return (long) teacherFeedbackCountDetails.size();
    }
    return teacherFeedbackCountDetails.stream()
        .filter(x -> isFeedBackGiven.equals(x.getFeedBack()))
        .count();
  }

  private Long getSummaryPendingFeedBackCount(
      User teacherUser, List<TeacherFeedbackCountDetails> teacherFeedbackCountDetails) {
    var students =
        studentRepository.getClassroomStudentByTeacher(
            teacherUser.getTeacherInfo().getId(), teacherUser.getOrganization());
    List<Long> givenStudents =
        teacherFeedbackCountDetails.stream()
            .map(TeacherFeedbackCountDetails::getStudentId)
            .toList();
    return students.stream().filter(student -> !givenStudents.contains(student.getId())).count();
  }

  private String concatFeedbackMessage(Student student, Long taskInstId) {
    var studentFeedback = studentFeedbackRepository.findByStudentAndTaskInst(student, taskInstId);
    if (studentFeedback.isEmpty()) {
      return "";
    }
    var messageTemplates =
        messageTemplateRepository.findAllById(
            studentFeedback.get().getMessageTemplates().messageTemplate());
    var messagesList = messageTemplates.stream().map(MessageTemplate::getMessage).toList();
    return String.join(" ", messagesList);
  }
}
