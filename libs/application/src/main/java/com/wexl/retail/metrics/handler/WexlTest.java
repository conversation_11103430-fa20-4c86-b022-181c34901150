package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.WexlTestReportService;
import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class WexlTest extends AbstractMetricHandler implements MetricHandler {

  public final WexlTestReportService wexlTestReportService;

  @Override
  public String name() {
    return "wexl-test";
  }

  @Override
  public List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {

    List<String> subjectSlugs =
        Optional.ofNullable(genericMetricRequest.getInput().get(SUBJECT))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    List<String> gradeSlugs =
        Optional.ofNullable(genericMetricRequest.getInput().get(GRADE))
            .map(List.class::cast)
            .orElse(Collections.emptyList());

    return wexlTestReportService.getWexlTestsReport(
        org, subjectSlugs, gradeSlugs, genericMetricRequest.getTimePeriod());
  }
}
