package com.wexl.retail.auth.validator;

import static com.wexl.retail.auth.AuthUtil.*;

import com.wexl.retail.model.User;

public class TeacherValidator implements RoleValidator {

  public boolean isValidRole(User currentUser, String userId) {
    return currentUser != null && (isOrgAdmin(currentUser) || isValidTeacher(currentUser, userId));
  }

  private boolean isValidTeacher(User currentUser, String userId) {
    return isTeacher(currentUser) && currentUser.getAuthUserId().equals(userId);
  }
}
