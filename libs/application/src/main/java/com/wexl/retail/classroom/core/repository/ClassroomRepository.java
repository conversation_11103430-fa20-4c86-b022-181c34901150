package com.wexl.retail.classroom.core.repository;

import com.wexl.retail.classroom.core.model.Classroom;
import com.wexl.retail.metrics.dto.StudentClassReportInterface;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface ClassroomRepository extends JpaRepository<Classroom, Long> {

  Optional<Classroom> findByNameAndOrgSlug(String name, String org);

  Optional<Classroom> findByIdAndOrgSlug(long classroomId, String orgSlug);

  List<Classroom> findByOrgSlugAndDeletedAtIsNull(String orgSlug);

  List<Classroom> findByOrgSlugAndDeletedAtIsNullOrderByCreatedAtDesc(String orgSlug);

  List<Classroom> findByTeachersInAndOrgSlugAndDeletedAtIsNullOrderByCreatedAtDesc(
      List<Teacher> teachers, String orgSlug);

  List<Classroom> findByTeachersInAndOrgSlugAndDeletedAtIsNull(
      List<Teacher> teachers, String orgSlug);

  List<Classroom> findByStudentsAndDeletedAtIsNullOrderByCreatedAtDesc(Student student);

  @Query(
      value =
          """
                  select distinct student_id from classroom_students cs
                  join students s on s.id = cs.student_id
                  join classrooms c on cs.classroom_id = c.id
                  where classroom_id in (:classroomIds)
                  and s.deleted_at is null and c.deleted_at is null
                  """,
      nativeQuery = true)
  List<Long> getStudentIdsByClassRoom(List<Long> classroomIds);

  List<Classroom> findAllByParent(Classroom parentClassroom);

  @Query(
      value =
          """
          select * from classrooms  where  parent_classroom_id in (
                    select id from classrooms  where org_slug = :orgSlug and deleted_at is NULL)
                    union
                    select * from classrooms where org_slug = :orgSlug  and deleted_at is NULL
                    order by org_slug desc""",
      nativeQuery = true)
  List<Classroom> getGroupClassrooms(String orgSlug);

  @Query(
      value =
          """
                  select distinct to_char(csi.start_time ,'yyyy-MM-dd') from classroom_students s join classrooms c on s.classroom_id = c.id
                  join classroom_schedules cs on cs.classroom_id = c.id
                  join classroom_schedule_inst csi on csi.classroom_schedule_id = cs.id
                  where s.student_id =:studentId order by to_char(csi.start_time ,'yyyy-MM-dd')  desc  limit :limit
                  """,
      nativeQuery = true)
  List<String> getStudentClassroomDates(Long studentId, int limit);

  @Query(
      value =
          """
                          select distinct to_char(csi.start_time ,'yyyy-MM-dd') from classroom_students
                          s join classrooms c on s.classroom_id = c.id
                          join classroom_schedules cs on cs.classroom_id = c.id
                          join classroom_schedule_inst csi on csi.classroom_schedule_id = cs.id
                          where s.student_id =:studentId and csi.start_time between :fDate and :tDate
                                  """,
      nativeQuery = true)
  List<String> getStudentClassroomsByDate(Long studentId, LocalDateTime fDate, LocalDateTime tDate);

  @Transactional
  @Modifying
  @Query(
      value =
          """
                delete FROM classroom_students where student_id = :studentId
                and classroom_id in (:classroomIds)
                """,
      nativeQuery = true)
  void deleteStudentInClassroom(Long studentId, List<Long> classroomIds);

  @Query(
      value =
          """
                                  select * from classrooms c where id = :classroomIds
                                          """,
      nativeQuery = true)
  Optional<Classroom> findById(List<Long> classroomIds);

  @Query(
      value =
          """
                                          select * from classrooms c where id in (:classroomIds)
                                                  """,
      nativeQuery = true)
  List<Classroom> getClassroomsByClassroomIds(List<Long> classroomIds);

  @Query(
      value =
          """
                  select c.* from applicant a
                  join classroom_students cs on cs.student_id=a.student_id
                  join classrooms c on c.id=cs.classroom_id
                  where a.id=:applicantId
                  """,
      nativeQuery = true)
  List<Classroom> getAllProductAndClassroom(Long applicantId);

  @Query(
      value =
          """
                                   select distinct to_char(csi.start_time ,'yyyy-MM-dd') from classroom_students s join classrooms c on s.classroom_id = c.id
                                                    join classroom_schedules cs on cs.classroom_id = c.id
                                                    join classroom_schedule_inst csi on csi.classroom_schedule_id = cs.id
                                                    where c.name in (:classRoomNames)
                                  """,
      nativeQuery = true)
  List<String> getClassroomDates(List<String> classRoomNames);

  @Query(
      value =
          """
                  SELECT
                    c.name AS classroom,
                    CONCAT(u.first_name, ' ', u.last_name) AS studentName,

                    SUM(CASE WHEN siad.attendance_status = 'PRESENT' THEN 1 ELSE 0 END) AS presentCount,
                    SUM(CASE WHEN siad.attendance_status = 'ABSENT' THEN 1 ELSE 0 END) AS absentCount,

                    SUM(CASE
                        WHEN csi.status = 'COMPLETED'
                             AND siad.id IS NOT NULL
                             AND (siad.attendance_status IS NULL OR siad.attendance_status = 'NOT_MARKED')
                        THEN 1 ELSE 0 END
                    ) AS notMarked,

                    SUM(CASE
                        WHEN siad.id IS NOT NULL AND (
                             siad.attendance_status IN ('PRESENT', 'ABSENT')
                             OR (csi.status = 'COMPLETED' AND (siad.attendance_status IS NULL OR siad.attendance_status = 'NOT_MARKED'))
                        )
                        THEN 1 ELSE 0 END
                    ) AS totalCount

                  FROM users u
                  JOIN students st ON st.user_id = u.id
                  JOIN sections sc ON sc.id = st.section_id
                  JOIN classroom_students cs ON cs.student_id = st.id
                  JOIN classrooms c ON c.id = cs.classroom_id
                  JOIN classroom_schedules css ON css.classroom_id = c.id
                  JOIN classroom_schedule_inst csi
                    ON csi.classroom_schedule_id = css.id
                   AND csi.status IN ('COMPLETED', 'NOT_STARTED')
                  LEFT JOIN schedule_inst_attendance sia
                    ON sia.classroom_schedule_inst_id = csi.id
                  LEFT JOIN schedule_inst_attendance_details siad
                    ON siad.student_id = st.id
                   AND siad.schedule_inst_attendance_id = sia.id

                  WHERE c.id IN (:classroomId)
                    AND u.organization = :orgSlug
                    AND csi.start_time BETWEEN :fromDate AND :endDate

                  GROUP BY c.name, CONCAT(u.first_name, ' ', u.last_name);
        """,
      nativeQuery = true)
  List<StudentClassReportInterface> getStudentData(
      String orgSlug, Long classroomId, LocalDateTime fromDate, LocalDateTime endDate);

  List<Classroom> findByIdInAndOrgSlugAndDeletedAtIsNull(List<Long> ids, String orgSlug);
}
