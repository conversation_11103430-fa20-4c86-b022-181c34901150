package com.wexl.retail.pdf.viewer.repository;

import com.wexl.retail.pdf.viewer.domain.HighlightTextRect;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

public interface HighlightTextRectsRepository
    extends JpaRepository<HighlightTextRect, Long>, JpaSpecificationExecutor<HighlightTextRect> {

  @Query(value = "select nextval('public.\"highlight_text_rect_id_seq\"')", nativeQuery = true)
  long getNextValOfHighlightTextRectIdSeq();
}
