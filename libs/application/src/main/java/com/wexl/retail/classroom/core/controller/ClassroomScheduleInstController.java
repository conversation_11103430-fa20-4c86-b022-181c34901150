package com.wexl.retail.classroom.core.controller;

import static com.wexl.retail.util.Constants.AUTHORIZATION_HEADER;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.classroom.core.dto.ClassroomScheduleInstRequest;
import com.wexl.retail.classroom.core.dto.ClassroomScheduleInstResponse;
import com.wexl.retail.classroom.core.dto.TaskResponse;
import com.wexl.retail.classroom.core.handler.ClassroomScheduleInstHandler;
import com.wexl.retail.classroom.core.service.ClassroomScheduleInstService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.model.User;
import com.wexl.retail.task.dto.StudentScheduleResponse;
import com.wexl.retail.task.dto.TaskRequest;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("orgs/{orgSlug}/schedule-insts")
@RequiredArgsConstructor
public class ClassroomScheduleInstController {

  private final ClassroomScheduleInstService classroomScheduleInstService;

  private final AuthService authService;

  private final List<ClassroomScheduleInstHandler> classroomScheduleInstHandler;

  @IsOrgAdminOrTeacher
  @GetMapping
  public List<ClassroomScheduleInstResponse> getClassroomSchedules(
      @PathVariable String orgSlug,
      @RequestParam(value = "from_date") Long fromDate,
      @RequestParam(value = "to_date") Long toDate) {
    User user = authService.getTeacherDetails();
    // Spring Framework ensures that higher order dependency that is injected will be executed first
    return classroomScheduleInstHandler
        .getFirst()
        .getClassroomSchedules(user, fromDate, toDate, orgSlug);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/{id}")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void meetingCompletionStatus(
      @PathVariable Long id,
      @PathVariable String orgSlug,
      @Valid @RequestBody ClassroomScheduleInstRequest classroomScheduleInstRequest) {
    try {
      classroomScheduleInstService.meetingCompletionStatus(
          id, orgSlug, classroomScheduleInstRequest);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.ClassRoomUpdate.Schedule", e);
    }
  }

  @IsOrgAdminOrTeacher
  @ResponseStatus(HttpStatus.CREATED)
  @PostMapping("/{instId}/tasks")
  public void createScheduleTasks(
      @PathVariable String orgSlug,
      @PathVariable("instId") Long scheduleInstId,
      @RequestBody List<TaskRequest> taskRequest,
      @RequestHeader(AUTHORIZATION_HEADER) String bearerToken) {
    classroomScheduleInstService.createScheduleTasks(
        scheduleInstId, orgSlug, taskRequest, bearerToken);
  }

  @IsStudent
  @GetMapping("/student/{studentId}/schedules")
  public List<StudentScheduleResponse> getStudentSchedules(
      @PathVariable String orgSlug,
      @Valid @PathVariable Long studentId,
      @RequestParam(required = false) Long date,
      @RequestParam(required = false, defaultValue = "100") Integer limit) {
    return classroomScheduleInstHandler
        .getFirst()
        .getStudentSchedules(orgSlug, studentId, limit, date);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/{InstId}/tasks")
  public TaskResponse getScheduleInstByClassroom(
      @PathVariable String orgSlug,
      @PathVariable(value = "InstId") Long scheduleInstId,
      @RequestParam(value = "from_date", required = false) Long fromDate,
      @RequestParam(value = "to_date", required = false) Long toDate,
      @RequestParam(required = false, defaultValue = "100") int limit) {

    return classroomScheduleInstService.getScheduleInstByscheduleInstsId(
        orgSlug, scheduleInstId, fromDate, toDate, limit);
  }

  @PostMapping("/migrate:tutors")
  @ResponseStatus(HttpStatus.CREATED)
  public void migrateTutors(@PathVariable String orgSlug) {
    classroomScheduleInstService.migrateTutors();
  }
}
