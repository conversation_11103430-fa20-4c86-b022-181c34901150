package com.wexl.retail.monitoring.service;

import com.wexl.retail.monitoring.entity.Endpoint;
import com.wexl.retail.monitoring.entity.EndpointPattern;
import com.wexl.retail.monitoring.repository.EndpointPatternRepository;
import com.wexl.retail.monitoring.repository.EndpointRepository;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.http.server.PathContainer;
import org.springframework.stereotype.Component;
import org.springframework.web.util.pattern.PathPattern;
import org.springframework.web.util.pattern.PathPatternParser;

@RequiredArgsConstructor
@Component
public class EndpointService {

  private final EndpointRepository endpointRepository;
  private final EndpointPatternRepository endpointPatternRepository;
  private final List<String> endpointPatterns = new ArrayList<>();

  public void saveEndpoint(String httpMethod, String requestUri) {
    Endpoint endpoint = new Endpoint();
    endpoint.setUri(requestUri);
    endpoint.setMethod(httpMethod);

    endpointPatterns.forEach(
        endpointPattern -> {
          if (isPatternMatchUri(
              trimSurroundingSquareBraces(endpointPattern), trimApiPrefix(requestUri))) {
            endpoint.setPattern(endpointPattern);
          }
        });

    endpointRepository.save(endpoint);
  }

  private String trimApiPrefix(String requestUri) {
    if (requestUri.startsWith("/api")) {
      return requestUri.substring(4);
    }
    return requestUri;
  }

  private String trimSurroundingSquareBraces(String pattern) {
    if (pattern == null || pattern.length() < 2) {
      return pattern;
    }
    if (pattern.startsWith("[") && pattern.endsWith("]")) {
      return pattern.substring(1, pattern.length() - 1);
    }
    return pattern;
  }

  public boolean populateEndpointPatterns() {
    return endpointPatternRepository.count() == 0;
  }

  public void saveEndpointPatterns(Set<String> endpoints) {
    final List<EndpointPattern> endpointPatterns =
        endpoints.stream()
            .map(
                endpoint -> {
                  EndpointPattern endpointPattern = new EndpointPattern();
                  endpointPattern.setPattern(endpoint);
                  return endpointPattern;
                })
            .toList();

    endpointPatternRepository.deleteAll();
    endpointPatternRepository.saveAll(endpointPatterns);
  }

  public boolean isPatternMatchUri(String pattern, String url) {
    PathPatternParser pathPatternParser = new PathPatternParser();
    PathPattern pathPattern = pathPatternParser.parse(pattern);
    PathContainer pathContainer = PathContainer.parsePath(url);
    return pathPattern.matches(pathContainer);
  }

  @PostConstruct
  public void initialize() {
    endpointPatternRepository.findAll().stream()
        .map(EndpointPattern::getPattern)
        .forEach(endpointPatterns::add);
  }
}
