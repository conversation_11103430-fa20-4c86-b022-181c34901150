package com.wexl.retail.auth.validator;

import static com.wexl.retail.auth.AuthUtil.isStudent;
import static com.wexl.retail.auth.AuthUtil.isTeacher;

import com.wexl.retail.model.User;
import com.wexl.retail.repository.UserRepository;

public class StudentValidator implements RoleValidator {
  private final UserRepository userRepository;

  public StudentValidator(UserRepository userRepository) {
    this.userRepository = userRepository;
  }

  public boolean isValidRole(User currentUser, String userId) {
    return currentUser != null && (isValidStudent(currentUser, userId) || isTeacher(currentUser));
  }

  private boolean isValidStudent(User currentUser, String userId) {
    return isStudent(currentUser) && currentUser.getAuthUserId().equals(userId);
  }
}
