package com.wexl.retail.metrics.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ChildOrgMlpsCountByMonth extends AbstractMetricHandler implements MetricHandler {

  @Override
  public String name() {
    return "childorgs-mlps-monthly-stats";
  }

  public List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    List<String> childOrgs =
        Optional.ofNullable(genericMetricRequest.getInput().get(ORG_KEY))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    if (!childOrgs.isEmpty()) {
      validateIfMapped(org, childOrgs.getFirst());
      return mlpService.getMlpsCountByMonth(childOrgs, genericMetricRequest.getTimePeriod());
    }
    return mlpService.getMlpsCountByMonth(getAllChildOrgs(), genericMetricRequest.getTimePeriod());
  }
}
