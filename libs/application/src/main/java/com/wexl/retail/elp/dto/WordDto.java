package com.wexl.retail.elp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record WordDto() {

  @Builder
  public record WordResponse(
      @JsonProperty("word") String word,
      @JsonProperty("phonetics") List<Phonetic> phonetics,
      @JsonProperty("meanings") List<Meaning> meanings) {}

  @Builder
  public record Phonetic(@JsonProperty("text") String text, @JsonProperty("audio") String audio) {}

  @Builder
  public record Meaning(
      @JsonProperty("partOfSpeech") String partOfSpeech,
      @JsonProperty("definitions") List<Definition> definitions,
      @JsonProperty("synonyms") List<String> synonyms,
      @JsonProperty("antonyms") List<String> antonyms) {}

  @Builder
  public record Definition(
      @JsonProperty("definition") String definition,
      @JsonProperty("synonyms") List<String> synonyms,
      @JsonProperty("antonyms") List<String> antonyms,
      @JsonProperty("example") String example) {}
}
