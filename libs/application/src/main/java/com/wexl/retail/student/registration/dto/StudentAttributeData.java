package com.wexl.retail.student.registration.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StudentAttributeData {
  @JsonProperty("principal_name")
  private String principalName;

  @JsonProperty("blood_group")
  private String bloodGroup;

  @JsonProperty("school_state")
  private String schoolState;

  @JsonProperty("school_pin")
  private String schoolPin;

  @JsonProperty("school_city")
  private String schoolCity;

  @JsonProperty("school_address")
  private String schoolAddress;

  @JsonProperty("date_of_birth")
  private String dateOfBirth;

  @JsonProperty("is_fee_paid")
  private Boolean isFeePaid = true;

  @JsonProperty("roadmap_id")
  private Long roadmapId;
}
