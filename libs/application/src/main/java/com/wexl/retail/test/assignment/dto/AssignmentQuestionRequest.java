package com.wexl.retail.test.assignment.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.wexl.retail.test.school.dto.TestQuestionRequest;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@JsonInclude(Include.NON_NULL)
public class AssignmentQuestionRequest extends TestQuestionRequest {

  private String question;

  @Override
  public String getType() {
    if (Objects.isNull(type)) {
      return "subjective";
    }
    return type;
  }
}
