package com.wexl.retail.student.exam.competitive.service;

import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.exam.competitive.dto.CompetitiveExamsDto;
import com.wexl.retail.student.exam.competitive.processor.CompetitiveExamValidatorProcessor;
import com.wexl.retail.test.schedule.domain.ScheduleTestMetadata;
import com.wexl.retail.test.schedule.dto.SimpleScheduleTestRequest;
import com.wexl.retail.test.schedule.dto.SimpleScheduleTestResponse;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.test.school.domain.TestCategory;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.test.school.repository.TestDefinitionExamInfo;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.util.ValidationUtils;
import jakarta.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CompetitiveExamService {

  private final TestDefinitionService testDefinitionService;
  private final StrapiService strapiService;
  private final ValidationUtils validationutils;
  private final CompetitiveExamValidatorProcessor competitiveExamValidatorProcessor;
  private final TestDefinitionRepository testDefinitionRepository;
  private final ScheduleTestService scheduleTestService;
  private final StudentRepository studentRepository;
  private final SectionRepository sectionRepository;
  private final SectionService sectionService;
  private final StorageService storageService;
  public static final String UUID_REGEX =
      "^[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$";

  @Value("${app.contentToken}")
  String contentBearerToken;

  public List<CompetitiveExamsDto.TeacherResponse> getCompetitiveExams(String orgSlug) {
    var testDefinition = testDefinitionService.getCompetitiveExams(orgSlug);
    return buildCompetitiveExamResponse(testDefinition);
  }

  private List<CompetitiveExamsDto.TeacherResponse> buildCompetitiveExamResponse(
      List<TestDefinitionExamInfo> competitiveExamInfo) {
    List<CompetitiveExamsDto.TeacherResponse> responseList = new ArrayList<>();
    var gradesList = strapiService.getAllGrades();
    List<TestDefinitionExamInfo> filteredResponseList =
        competitiveExamInfo.stream()
            .filter(c -> TestType.MOCK_TEST.name().equals(c.getType()))
            .toList();

    filteredResponseList.forEach(
        td -> {
          if (td.getCategory() != null) {
            var grade = validationutils.findGradeBySlug(gradesList, td.getGradeSlug());
            responseList.add(
                CompetitiveExamsDto.TeacherResponse.builder()
                    .testDefinitionId(td.getTestDefinitionId())
                    .testScheduleId(td.getTestScheduleId())
                    .sectionName(getSection(td.getSections()))
                    .gradeSlug(td.getGradeSlug())
                    .gradeName(grade.getName())
                    .title(td.getTestName())
                    .date(DateTimeUtil.convertIso8601ToEpoch(td.getDate()))
                    .testCategory(TestCategory.fromValue(td.getCategory()))
                    .build());
          }
        });
    return responseList;
  }

  private String getSection(String section) {
    if (section.matches(UUID_REGEX)) {
      var sectionData = sectionService.findByUuid(section);
      return sectionData.getName();
    }
    return section;
  }

  @Transactional
  public SimpleScheduleTestResponse saveCompetitiveExams(
      String orgSlug, CompetitiveExamsDto.Request request) {
    var testDefinitionEntity =
        competitiveExamValidatorProcessor.buildTestDefinitionData(request, orgSlug);
    var testDefinition = testDefinitionRepository.save(testDefinitionEntity);
    testDefinitionService.publishTestDefinitionById(
        testDefinition.getId(), true, contentBearerToken, true);
    return scheduleTestService.scheduleTest(buildScheduleTestRequest(testDefinition, request));
  }

  private SimpleScheduleTestRequest buildScheduleTestRequest(
      TestDefinition testDefinition, CompetitiveExamsDto.Request request) {
    return SimpleScheduleTestRequest.builder()
        .testDefinitionId(testDefinition.getId())
        .allStudents(Boolean.FALSE)
        .duration(300)
        .startDate(DateTimeUtil.convertIso8601ToEpoch(LocalDateTime.now()))
        .endDate(DateTimeUtil.convertIso8601ToEpoch(LocalDateTime.now().plusMinutes(30)))
        .orgSlug(testDefinition.getOrganization())
        .studentIds(buildStudentIds(request.sections()))
        .metadata(buildMetaData(request))
        .build();
  }

  private ScheduleTestMetadata buildMetaData(CompetitiveExamsDto.Request request) {
    List<UUID> uuids = request.sections().stream().map(UUID::fromString).toList();
    var sections = sectionRepository.findAllByUuidIn(uuids);
    return ScheduleTestMetadata.builder()
        .sections(sections.stream().map(Section::getName).toList())
        .build();
  }

  private Set<Long> buildStudentIds(List<String> sections) {
    List<Student> students = new ArrayList<>();
    sections.forEach(
        sect -> {
          var section = sectionRepository.findByUuid(UUID.fromString(sect));
          students.addAll(studentRepository.getStudentsBySectionAndDeletedAtIsNull(section.get()));
        });
    var users = students.stream().map(Student::getUserInfo).toList();
    return users.stream().map(User::getId).collect(Collectors.toSet());
  }

  public Map<String, String> upload(String orgSlug, CompetitiveExamsDto.UploadRequest request) {
    String documentName = UUID.randomUUID().toString();
    var path = getFilePath(orgSlug, documentName, request.extension());
    return Map.of(
        "reference",
        path,
        "url",
        storageService.generatePreSignedUrlForUpload(path),
        "preview-url",
        storageService.generatePreSignedUrlForFetch(path));
  }

  public String getFilePath(String orgSlug, String documentName, String extension) {
    return "%s/test-definiton-questions/%s.%s".formatted(orgSlug, documentName, extension);
  }
}
