package com.wexl.retail.staff.controller;

import com.wexl.retail.commons.caching.CacheConstants;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.model.EduBoard;
import com.wexl.retail.staff.dto.StaffDto;
import com.wexl.retail.staff.service.StaffService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/staff")
@RequiredArgsConstructor
@Slf4j
public class StaffController {

  private final StaffService staffService;
  private static final String HARD_DELETE_FAILED = "Failed to delete staff";

  @PostMapping
  public ResponseEntity<StaffDto.StaffResponse> createStaff(
      @RequestBody StaffDto.StaffRequest request, @PathVariable String orgSlug) {
    log.info("Creating new staff member: {}", request.firstName());
    return new ResponseEntity<>(staffService.createStaff(request, orgSlug), HttpStatus.CREATED);
  }

  @GetMapping("/{id}")
  public ResponseEntity<StaffDto.StaffResponse> getStaffById(
      @PathVariable Long id, @PathVariable String orgSlug) {
    log.info("Fetching staff with id: {}", id);
    return ResponseEntity.ok(staffService.getStaffById(id, orgSlug));
  }

  @GetMapping
  public ResponseEntity<List<StaffDto.StaffResponse>> getAllStaff(@PathVariable String orgSlug) {
    log.info("Fetching all staff members");
    return ResponseEntity.ok(staffService.getAllStaff(orgSlug));
  }

  @PutMapping("/{id}")
  public ResponseEntity<StaffDto.StaffResponse> updateStaff(
      @PathVariable Long id, @RequestBody StaffDto.StaffRequest request) {
    log.info("Updating staff with id: {}", id);
    return ResponseEntity.ok(staffService.updateStaff(id, request));
  }

  @DeleteMapping("/{id}")
  public ResponseEntity<Void> deleteStaff(@PathVariable Long id) {
    log.info("Deleting staff with id: {}", id);
    staffService.deleteStaff(id);
    return ResponseEntity.noContent().build();
  }

  @IsOrgAdmin
  @DeleteMapping("/{staffId}:inactive")
  public ResponseEntity<Void> deleteStaff(
      @PathVariable String orgSlug, @PathVariable String staffId) {
    try {
      staffService.deleteStaff(orgSlug, staffId);
      return ResponseEntity.ok().build();
    } catch (Exception exception) {
      log.error(HARD_DELETE_FAILED, exception);
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.StaffDelete.Failed");
    }
  }

  @IsOrgAdmin
  @PostMapping("/{staffId}:active")
  public ResponseEntity<Void> undeleteStudent(
      @PathVariable String orgSlug, @PathVariable String staffId) {
    try {
      staffService.undeleteStaff(orgSlug, staffId);
      return ResponseEntity.ok().build();
    } catch (Exception exception) {
      log.error("Failed to undelete student", exception);
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.StudentUndelete.Failed");
    }
  }

  @GetMapping("/{authUserId}/curriculum")
  public ResponseEntity<List<EduBoard>> getStaffSpecificCurriculum(
      @PathVariable String authUserId) {

    return ResponseEntity.ok()
        .cacheControl(CacheControl.maxAge(CacheConstants.MEDIUM))
        .body(staffService.transformBoardHierarchy(authUserId));
  }
}
