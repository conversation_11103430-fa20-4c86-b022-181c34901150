package com.wexl.retail.monitoring.entity;

import com.wexl.retail.model.Model;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Entity
@Data
@Table(name = "endpoints")
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class Endpoint extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String method;
  private String uri;
  private String pattern;
}
