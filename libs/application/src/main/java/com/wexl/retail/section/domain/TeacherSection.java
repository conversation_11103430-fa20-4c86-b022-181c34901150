package com.wexl.retail.section.domain;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.Teacher;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Where;

@Entity
@Table(name = "teacher_sections")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Where(clause = "deleted_at IS NULL")
public class TeacherSection extends Model {
  @Id
  @GeneratedValue(
      strategy = GenerationType.SEQUENCE,
      generator = "teacher-section-sequence-generator")
  @SequenceGenerator(
      name = "teacher-section-sequence-generator",
      sequenceName = "teacher_sections_seq",
      allocationSize = 1)
  private long id;

  @OneToOne
  @JoinColumn(name = "section_id")
  private Section section;

  @OneToOne
  @JoinColumn(name = "teacher_id")
  private Teacher teacher;
}
