<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wexl</groupId>
        <artifactId>libs</artifactId>
        <version>1.2-SNAPSHOT</version>
    </parent>

    <artifactId>google-dialogflow</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.cloud</groupId>
            <artifactId>google-cloud-dialogflow-cx</artifactId>
            <version>0.62.0</version>
        </dependency>
        <dependency>
            <groupId>com.wexl</groupId>
            <artifactId>application</artifactId>
            <version>1.2-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

</project>