error.serverError=ØªØ¹Ø°Ø± ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨                  
error.couldNotFindTaskInst=ØªØ¹Ø°Ø± Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ ÙØ¹ÙØ¯ Ø§ÙÙÙØ§Ù
#MLP
error.Strapi.Service=ØªØ¹Ø°Ø±Øª ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ÙØ§ ÙÙÙÙ Ø§ÙØ§ØªØµØ§Ù Ø¨Ø®Ø¯ÙØ© strapi
error.KnowledgeSubjectDetailsNotFound =ØªØ¹Ø°Ø± ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨ØªØ¹Ø°Ø± Ø§ÙØ­ØµÙÙ Ø¹ÙÙ ØªÙØ§ØµÙÙ ÙÙØ¶ÙØ¹ Ø§ÙÙØ¹Ø±ÙØ© ÙÙ {0}
error.NoEnrolledStudent =ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙÙ ÙØªÙ ØªØ³Ø¬ÙÙ Ø£Ù Ø·ÙØ§Ø¨ ÙÙ ÙØ°Ø§ Ø§ÙÙØ³Ù ÙÙ ÙØ°Ø§ Ø§ÙÙØ³Ù
error.QuestionUUIDsNotBeNullOrEmpty=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙØ§ ÙÙÙÙ Ø£Ù ØªÙÙÙ QuestionUIDs ÙØ§Ø±ØºØ© Ø£Ù ÙØ§Ø±ØºØ©
error.TeacherNotFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙÙØ¹ÙÙ.
error.SectionNotFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø§ÙÙØ³Ù {0} ØºÙØ± ÙÙØ¬ÙØ¯.
error.MLPNotFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.MLP ØºÙØ± ÙÙØ¬ÙØ¯.
error.StudentsNotAssignedMlp=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙÙ ÙØªÙ ØªØ¹ÙÙÙ MLP ÙÙØ·ÙØ§Ø¨ {0}
error.MlpFind.MLPId=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ mlps ÙÙØ¹Ø±Ù Mlp ÙØ°Ø§.
error.OrganizationNotFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø§ÙÙÙØ¸ÙØ© ØºÙØ± ÙÙØ¬ÙØ¯Ø©.
error.MLPResponseNotAuthorized=Ø§ÙÙØµÙÙ ØºÙØ± Ø§ÙÙØµØ±Ø­ Ø¨Ù Ø§ÙÙØµÙÙ Ø¥ÙÙ MLP ØºÙØ± ÙØµØ±Ø­ Ø¨Ù.
error.StudentFind.Organization=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø·Ø§ÙØ¨ ÙØ­ÙÙ Ø§Ø³Ù ÙØ³ØªØ®Ø¯Ù {0} ØºÙØ± ÙÙØ¬ÙØ¯ ÙÙ Ø§ÙÙØ¤Ø³Ø³Ø© {1}
error.UnableToUpdateItemStatus=ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙÙÙØ§Ø±Ø¯. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ ØªØ­Ø¯ÙØ« Ø­Ø§ÙØ© Ø§ÙØ¹ÙØµØ± {0} Ø¨Ø§ÙØ­Ø§ÙØ© {1}
error.MLPFind.ExamRef=InvalidInput.MLP ØºÙØ± ÙÙØ¬ÙØ¯ Ù ExamRef.
error.MLPMetaDataFind.MLPID= ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙÙÙØ§Ø±Ø¯. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ Ø¬ÙØ¨ Ø§ÙØ¨ÙØ§ÙØ§Øª Ø§ÙÙØµÙÙØ© ÙÙ mlp Ø¨Ø§ÙÙØ¹Ø±Ù {0}
error.MLPMetaDataFind.Slugs=ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙÙÙØ±Ø¯. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ Ø¬ÙØ¨ Ø§ÙØ¨ÙØ§ÙØ§Øª Ø§ÙÙØµÙÙØ© ÙÙØ°Ù Ø§ÙØ§Ø±ØªØ¨Ø§Ø·Ø§Øª Ø§ÙØ±Ø®ÙØ©: Chapter Slug {0} & Subject Slug {1}
error.FetchQuestionsFind.SubjectSlug=InvalidInput. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ Ø¬ÙØ¨ Ø£Ø³Ø¦ÙØ© ÙÙÙÙØ¶ÙØ¹: {0}
error.OrganizationChild.Parent=ÙØµÙÙ ØºÙØ± ÙØµØ±Ø­ Ø¨Ù. ÙØ°Ù Ø§ÙÙØ¤Ø³Ø³Ø© {0} ÙÙØ³Øª ØªØ§Ø¨Ø¹Ø© ÙÙ {1}
error.TeacherUuid.ChildOrg=ÙØµÙÙ ØºÙØ± ÙØµØ±Ø­ Ø¨Ù. ÙØ°Ø§ Ø§ÙÙØ¹ÙÙ {0} ÙÙØ³ ÙÙØ¬ÙØ¯ÙØ§ ÙÙ ÙØ¤Ø³Ø³Ø© {1} ÙØ°Ù
error.MLP.Questions=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ ØªØ¹ÙÙÙ Ø£Ø³Ø¦ÙØ© ØªØ¯Ø±ÙØ¨ ÙÙØ°Ù Mlp {0}
error.ExpiredOrInvalidToken=ÙØµÙÙ ØºÙØ± ÙØµØ±Ø­ Ø¨Ù Ø±ÙØ² ØºÙØ± ØµØ§ÙØ­ Ø£Ù ÙÙØªÙÙ Ø§ÙØµÙØ§Ø­ÙØ©
error.User.Configure=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙÙ ÙØªÙ ØªÙÙÙÙ Ø§ÙÙØ³ØªØ®Ø¯Ù Ø¨Ø´ÙÙ ØµØ­ÙØ­ ÙÙØ¤Ø³Ø³Ø© ØºÙØ± ÙØ¹Ø±ÙÙØ©.
error.UserLogin.UserName=ÙØµÙÙ ØºÙØ± ÙØµØ±Ø­ Ø¨Ù. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ ØªØ³Ø¬ÙÙ Ø¯Ø®ÙÙ Ø§ÙÙØ³ØªØ®Ø¯Ù Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù {0}
error.User.Disable=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ØªÙ ØªØ¹Ø·ÙÙ Ø§ÙÙØ³ØªØ®Ø¯Ù Ø§ÙØ®Ø§Øµ Ø¨Ù. Ø§ÙØ±Ø¬Ø§Ø¡ Ø§ÙØ§ØªØµØ§Ù Ø¨Ø§ÙÙØ³Ø¤ÙÙ 
error.AuthUserID.Exist=Ø§ÙÙØµÙÙ ØºÙØ± Ø§ÙÙØµØ±Ø­ Ø¨Ù. Ø§ÙÙØ³ØªØ®Ø¯Ù ÙØ¹ authUser {0} ØºÙØ± ÙÙØ¬ÙØ¯ ÙÙ Ø§ÙÙØ¸Ø§Ù.
error.User.Token=Ø¯Ø®ÙÙ ØºÙØ± ÙØµØ±Ø­ Ø¨Ù. ÙÙØªÙÙ Ø§ÙÙØ³ØªØ®Ø¯Ù Ø¥ÙÙ {0} ÙØ§ÙØ±ÙØ² Ø§ÙÙÙÙØ² ÙÙ {1}.
error.UserName.Status=Ø§ÙÙØµÙÙ ØºÙØ± Ø§ÙÙØµØ±Ø­ Ø¨Ù: Ø§ÙÙØ³ØªØ®Ø¯Ù {0} ÙÙØ³ ÙÙ Ø­Ø§ÙØ© Ø§ÙØªØ­ÙÙ.
error.InvalidUserEmail=ÙØµÙÙ ØºÙØ± ÙØµØ±Ø­ Ø¨Ù. ÙÙ ÙØªÙ Ø§ÙØªØ­ÙÙ ÙÙ Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ ÙÙÙØ³ØªØ®Ø¯Ù {0}.
error.ParsePhoneNumber=ØªØ¹Ø°Ø±Øª ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ ØªØ­ÙÙÙ Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØ­Ø¯Ø¯ {0}.
error.PhoneNumber=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.PhoneNumber {0} ØºÙØ± ØµØ§ÙØ­.
error.MobileNumber.Login=ÙØµÙÙ ØºÙØ± ÙØµØ±Ø­ Ø¨Ù. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØ­ÙÙÙ {0}.
error.OTP=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.otp ØºÙØ± ØµØ§ÙØ­.
error.MobileNumber.Verify=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙÙ ÙØªÙ Ø§ÙØªØ­ÙÙ ÙÙ Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØ­ÙÙÙ. Ø§ÙØ±Ø¬Ø§Ø¡ Ø¥Ø¯Ø®Ø§Ù Otp ÙØ±Ø© Ø£Ø®Ø±Ù!
error.WrongCredentials=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø£ÙØ±Ø§Ù Ø§ÙØ§Ø¹ØªÙØ§Ø¯ Ø®Ø§Ø·Ø¦Ø© Ø£Ù ÙØ§Ø±ØºØ©.
error.ParseToken=Ø§ÙÙØµÙÙ ØºÙØ± Ø§ÙÙØµØ±Ø­ Ø¨Ù. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ ØªØ­ÙÙÙ Ø§ÙØ±ÙØ² Ø§ÙÙØ·ÙÙØ¨.
error.MobileNumber.Users=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªÙ ØªØ¬Ø§ÙØ² Ø¹Ø¯Ø¯ Ø§ÙÙØ³ØªØ®Ø¯ÙÙÙ Ø¨Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØ­ÙÙÙ ÙØ°Ø§ Ø ÙØ±Ø¬Ù ØªØ¬Ø±Ø¨Ø© Ø±ÙÙ ÙØ®ØªÙÙ.
error.StudentCombination.Device=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªÙ ØªØ³Ø¬ÙÙ Ø­Ø³Ø§Ø¨Ù Ø¹ÙÙ Ø¬ÙØ§Ø² ÙØ®ØªÙÙ Ø Ø§ØªØµÙ Ø¨ÙØ³Ø¤ÙÙ Ø§ÙÙØ¹ÙØ¯.
error.ProcessingAuth=ÙØµÙÙ ØºÙØ± ÙØµØ±Ø­ Ø¨Ù Ø®Ø·Ø£ ØºÙØ± ÙØªÙÙØ¹ Ø£Ø«ÙØ§Ø¡ ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØªÙÙÙØ¶.
error.User.Null=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø§ÙÙØ³ØªØ®Ø¯Ù ÙÙ NUll.
error.AccessDenied=Ø§ÙÙØµÙÙ ØºÙØ± Ø§ÙÙØµØ±Ø­ Ø¨Ù. ØªÙ Ø±ÙØ¶ Ø§ÙÙØµÙÙ
error.authUserID.Find=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø§ÙÙØ³ØªØ®Ø¯Ù Ø¨Ø§ÙÙØ¹Ø±Ù {0} ØºÙØ± ÙÙØ¬ÙØ¯.
error.SectionFind.Organization=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø§ÙÙØ³Ù Ø§ÙØ°Ù ÙØ­ÙÙ Ø§ÙØ§Ø³Ù {0} ØºÙØ± ÙÙØ¬ÙØ¯ ÙÙ Ø§ÙÙØ¤Ø³Ø³Ø© {1}.
error.UserFind.Organization=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø§ÙÙØ³ØªØ®Ø¯Ù Ø§ÙØ°Ù ÙØ­ÙÙ Ø§ÙØ§Ø³Ù {0} ØºÙØ± ÙÙØ¬ÙØ¯ ÙÙ Ø§ÙÙØ¤Ø³Ø³Ø© {1}.
error.TeacherEmailFind.Organization=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø§ÙÙØ¹ÙÙ ÙØ¹ {0} ØºÙØ± ÙÙØ¬ÙØ¯ ÙÙ Ø§ÙÙØ¤Ø³Ø³Ø© {1}.
error.EduboardFind.SectionAndGrade=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Eduboard ÙÙØ·Ø§ÙØ¨ Ø§ÙØ°Ù ÙÙØªÙÙ Ø¥ÙÙ Ø§ÙÙØ³Ù {0} ÙØ§ÙØ¯Ø±Ø¬Ø© {1}.
error.SectionScheduleFind.MeetingID=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø¬Ø¯ÙÙ Ø§ÙÙØ³Ù Ø¨Ø§ÙÙØ¹Ø±Ù {0} ØºÙØ± ÙÙØ¬ÙØ¯.
error.AttendanceRecordFind.AttendnaceId=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø³Ø¬Ù Ø­Ø¶ÙØ± ÙÙØ¹Ø±Ù Ø§ÙØ­Ø¶ÙØ±: {0}.
error.appContext.Null=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø­Ø§ÙÙ ÙØ±Ø© Ø§Ø®Ø±Ù.
error.CannotGetDetailsOfOrganization=Ø§ÙÙØµÙÙ ØºÙØ± Ø§ÙÙØµØ±Ø­ Ø¨Ù. ÙØ§ ÙÙÙÙ Ø§Ø³ØªØ±Ø¯Ø§Ø¯ ØªÙØ§ØµÙÙ Ø§ÙÙØ¤Ø³Ø³Ø© {0}.
-->error.OrgAdmin.Org=Unauthorized Access.Org Admin not available for organization {0}.
error.Classroom.Exists=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Classroom ÙÙØ¬ÙØ¯ Ø¨Ø§ÙÙØ¹Ù Ø¨Ø§ÙØ§Ø³Ù {0}.
error.TeacherUnauthorized.Org=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ ÙØ¯Ø±Ø³ / ÙØ¯Ø±Ø³ÙÙ ØºÙØ± ÙØ¹ØªÙØ¯ÙÙ ÙÙÙØ¤Ø³Ø³Ø© {0}.
error.StudentUnauthorized.Org=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙØ·ÙØ§Ø¨ / Ø§ÙØ·ÙØ§Ø¨ ØºÙØ± Ø§ÙÙØµØ±Ø­ ÙÙÙ ÙÙØ¤Ø³Ø³Ø© {0}.
error.ClassroomValidity.Org=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙØµÙ Ø¯Ø±Ø§Ø³Ù ØºÙØ± ØµØ§ÙØ­ ÙÙÙØ¤Ø³Ø³Ø© {0}.
error.ClassRoomValidity.ClassID=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø­Ø¬Ø±Ø© Ø§ÙØ¯Ø±Ø§Ø³Ø© ØºÙØ± ØµØ§ÙØ­Ø©.
error.ClassRoomValidity.ClassIdAndOrg=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Classroom ØºÙØ± ØµØ§ÙØ­ ÙÙ ClassID {0} ÙØ§ÙÙØ¤Ø³Ø³Ø© {1}
error.ConflictsWithBusyTeacher=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙÙØ§Ù ÙØ´ÙÙØ© ÙØ¹ Ø§ÙÙØ¹ÙÙÙÙ {0}.
error.MeetingRoomFind.Org=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªØ¹Ø°Ø± Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ ØºØ±ÙØ© Ø§Ø¬ØªÙØ§Ø¹Ø§Øª ÙÙÙØ¤Ø³Ø³Ø© {0}.
error.ClassroomScheduleValidity.Org=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªØ¹Ø°Ø± Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø¬Ø¯ÙÙ Ø­Ø¬Ø±Ø© Ø§ÙØ¯Ø±Ø§Ø³Ø© ÙÙÙØ¤Ø³Ø³Ø© {0}.
error.StudentGet.Schedules=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªØ¹Ø°Ø± Ø§ÙØ­ØµÙÙ Ø¹ÙÙ Ø¬Ø¯Ø§ÙÙ Ø§ÙØ·ÙØ§Ø¨.
error.ClassRoomUpdate.Schedule=ØªØ¹Ø°Ø±Øª ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ ØªØ­Ø¯ÙØ« Ø¬Ø¯ÙÙ Ø§ÙÙØµÙ Ø§ÙØ¯Ø±Ø§Ø³Ù

error.CannotGetChapter=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙØ§ ÙÙÙÙ Ø§ÙØ­ØµÙÙ Ø¹ÙÙ Ø§ÙÙØµÙ {0} Ø ÙÙ strapi.
error.NoOfQuestionsNotFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø¹Ø¯Ø¯ Ø§ÙØ£Ø³Ø¦ÙØ© ÙØ¹ ÙØ°Ø§ classId.
error.ClassFind.Input=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø§ÙÙØ¦Ø© ØºÙØ± ÙÙØ¬ÙØ¯Ø© ÙØ¥Ø¯Ø®Ø§Ù ÙØ¹ÙÙ. 
error.QuestionCountGet.CLassID=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙØ§ ÙÙÙÙ Ø§ÙØ­ØµÙÙ Ø¹ÙÙ Ø¹Ø¯Ø¯ Ø§ÙØ£Ø³Ø¦ÙØ© ÙÙØ¹Ø±Ù Ø§ÙÙØµÙ Ø§ÙØ¯Ø±Ø§Ø³Ù: {0}.
error.SubTopicNotFound= ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø§ÙÙÙØ¶ÙØ¹ Ø§ÙÙØ±Ø¹Ù {0} ØºÙØ± ÙÙØ¬ÙØ¯ / Ø®Ø·Ø£ ØºÙØ± ÙØªÙÙØ¹.
error.CannotGetSQLQuery=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªØ¹Ø°Ø± Ø§Ø³ØªØ±Ø¯Ø§Ø¯ Ø§Ø³ØªØ¹ÙØ§Ù sql ÙÙ Ø³ØªØ±Ø§Ø¨Ù.
error.InvalidSlug=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø³Ø¨ÙÙØ© ØºÙØ± ØµØ§ÙØ­Ø© {0}.
error.ApplicationConfigFind.Referer=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ ØªÙÙÙÙ Ø§ÙØªØ·Ø¨ÙÙ ÙÙÙØ±Ø¬Ø¹ {0} Ø Ø­Ø¯Ø« Ø®Ø·Ø£ ØºÙØ± ÙØªÙÙØ¹.
error.GradeGet.Id=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªØ¹Ø°Ø± Ø¬ÙØ¨ Ø§ÙØ¯Ø±Ø¬Ø© Ø¨ÙØ§Ø³Ø·Ø© Ø§ÙÙØ¹Ø±Ù {0}.
error.EduBoardFind.EduBoardId=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.EduBoard ØºÙØ± ÙÙØ¬ÙØ¯ / Ø­Ø¯Ø« Ø®Ø·Ø£ ØºÙØ± ÙØªÙÙØ¹
error.InvalidGradeSlug=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø§ÙØ¯Ø±Ø¬Ø© {0} ØºÙØ± ØµØ§ÙØ­Ø©.
error.InvalidBoard=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙÙØ­Ø© ØºÙØ± ØµØ§ÙØ­Ø©.
error.EduBoardFind.Board=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.EduBoard ØºÙØ± ÙÙØ¬ÙØ¯ ÙÙÙØ­Ø© {0} / Ø­Ø¯Ø« Ø®Ø·Ø£ ØºÙØ± ÙØªÙÙØ¹.
error.InvalidChapter=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙØµÙ ØºÙØ± ØµØ§ÙØ­.
error.InvalidAcademicYear=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø¹Ø§Ù Ø£ÙØ§Ø¯ÙÙÙ ØºÙØ± ØµØ­ÙØ­.
error.SubjectGet.Slug=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªØ¹Ø°Ø± Ø¬ÙØ¨ Ø§ÙÙÙØ¶ÙØ¹ Ø¨ÙØ§Ø³Ø·Ø© slug {0}.
error.SubjectGet.Id=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªØ¹Ø°Ø± Ø¬ÙØ¨ Ø§ÙÙÙØ¶ÙØ¹ Ø¨Ø§ÙÙØ¹Ø±Ù {0}.
error.CannotCallStrapiService=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙØ§ ÙÙÙÙ Ø§ÙØ§ØªØµØ§Ù Ø¨Ø®Ø¯ÙØ© Ø³ØªØ±Ø¨Ù.
error.InvalidAssertSlug=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªÙ ØªÙØ¯ÙÙ ØªØ£ÙÙØ¯ ØºÙØ± ØµØ§ÙØ­ {0}.
error.InvalidSubTopic=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø§ÙÙÙØ¶ÙØ¹ Ø§ÙÙØ±Ø¹Ù ØºÙØ± ØµØ§ÙØ­.
error.InvalidReferer=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙØ±Ø¬Ø¹ ØºÙØ± ØµØ§ÙØ­.
error.CannotFindConfiguration=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªØ¹Ø°Ø± Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙØªÙÙÙÙ ÙÙ {0}
error.OrganizationFind.Slug=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø§ÙÙØ¤Ø³Ø³Ø© {0} ØºÙØ± ÙÙØ¬ÙØ¯Ø©.
error.InstituteName=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙÙØ¬Ø¯ ÙØ¹ÙØ¯ Ø¨ÙÙØ³ Ø§ÙØ§Ø³Ù. Ø§ÙØ±Ø¬Ø§Ø¡ ÙØ­Ø§ÙÙØ© Ø§Ø³Ù Ø¢Ø®Ø±.
error.MessagePayload= ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ ÙØ¹Ø§ÙØ¬Ø© Ø­ÙÙÙØ© Ø§ÙØ±Ø³Ø§ÙØ©.
error.directory.file=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙØ¬Ø¨ Ø£Ù ÙÙÙÙ Ø§ÙÙÙÙ Ø¯ÙÙÙØ§Ù.
error.FileNotFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ ÙÙÙ ÙÙÙØ³Ø§Ø± Ø§ÙÙØ­Ø¯Ø¯ / ØºÙØ± ØµØ§ÙØ­.
error.InvalidCategory=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªÙ ØªÙØ±ÙØ± ÙØ¦Ø© ØºÙØ± ØµØ§ÙØ­Ø©.
error.organization.grade=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø§ÙÙØ¤Ø³Ø³Ø© {0} ØºÙØ± ÙÙÙØ£Ø© ÙÙØ¯Ø±Ø¬Ø© {1}. 
error.SubjectsFound.duplication=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ ØªÙØ±Ø§Ø± ÙÙ Ø§ÙÙÙØ¶ÙØ¹Ø§Øª Ø§ÙÙØ­Ø¯Ø¯Ø© Ø ÙØ±Ø¬Ù Ø¥Ø²Ø§ÙØªÙ ÙØ§ÙØ­ÙØ¸ ÙØ±Ø© Ø£Ø®Ø±Ù.
error.SubjectProfileFind.Id=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙØ§ ÙÙÙÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙÙÙÙ Ø§ÙØ´Ø®ØµÙ ÙÙÙÙØ¶ÙØ¹ Ø¨Ø§ÙÙØ¹Ø±Ù {0}.
error.SubjectAdd.Profile=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªÙØª Ø¥Ø¶Ø§ÙØ© Ø§ÙÙÙØ¶ÙØ¹ Ø¨Ø§ÙÙØ¹Ù Ø Ø­Ø¯Ø¯ ÙÙØ¶ÙØ¹ÙØ§ ÙØ®ØªÙÙÙØ§.
error.student.profile=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙØ§ ÙÙÙÙ Ø­Ø°Ù ÙÙÙ Ø§ÙØªØ¹Ø±ÙÙ Ø ÙÙÙ ÙØ±ØªØ¨Ø· Ø¨Ø¨Ø¹Ø¶ Ø§ÙØ·ÙØ§Ø¨.
error.SubjectDelete.Profile=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø£Ø¶Ù ÙÙØ¶ÙØ¹ÙØ§ Ø¢Ø®Ø± ÙØ¨Ù Ø­Ø°Ù ÙØ°Ø§ Ø ÙØ§ ÙÙÙÙ Ø£Ù ÙÙÙÙ ÙÙÙ ØªØ¹Ø±ÙÙ Ø§ÙÙÙØ¶ÙØ¹ ÙØ§Ø±ØºÙØ§.
error.SubjectFind.Profile=Ø®Ø·Ø£ ÙÙ Ø§ÙÙÙØ§Ø±Ø¯. ØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ SubjectProfileNotFoundByIdot. ØªØ¹Ø°Ø± Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙÙÙØ¶ÙØ¹ ÙÙ Ø§ÙÙÙÙ Ø§ÙØ´Ø®ØµÙ {0}.
error.StudentFind.StudentName=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø§ÙØ·Ø§ÙØ¨ Ø§ÙØ°Ù ÙØ­ÙÙ Ø§Ø³Ù Ø§ÙÙØ³ØªØ®Ø¯Ù {0} ØºÙØ± ÙÙØ¬ÙØ¯.
error.StudentDelete.Profile=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø§ÙØ±Ø¬Ø§Ø¡ Ø¥Ø¶Ø§ÙØ© ÙÙÙ ØªØ¹Ø±ÙÙ Ø¢Ø®Ø± ÙÙØ·Ø§ÙØ¨ ÙØ¨Ù Ø­Ø°Ù ÙØ°Ø§.
error.ProfileFind.Student=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªØ¹Ø°Ø± Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ ÙØ°Ø§ Ø§ÙÙÙÙ Ø§ÙØ´Ø®ØµÙ ÙÙØ°Ø§ Ø§ÙØ·Ø§ÙØ¨
error.Delete.Profile=ØªØ¹Ø°Ø±Øª ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ÙØ´Ù Ø­Ø°Ù ÙÙÙ Ø§ÙØªØ¹Ø±ÙÙ.
error.BoardOrSubject.Null=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙØ§ ÙÙÙÙ Ø£Ù ÙÙÙÙ BoardSlug Ø£Ù SubjectSlug ÙØ§Ø±ØºÙØ§.
error.TeacherSubject.Mapped=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªÙ ØªØ¹ÙÙÙ Ø§ÙÙØ¹ÙÙ Ø¨Ø§ÙÙÙØ¶ÙØ¹ {0} Ø¨Ø§ÙÙØ¹Ù.
error.Metadata.Fetch=ØªØ¹Ø°Ø±Øª ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ØªØ¹Ø°Ø± Ø¬ÙØ¨ Ø§ÙØ¨ÙØ§ÙØ§Øª Ø§ÙÙØµÙÙØ©.
error.BoardSlug.Exists=ØªØ¹Ø°Ø±Øª ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. Ø§ÙÙÙØ­Ø© {0} ØºÙØ± ÙÙØ¬ÙØ¯Ø©.
error.CSV.generate=ØªØ¹Ø°Ø±Øª ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ØªØ¹Ø°Ø± Ø¥ÙØ´Ø§Ø¡ ÙÙÙ CSV. 
error.InvalidAttendnaceId=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙØ¹Ø±Ù Ø§ÙØ­Ø¶ÙØ± ØºÙØ± ØµØ§ÙØ­.
error.UnAuthorized=Ø¯Ø®ÙÙ ØºÙØ± ÙØ±Ø®Øµ. Ø§ÙØ·ÙØ¨ ØºÙØ± ÙØµØ±Ø­ Ø¨Ù.
error.StudentsNone.Attendance=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙØ§ ÙÙØ¬Ø¯ Ø·ÙØ§Ø¨ ÙØªÙÙÙØ² Ø§ÙØ­Ø¶ÙØ±
error.Student.Unauthorized=ÙØ³ØªØ®Ø¯Ù ØºÙØ± ÙØµØ±Ø­ ÙÙ {0}.
error.InvalidSection=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙØ³Ù ØºÙØ± ØµØ§ÙØ­ {0}.
error.CannotFindSection=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªØ¹Ø°Ø± Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙØ£ÙØ³Ø§Ù.
error.GradeNotFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙØ¯Ø±Ø¬Ø©.
error.fee.grade=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªÙØª Ø¬Ø¯ÙÙØ© ÙØ°Ù Ø§ÙØ±Ø³ÙÙ Ø¨Ø§ÙÙØ¹Ù ÙÙ Ø§ÙØ¯Ø±Ø¬Ø© {0}
error.StudentNone.Grade=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø·ÙØ§Ø¨ ÙÙ ÙØ°Ø§ Ø§ÙØµÙ {0}
error.invoices.grade=ØªØ¹Ø°Ø±Øª ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ÙØ´Ù Ø¥ÙØ´Ø§Ø¡ ÙÙØ§ØªÙØ± ÙÙØ°Ø§ Ø§ÙØªÙØ¯ÙØ±
error.InvalidFeeSchedle=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø¬Ø¯ÙÙ Ø§ÙØ±Ø³ÙÙ ØºÙØ± ØµØ§ÙØ­ {0}.
error.feeSchedule.view=Ø¯Ø®ÙÙ ØºÙØ± ÙØ±Ø®Øµ. ØºÙØ± ÙØµØ±Ø­ ÙÙ Ø¨Ø¹Ø±Ø¶ ØªÙØ§ØµÙÙ Ø¬Ø¯ÙÙ Ø§ÙØ±Ø³ÙÙ ÙØ°Ø§ {0}.
error.MeetingLink.exists=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø§Ø±ØªØ¨Ø§Ø· Ø§ÙØ§Ø¬ØªÙØ§Ø¹ ÙÙØ¬ÙØ¯ Ø¨Ø§ÙÙØ¹Ù {0}.
error.MeetingName.exists=Ø§Ø³Ù ØºØ±ÙØ© Ø§ÙØ§Ø¬ØªÙØ§Ø¹Ø§Øª ÙÙØ¬ÙØ¯ Ø¨Ø§ÙÙØ¹Ù {0}.
error.MeetingRoom.Update=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ§ ØªÙØ¬Ø¯ ØºØ±ÙØ© Ø§Ø¬ØªÙØ§Ø¹Ø§Øª {0} ÙØªØ§Ø­Ø© ÙÙØªØ­Ø¯ÙØ«.
error.MeetingRoom.Delete=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ§ ØªÙØ¬Ø¯ ØºØ±ÙØ© Ø§Ø¬ØªÙØ§Ø¹Ø§Øª {0} ÙØªØ§Ø­Ø© ÙÙØ­Ø°Ù.
error.Meeting.CreateRequest=ØªØ¹Ø°Ø± ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ØªØ¹Ø°Ø± Ø¥ÙØ´Ø§Ø¡ Ø·ÙØ¨ Ø§ÙØ§Ø¬ØªÙØ§Ø¹.
error.MetricProcessing=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ØªØ¹Ø°Ø±Øª ÙØ¹Ø§ÙØ¬Ø© Ø§ÙÙÙÙØ§Ø³ Ø¨Ø§ÙØ§Ø³Ù {0}.
error.MetricProcessing.Name=ØªØ¹Ø°Ø± ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ ÙØ¹Ø§ÙØ¬Ø© Ø§ÙÙÙÙØ§Ø³.
error.services.organization=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ Ø§ÙØ­ØµÙÙ Ø¹ÙÙ Ø§ÙØ®Ø¯ÙØ§Øª Ø§ÙÙØ³ØªØ®Ø¯ÙØ© ÙÙØ°Ù Ø§ÙÙØ¤Ø³Ø³Ø©.
error.TimeBomb.Request=Ø·ÙØ¨ ØºÙØ± ØµØ§ÙØ­. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ Ø¥ÙØ´Ø§Ø¡ Ø·ÙØ¨ ÙÙØ¨ÙØ© Ø²ÙÙÙØ©
error.StudentUsername.Exist=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø§ÙØ·Ø§ÙØ¨ Ø§ÙØ°Ù ÙØ­ÙÙ Ø§Ø³Ù Ø§ÙÙØ³ØªØ®Ø¯Ù {0} ØºÙØ± ÙÙØ¬ÙØ¯.
error.ConfigurationValidity.Roles=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ØªÙÙÙÙ ØºÙØ± ØµØ§ÙØ­ ÙÙØ¯ÙØ±.
error.ParentEmail=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ÙØ°Ø§ Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ ÙÙÙØ§ÙØ¯ ÙØ£Ø®ÙØ° Ø¨Ø§ÙÙØ¹Ù. Ø§ÙØ±Ø¬Ø§Ø¡ Ø§Ø®ØªÙØ§Ø± ÙØ§Ø­Ø¯ Ø¢Ø®Ø± !.
error.EmailNotFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ¹Ø±Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ ØºÙØ± ØµØ§ÙØ­.
error.SubjectProfiles.Configuration=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ØªÙ ØªÙÙÙÙ ÙÙÙØ§Øª ØªØ¹Ø±ÙÙ Ø§ÙÙÙØ¶ÙØ¹ Ø¨Ø´ÙÙ ØºÙØ± ØµØ­ÙØ­ Ø Ø§ØªØµÙ Ø¨Ø§ÙÙØ³Ø¤ÙÙ.
error.StudentFind.AuthUserID=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ³ØªØ®Ø¯Ù Ø§ÙØ·Ø§ÙØ¨ ÙØ¹ authUserId {0} ØºÙØ± ÙÙØ¬ÙØ¯.
error.StudentNotFound=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­ Ø§ÙØ·Ø§ÙØ¨ ØºÙØ± ÙÙØ¬ÙØ¯.
error.UserNotFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙÙØ³ØªØ®Ø¯Ù.
error.ParentNotFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø§ÙÙØ§ÙØ¯ ØºÙØ± ÙÙØ¬ÙØ¯.
error.InvalidPassword=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø±ÙØ² ÙØ±ÙØ± Ø®Ø§Ø·Ø¦.
error.StudentCreation.Failed=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ´Ù Ø¥ÙØ´Ø§Ø¡ Ø§ÙØ·Ø§ÙØ¨.
error.StudentsGet.Failed=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ´Ù Ø§ÙØ­ØµÙÙ Ø¹ÙÙ Ø§ÙØ·ÙØ§Ø¨.
error.StudentsGetInfo.Failed=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ´Ù Ø§ÙØ­ØµÙÙ Ø¹ÙÙ ÙØ¹ÙÙÙØ§Øª Ø§ÙØ·Ø§ÙØ¨.
error.StudentsUpdateInfo.Failed=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ´Ù ØªØ­Ø¯ÙØ« ÙØ¹ÙÙÙØ§Øª Ø§ÙØ·Ø§ÙØ¨.
error.StudentDelete.Failed=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ´Ù Ø­Ø°Ù Ø§ÙØ·Ø§ÙØ¨.
error.StudentUndelete.Failed=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ´Ù ÙÙ Ø¥ÙØºØ§Ø¡ Ø­Ø°Ù Ø§ÙØ·Ø§ÙØ¨.
error.UsernameExists=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø§Ø³Ù Ø§ÙÙØ³ØªØ®Ø¯Ù ÙÙØ¬ÙØ¯ Ø¨Ø§ÙÙØ¹Ù.
error.InvalidInput=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ØªÙ ØªÙÙÙØ± Ø¥Ø¯Ø®Ø§Ù ÙÙØ±Ø± Ø ØªØ­ÙÙ ÙÙ Ø§ÙØ¥Ø¯Ø®Ø§Ù.
error.section.grade= Input.section ØºÙØ± ØµØ§ÙØ­ {0} ØºÙØ± ÙÙØ¬ÙØ¯ ÙÙ Ø§ÙØ¯Ø±Ø¬Ø© {1}.
error.StudentRoleNotFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø¯ÙØ± Ø§ÙØ·Ø§ÙØ¨ ØºÙØ± ÙÙØ¬ÙØ¯.
error.ParentRoleNotFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø¯ÙØ± Ø§ÙÙØ§ÙØ¯ÙÙ ØºÙØ± ÙÙØ¬ÙØ¯.
error.Email.Registered=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ­ÙØ­. ÙØ¹Ø±Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ ÙØ³Ø¬Ù Ø¨Ø§ÙÙØ¹Ù.
error.Abbrevation.exists=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø§ÙØ§Ø®ØªØµØ§Ø± ÙÙØ¬ÙØ¯ Ø¨Ø§ÙÙØ¹Ù. Ø§ÙØ±Ø¬Ø§Ø¡ Ø§Ø®ØªÙØ§Ø± Ø¢Ø®Ø±.
error.InvalidEmailOTP=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ ØºÙØ± ØµØ­ÙØ­ OTP.
error.InvalidMobileOTP=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Mobile-OTP ØºÙØ± ØµØ§ÙØ­.
error.OrgAdminCreationFailed=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ´Ù Ø¥ÙØ´Ø§Ø¡ OrgAdmin.
error.InstituteName.Characters=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ¬Ø¨ Ø£ÙØ§ ÙÙÙ Ø§Ø³Ù Ø§ÙÙØ¹ÙØ¯ Ø¹Ù 3 Ø£Ø­Ø±Ù.
error.CaptchaCode.null=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙØ¯ Captcha ÙØ§Ø±Øº
error.ReCaptcha.Invalid=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ReCaptcha ØºÙØ± ØµØ§ÙØ­.
error.OrganizationEdit.Failed=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ´Ù ØªØ­Ø±ÙØ± Ø§ÙÙØ¤Ø³Ø³Ø© ÙÙ Ø§ÙÙØ­ØªÙÙ
error.Section.Exists=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø§ÙÙØ³Ù Ø§ÙØ°Ù ÙØ­ÙÙ Ø§ÙØ§Ø³Ù {0} ÙÙØ¬ÙØ¯ Ø¨Ø§ÙÙØ¹Ù.
error.StudentsRemove.Section=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙØ¬Ø¯ Ø¨Ø¹Ø¶ Ø§ÙØ·ÙØ§Ø¨ ÙÙ ÙØ°Ø§ Ø§ÙÙØ³Ù {0}. Ø§ÙØ±Ø¬Ø§Ø¡ Ø¥Ø²Ø§ÙØªÙÙ ÙØ¨Ù Ø­Ø°ÙÙÙ.
error.Sections.Retreival=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ§ ÙÙÙÙ Ø§Ø³ØªØ±Ø¯Ø§Ø¯ Ø§ÙØ·ÙØ§Ø¨.
error.ServicesGet.Organization=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ Ø§ÙØ­ØµÙÙ Ø¹ÙÙ Ø§ÙØ®Ø¯ÙØ§Øª Ø§ÙÙØ³ØªØ®Ø¯ÙØ© ÙÙØ°Ù Ø§ÙÙÙØ¸ÙØ©.
error.organizationUpdate.Failed=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ´Ù ØªØ­Ø¯ÙØ« Ø§ÙÙÙØ¸ÙØ©
error.User.Analytics=ØªØ¹Ø°Ø± ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ØªØ¹Ø°Ø± Ø§ÙØ­ØµÙÙ Ø¹ÙÙ ØªØ­ÙÙÙØ§Øª Ø§ÙÙØ³ØªØ®Ø¯Ù
error.CouldnotGetDetails=ØªØ¹Ø°Ø±Øª ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ØªØ¹Ø°Ø± Ø§ÙØ­ØµÙÙ Ø¹ÙÙ Ø§ÙØªÙØ§ØµÙÙ.
error.Organization.Services=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ Ø§ÙØ­ØµÙÙ Ø¹ÙÙ Ø§ÙØ®Ø¯ÙØ§Øª Ø§ÙÙØ³ØªØ®Ø¯ÙØ© ÙÙØ°Ù Ø§ÙÙÙØ¸ÙØ©.
error.ParentRole=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø¯ÙØ± Ø§ÙÙØ§ÙØ¯ÙÙ ØºÙØ± ÙÙØ¬ÙØ¯.
error.InvalidCode=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø±ÙØ² Ø§ÙØªØ­ÙÙ ØºÙØ± ØµØ§ÙØ­ Ø£Ù ÙÙØªÙÙ Ø§ÙØµÙØ§Ø­ÙØ©!
error.Child.Details=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ØªÙØ§ØµÙÙ Ø§ÙØ·ÙÙ ØºÙØ± ØµØ­ÙØ­Ø©.
error.ExamResult.Access=Ø§ÙÙØµÙÙ ØºÙØ± Ø§ÙÙØµØ±Ø­ Ø¨Ù. ØºÙØ± ÙØ®ÙÙ ÙÙÙØµÙÙ Ø¥ÙÙ ÙØªØ§Ø¦Ø¬ Ø§ÙØ§ÙØªØ­Ø§Ù
error.ExamId=ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙÙÙØ§Ø±Ø¯. ØªØ¹Ø°Ø± Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙØ§Ø®ØªØ¨Ø§Ø± {0}.
error.CannotFindUser=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ ÙØ³ØªØ®Ø¯Ù Ø¨Ø§Ø³Ù ÙØ³ØªØ®Ø¯Ù {0}.
error.Assesment.Started=Ø·ÙØ¨ ØºÙØ± ØµØ§ÙØ­. Ø§ÙØªÙÙÙÙ ÙÙ ÙØ¨Ø¯Ø£.
error.Assesment.Completed=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. Ø§ÙØªÙÙ Ø§ÙØªÙÙÙÙ.
error.Assesment.Grade=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ°Ø§ Ø§ÙØªÙÙÙÙ ÙÙØ³ ÙØ¯Ø±Ø¬ØªÙ.
error.Assesment.Taken=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. Ø§ÙØªÙÙÙÙ ÙØ£Ø®ÙØ° Ø¨Ø§ÙÙØ¹Ù.
error.TestDefinition=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ ØªØ¹ÙÙÙ TestDefinition Ø¨Ø§ÙØ£Ø³Ø¦ÙØ© {1}.
error.Exam.Access=Ø§ÙÙØµÙÙ ØºÙØ± Ø§ÙÙØµØ±Ø­ Ø¨Ù. ØºÙØ± ÙØµØ±Ø­ ÙÙ Ø¨Ø§ÙÙØµÙÙ Ø¥ÙÙ Ø§ÙØ§Ø®ØªØ¨Ø§Ø±.
error.PDFAnnotations=Inut ØºÙØ± ØµØ§ÙØ­. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ ÙØ¹Ø§ÙØ¬Ø© Ø´Ø±ÙØ­ pdf!
error.InsufficientPoints=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙØ§Ø· ØºÙØ± ÙØ§ÙÙØ©. Ø§ÙØ±Ø¬Ø§Ø¡ Ø´Ø±Ø§Ø¡ Ø§ÙÙØ²ÙØ¯ ÙÙ Ø§ÙÙÙØ§Ø·.
error.Payment.Details=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ØªÙØ§ØµÙÙ Ø§ÙØ¯ÙØ¹ ØºÙØ± ØµØ­ÙØ­Ø©.
error.InvalidRequest=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø·ÙØ¨ ØºÙØ± ØµØ§ÙØ­.
error.NoSubscriptionFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ ØªÙØ§ØµÙÙ Ø§ÙØ§Ø´ØªØ±Ø§Ù
error.Payment.Discount=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. Ø¥Ø¬ÙØ§ÙÙ Ø§ÙØ®ØµÙ Ø£ÙØ¨Ø± ÙÙ Ø§ÙØ¯ÙØ¹. ÙØ§ ÙÙÙÙ Ø§ÙÙØªØ§Ø¨Ø¹Ø©.
error.Payment.Zero=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ§ ÙÙÙÙ Ø£Ù ÙÙÙÙ Ø¥Ø¬ÙØ§ÙÙ ÙØ¨ÙØº Ø§ÙØ¯ÙØ¹ ØµÙØ±Ø§Ù.
error.Plan.Subscription=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ØªØ¹Ø°Ø± Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ ÙØ¹Ø±ÙÙ Ø§ÙØ®Ø·Ø© {0}.
error.RazorPay.Order=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ØªØ¹Ø°Ø± Ø¥ÙØ´Ø§Ø¡ Ø·ÙØ¨ RazorPay.
error.NoRecordFound= ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ§ ØªÙØ¬Ø¯ Ø³Ø¬ÙØ§Øª.
error.User.Email=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙÙØ³ØªØ®Ø¯Ù Ø¹Ù Ø·Ø±ÙÙ Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ {0}.
error.Student.SubscriptionDetails=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. Ø­Ø¯Ø« Ø®Ø·Ø£ Ø£Ø«ÙØ§Ø¡ Ø¥Ø¶Ø§ÙØ© ØªÙØ§ØµÙÙ Ø§ÙØ§Ø´ØªØ±Ø§Ù ÙÙØ·Ø§ÙØ¨ {0}.
error.InvalidSubscription=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø§Ø´ØªØ±Ø§Ù ØºÙØ± ØµØ§ÙØ­.
error.Payment.Info=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ¹ÙÙÙØ§Øª Ø§ÙØ¯ÙØ¹ ØºÙØ± ØµØ­ÙØ­Ø©. ØªÙÙÙØ¹ Ø§ÙØ¯ÙØ¹ ØºÙØ± ÙØ·Ø§Ø¨Ù.
error.SubscriptionDetails=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø§ÙØ±Ø¬Ø§Ø¡ Ø¥Ø¶Ø§ÙØ© ØªÙØ§ØµÙÙ Ø§ÙØ§Ø´ØªØ±Ø§Ù.
error.Student.Subscription=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø®Ø·Ø£ Ø£Ø«ÙØ§Ø¡ Ø¥Ø¶Ø§ÙØ© Ø§ÙØ§Ø´ØªØ±Ø§Ù ÙÙØ·Ø§ÙØ¨ {0}
error.GiftCardNotFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø¨Ø·Ø§ÙØ© Ø§ÙÙØ¯Ø§ÙØ§.
error.InvalidSubjectSlug = Invalid SubjectSlug {0}
error.StudentTeacher.Conflict=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙØ§Ù ØªØ¹Ø§Ø±Ø¶Ø§Øª ÙÙ {0} Ø ÙÙ Ø£ÙØª ÙØªØ£ÙØ¯ ÙÙ Ø£ÙÙ ØªØ±ÙØ¯ Ø§ÙÙØªØ§Ø¨Ø¹Ø©Ø
error.Meeting.MeetingID = ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ´ÙÙØ© ÙÙ Ø§Ø³ØªØ±Ø¯Ø§Ø¯ Ø§ÙØ§Ø¬ØªÙØ§Ø¹ Ø¨Ø§ÙÙØ¹Ø±Ù {0}.
error.ZoomConfig.Organization=ÙØ§ ÙÙÙÙ ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ÙØ´ÙÙØ© Ø§ÙØªÙÙÙÙ. ØªØ¹Ø°Ø± Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ ØªÙÙÙÙ ØªÙØ¨ÙØ± ØµØ§ÙØ­ ÙÙÙØ¤Ø³Ø³Ø© {0}.
error.User.Inactive=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø§ÙÙØ³ØªØ®Ø¯Ù ØºÙØ± ÙØ´Ø·. ÙØ§ ÙÙÙÙ Ø§ÙØ§ÙØ¶ÙØ§Ù Ø¥ÙÙ Ø§ÙØ§Ø¬ØªÙØ§Ø¹.
error.Organization.Config=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ´ÙÙØ© ØªÙÙØ¦Ø© Zoom SDK ÙÙÙØ¤Ø³Ø³Ø© {0}
error.RolesNotFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø¯ÙØ± Ø§ÙÙØ¹ÙÙ / ÙØ³Ø¤ÙÙ Ø§ÙÙØ¤Ø³Ø³Ø© / Ø§ÙØ·Ø§ÙØ¨ / ÙÙÙ Ø§ÙØ£ÙØ± / Ø§ÙÙØ¯ÙØ±
error.InvalidOrganization=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø¯ÙØ± / ÙÙØ¸ÙØ© ØºÙØ± ØµØ§ÙØ­Ø©.
error.RoleNotFound=ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙÙÙØ§Ø±Ø¯. Ø§ÙØ¯ÙØ± ØºÙØ± ÙÙØ¬ÙØ¯
error.Permission.added=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªÙØª Ø¥Ø¶Ø§ÙØ© Ø¨Ø¹Ø¶ Ø§ÙØ£Ø°ÙÙØ§Øª Ø¨Ø§ÙÙØ¹Ù Ø ÙÙØ±Ø¬Ù ÙÙØ­ Ø¥Ø°Ù Ø¬Ø¯ÙØ¯.
error.OrganizationNotFound1=ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙÙÙØ§Ø±Ø¯. Ø§ÙÙÙØ¸ÙØ© {0} ØºÙØ± ÙÙØ¬ÙØ¯Ø©.
error.TargetDirectory=Ø§ÙØ¥Ø¯Ø®Ø§Ù Ø®Ø§Ø±Ø¬ Ø§ÙØ¯ÙÙÙ Ø§ÙÙØ¯Ù.
error.InvalidFile=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙÙ ØºÙØ± ØµØ§ÙØ­.
error.UnZipFolder=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ Ø¥ÙØ´Ø§Ø¡ ÙØ¬ÙØ¯ Ø¨ÙÙ Ø§ÙØ¶ØºØ·.
error.HMAC.Generation=ÙØ´Ù Ø¥ÙØ´Ø§Ø¡ HMAC {0}.
error.File.Uploaded=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ ØªØ­ÙÙÙ Ø§ÙÙÙÙ.
error.File.Available=Ø§ÙÙÙÙ ØºÙØ± ÙØªÙÙØ± Ø¨Ø¹Ø¯.
error.Validation.Class=ÙØ¦Ø© Ø§ÙØªØ­ÙÙ ÙÙ Ø§ÙØµØ­Ø©.
error.urls.generate=ØªØ¹Ø°Ø± ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ Ø¥ÙØ´Ø§Ø¡ Ø¹ÙØ§ÙÙÙ url Ø§ÙÙÙØ§Ø³Ø¨Ø© ÙÙØ¯ÙØ±Ø©
error.InvalidUrl=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. URL ØºÙØ± ØµØ§ÙØ­
error.TestSchedule=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø¬Ø¯ÙÙ Ø§ÙØ§Ø®ØªØ¨Ø§Ø± ÙÙ {0}.
error.Students.ScheduleTest=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ§ ÙÙØ¬Ø¯ Ø·ÙØ§Ø¨ ÙØ¬Ø¯ÙÙØ© Ø§ÙØ§Ø®ØªØ¨Ø§Ø±
error.Test.Unauthorized=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ÙØ³Øª ÙØ®ÙÙØ§Ù ÙØ¬Ø¯ÙÙØ© ÙØ°Ø§ Ø§ÙØ§Ø®ØªØ¨Ø§Ø±.
error.TestDefinition1=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ ØªØ¹Ø±ÙÙ Ø§ÙØ§Ø®ØªØ¨Ø§Ø± ÙÙ {0}.
error.ExamNotFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø§ÙØ§ÙØªØ­Ø§Ù ØºÙØ± ÙÙØ¬ÙØ¯.
error.TestSchedule1=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø¬Ø¯ÙÙ Ø§ÙØ§Ø®ØªØ¨Ø§Ø± ØºÙØ± ØµØ§ÙØ­.
error.Test.Delete=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ§ ÙÙÙÙ Ø¥Ø¬Ø±Ø§Ø¡ Ø¹ÙÙÙØ© Ø§ÙØ­Ø°Ù. Ø§ÙØ§Ø®ØªØ¨Ø§Ø± ÙÙ Ø­Ø§ÙØ© {0}.
error.Test.Find=ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙÙÙØ§Ø±Ø¯. ØªØ¹Ø°Ø± Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§Ø®ØªØ¨Ø§Ø± Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù {0}
error.DeleteTest.Unauthorized=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ØºÙØ± ÙØµØ±Ø­ ÙÙ Ø¨Ø­Ø°Ù ÙØ°Ø§ Ø§ÙØ§Ø®ØªØ¨Ø§Ø±
error.Test.Definition=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙØ§Ù Ø¨Ø¹Ø¶ Ø§ÙØ§Ø®ØªØ¨Ø§Ø±Ø§Øª Ø§ÙÙØ¬Ø¯ÙÙØ© Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù ØªØ¹Ø±ÙÙ Ø§ÙØ§Ø®ØªØ¨Ø§Ø± ÙØ°Ø§ Ø ÙØ±Ø¬Ù Ø­Ø°ÙÙØ§ ÙØ¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
errror.UserNotFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø§ÙÙØ³ØªØ®Ø¯Ù ØºÙØ± ÙÙØ¬ÙØ¯ ÙÙ {0}.
error.TeamName.Exists=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. Ø§Ø³Ù Ø§ÙÙØ±ÙÙ ÙÙØ¬ÙØ¯ Ø¨Ø§ÙÙØ¹Ù
error.TeamNotFound=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. Ø§ÙÙØ±ÙÙ ØºÙØ± ÙÙØ¬ÙØ¯
error.Persons.Team=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ§ ÙÙÙÙ Ø£Ù ÙÙÙÙ Ø§ÙØ£Ø´Ø®Ø§Øµ ÙÙ Ø§ÙÙØ±ÙÙ ÙØ§Ø±ØºÙØ§.
error.NewPerson.Schedule=ØªØ¹Ø°Ø±Øª ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ÙØ´Ù ÙÙ Ø¬Ø¯ÙÙØ© Ø§ÙØ¯ÙØ±Ø§Øª ÙÙØ£Ø´Ø®Ø§Øµ Ø§ÙØ¬Ø¯Ø¯ ÙÙ ÙØ°Ø§ Ø§ÙÙØ±ÙÙ.
error.Student.Invalid=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø·Ø§ÙØ¨ ØºÙØ± ØµØ§ÙØ­.
error.Username.Exists=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø§Ø³Ù Ø§ÙÙØ³ØªØ®Ø¯Ù ÙÙØ¬ÙØ¯ Ø¨Ø§ÙÙØ¹Ù
error.UnableToProcess=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ ÙØ¹Ø§ÙØ¬Ø© Ø·ÙØ¨Ù.
error.TeacherCreate.Email=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ§ ÙÙÙÙ Ø¥ÙØ´Ø§Ø¡ ÙØ¯Ø±Ø³ Ø¨Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ {0}.
error.Code.Expired=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø±ÙØ² Ø§ÙØªØ­ÙÙ ØºÙØ± ØµØ§ÙØ­ Ø£Ù ÙÙØªÙÙ Ø§ÙØµÙØ§Ø­ÙØ©.
error.RoleNotFound1=Ø§ÙÙØ¯Ø®ÙØ§Øª ØºÙØ± ØµØ­ÙØ­Ø©. ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø¯ÙØ± Ø§ÙÙØ´Ø±Ù ÙÙÙØ¹ÙÙ / Ø§ÙÙØ¤Ø³Ø³Ø©.
error.OrgsDelete.Teacher=ØªØ¹Ø°Ø± ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ Ø­Ø°Ù Ø§ÙÙØ¤Ø³Ø³Ø§Øª ÙÙØ¹ÙÙ ÙØ¹ÙÙ.
error.ChildOrgNotFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙÙÙØ¸ÙØ© Ø§ÙØªØ§Ø¨Ø¹Ø©.
error.UserNotFound1=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. Ø§ÙÙØ³ØªØ®Ø¯Ù ØºÙØ± ÙÙØ¬ÙØ¯.
error.StudentEdit.Unauthorized=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ØºÙØ± ÙØµØ±Ø­ ÙÙ Ø¨ØªØ¹Ø¯ÙÙ Ø§ÙØ·Ø§ÙØ¨.
error.StudentGet.Failed=ØªØ¹Ø°Ø± ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ÙØ´Ù ÙÙ Ø§ÙØ­ØµÙÙ Ø¹ÙÙ Ø§ÙØ·ÙØ§Ø¨.
error.StudentCreate.Failed=ØªØ¹Ø°Ø±Øª ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ÙØ´Ù Ø¥ÙØ´Ø§Ø¡ Ø§ÙØ·Ø§ÙØ¨.
error.StudentUpdate.Failed=ØªØ¹Ø°Ø± ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ÙØ´Ù ØªØ­Ø¯ÙØ« ÙØ¹ÙÙÙØ§Øª Ø§ÙØ·Ø§ÙØ¨.
error.InvalidClassroom=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø­Ø¬Ø±Ø© Ø§ÙØ¯Ø±Ø§Ø³Ø© ØºÙØ± ØµØ§ÙØ­Ø©
error.Organization.Task=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ÙÙÙØ© ØºÙØ± ØµØ§ÙØ­Ø© ÙÙÙØ¤Ø³Ø³Ø© {0}.
error.TaskNotFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙÙÙÙØ©
error.InvalidTask=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙÙØ© ØºÙØ± ØµØ§ÙØ­Ø© ÙÙÙØ¤Ø³Ø³Ø© {0}.
error.QuestionsFind.Task=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø£Ø³Ø¦ÙØ© Ø¨Ø®ØµÙØµ Ø§Ø³Ù Ø§ÙÙÙÙØ© {0}
error.Ticket.Create=ØªØ¹Ø°Ø± ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ Ø¥ÙØ´Ø§Ø¡ Ø¨Ø·Ø§ÙØ© ÙÙ Ø§ÙÙØ¸Ø§Ù
error.AccessToken.generate=ØªØ¹Ø°Ø±Øª ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ Ø¥ÙØ´Ø§Ø¡ Ø±ÙØ² Ø§ÙÙØµÙÙ.
error.Ticket.Id=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ§ ØªÙØ¬Ø¯ ØªØ°ÙØ±Ø© ÙØªØ§Ø­Ø© Ø¨Ø§ÙÙØ¹Ø±Ù {0}.
error.RecordFind.Updation=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø³Ø¬Ù ÙÙØªØ­Ø¯ÙØ«.
error.DocId.Null=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ§ ÙÙÙÙ Ø£Ù ÙÙÙÙ docId ÙØ§Ø±ØºÙØ§.
error.FailedToCreatePerson.InvalidOrgConfig=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ´Ù Ø¥ÙØ´Ø§Ø¡ Ø´Ø®Øµ Ø¨Ø³Ø¨Ø¨ ØªÙÙÙÙ Ø§ÙÙØ¤Ø³Ø³Ø© ØºÙØ± ØµØ§ÙØ­.
error.SectionFind.Grade=ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙÙÙØ§Ø±Ø¯. ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø£ÙØ³Ø§Ù ÙÙØ°Ù Ø§ÙØ¯Ø±Ø¬Ø©.
error.PersonNotFound=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙØ´Ø®Øµ.
error.PersonIsDeleted=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ØªÙ Ø­Ø°Ù ÙØ°Ø§ Ø§ÙØ´Ø®Øµ.
error.PersonDoesntExist=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø§ÙØ´Ø®Øµ ØºÙØ± ÙÙØ¬ÙØ¯.
error.InvalidPerson=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. Ø´Ø®Øµ ØºÙØ± ØµØ§ÙØ­.
error.InvalidCR=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. CR ØºÙØ± ØµØ§ÙØ­.
error.InvalidPromoCard=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ÙÙØ¯ Ø§ÙØªØ±ÙÙØ¬ ØºÙØ± ØµØ§ÙØ­ Ø£Ù ÙÙØªÙÙ Ø§ÙØµÙØ§Ø­ÙØ© !!
error.PromocodeUser.NotActive=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ÙØ°Ø§ Ø§ÙØ±ÙØ² Ø§ÙØªØ±ÙÙØ¬Ù ØªÙ Ø§Ø³ØªØ±Ø¯Ø§Ø¯Ù Ø¨Ø§ÙÙØ¹Ù ÙÙ Ø®ÙØ§Ù Ø§ÙØ¹Ø¯Ø¯ Ø§ÙØ°Ù ØªÙ ØªÙÙÙÙÙ ÙÙ Ø§ÙÙØ³ØªØ®Ø¯Ù. ÙÙ ÙØ¹Ø¯ ÙØ´Ø·ÙØ§ Ø¨Ø¹Ø¯ Ø§ÙØ¢Ù.
error.PromocodeUser.NotActive1=Input.Promocode ØºÙØ± ØµØ§ÙØ­ {0} ÙØªØ§Ø­ Ø¨Ø§ÙÙØ¹Ù ÙÙ ÙØ¨Ù Ø§ÙÙØ³ØªØ®Ø¯Ù. ÙØ§ ÙÙÙÙ Ø§Ø³ØªØ®Ø¯Ø§ÙÙ ÙØ±Ø© Ø£Ø®Ø±Ù
error.PromocodeUser.NotActive2=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ØªÙ ØªÙÙÙØ± Promocode {0} Ø¨Ø§ÙÙØ¹Ù Ø¨ÙØ§Ø³Ø·Ø© Ø§ÙÙØ³ØªØ®Ø¯Ù ÙÙØ·Ø§ÙØ¨ {1}. ÙØ§ ÙÙÙÙ Ø§Ø³ØªØ®Ø¯Ø§ÙÙØ§ ÙØ±Ø© Ø£Ø®Ø±Ù
error.PromocodeNotAvailable=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ÙÙØ¯ Ø¨Ø±ÙÙÙÙÙØ¯ {0} ØºÙØ± ÙØªØ§Ø­. ÙÙØ§Ù {1} Ø¥Ø¯Ø®Ø§ÙØ§Øª ØªÙØª ØªÙÙØ¦ØªÙØ§ ÙÙ Ø§ÙØ®ÙÙÙØ© ÙÙ {2} ÙØ§ÙØ®Ø·Ø© {3}.
error.PromocodeValidity.Student=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. Ø±ÙØ² Ø§ÙØªØ±ÙÙØ¬ ÙØ°Ø§ {0} ØºÙØ± ØµØ§ÙØ­ ÙÙØ°Ø§ Ø§ÙØ³ÙÙØ§Ø±ÙÙ. ÙØ§ ÙÙØ¬Ø¯ Ø·ÙØ§Ø¨ ÙÙØ°Ø§ Ø§ÙÙØ§ÙØ¯.
error.PromocodeValidity.Subscriptions=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ÙÙÙØ§ÙØ¯ Ø§Ø´ØªØ±Ø§ÙØ§Øª ÙØ®ØªÙÙØ© ÙØ£Ø·ÙØ§Ù ÙØ®ØªÙÙÙÙ. Ø±ÙØ² Ø§ÙØªØ±ÙÙØ¬ ÙØ°Ø§ {0} ØºÙØ± ØµØ§ÙØ­ ÙÙØ°Ø§ Ø§ÙØ³ÙÙØ§Ø±ÙÙ.
error.InvalidQRCode=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. QrCode ØºÙØ± ØµØ§ÙØ­.
error.InvalidOTPId=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. OtpId ØºÙØ± ØµØ§ÙØ­. Ø§ÙØ±Ø¬Ø§Ø¡ Ø§ÙØªØ­ÙÙ!
error.MobileNumberNotVerified=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ Ø§ÙØªØ­ÙÙ ÙÙ Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØ­ÙÙÙ. Ø§Ø±Ø¬ÙÙ ØªØ­ÙÙ!
error.OrganizationFind.Slug1=ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙÙÙØ§Ø±Ø¯.
error.QRCodeGeneration=ØªØ¹Ø°Ø±Øª ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨ Ø®Ø·Ø£ ÙÙ Ø¥ÙØ´Ø§Ø¡ Ø±ÙØ² Ø§ÙØ§Ø³ØªØ¬Ø§Ø¨Ø© Ø§ÙØ³Ø±ÙØ¹Ø©.
error.StudentCreate.Failed1=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­ Ø ÙØ´Ù Ø¥ÙØ´Ø§Ø¡ Ø§ÙØ·Ø§ÙØ¨
error.InvalidReferralToken=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø±ÙØ² Ø¥Ø­Ø§ÙØ© ØºÙØ± ØµØ§ÙØ­ {0}.
error.ReferralNotFound=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø¥Ø­Ø§ÙØ©.
error.ReportsNotAvailable=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­ Ø§ÙØªÙØ§Ø±ÙØ± ØºÙØ± ÙØªÙÙØ±Ø©
error.UnknownRole=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. Ø¯ÙØ± ØºÙØ± ÙØ¹Ø±ÙÙ
error.ScheduleFind.Id=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø§ÙØ¬Ø¯ÙÙ ØºÙØ± ÙÙØ¬ÙØ¯ ÙÙÙØ¹Ø±Ù.
error.UnauthorizedAccess=Ø¯Ø®ÙÙ ØºÙØ± ÙØ±Ø®Øµ.
error.InvalidUser=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ³ØªØ®Ø¯Ù ØºÙØ± ØµØ§ÙØ­.
error.InvalidScromDefId=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. scormDefinitionID ØºÙØ± ØµØ§ÙØ­
error.InvalidDefId=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ÙØ¹Ø±Ù ØªØ¹Ø±ÙÙ ØºÙØ± ØµØ§ÙØ­.
error.UnableToConfigure=ØªØ¹Ø°Ø± ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ Ø§ÙØªÙÙÙÙ.
error.IncorrectResponseHeaders=ØªØ¹Ø°Ø±Øª ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ Ø¥Ø¶Ø§ÙØ© Ø±Ø¤ÙØ³ Ø§Ø³ØªØ¬Ø§Ø¨Ø© ØµØ­ÙØ­Ø©
error.SubjectDelete.Teacher=ØªØ¹Ø°Ø±Øª ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ Ø¥Ø¶Ø§ÙØ© Ø±Ø¤ÙØ³ Ø§Ø³ØªØ¬Ø§Ø¨Ø© ØµØ­ÙØ­Ø©
error.SectionAlredayExists=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. {0} ÙØ°Ø§ Ø§ÙÙØ³Ù ÙØ®Ø±Ø¬ Ø¨Ø§ÙÙØ¹Ù. ØªÙØ´ÙØ· ÙØ°Ø§ Ø§ÙÙØ³Ù ÙÙ ÙØ§Ø¦ÙØ© Ø§ÙÙØ³Ù.
error.InvalidGoalID=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. GoalId ØºÙØ± ØµØ§ÙØ­.
error.StudentFind.StudentId=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙØ·Ø§ÙØ¨ ÙÙ {0}.
error.StudentFind.User=ÙØµÙÙ ØºÙØ± ÙØµØ±Ø­ Ø¨Ù. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø·Ø§ÙØ¨ ÙÙÙØ³ØªØ®Ø¯Ù.
error.ParentRole.Config=ØªØ¹Ø°Ø±Øª ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ÙÙ ÙØªÙ ØªÙÙÙÙ Ø§ÙØ¯ÙØ± Ø§ÙØ±Ø¦ÙØ³Ù Ø¨Ø´ÙÙ ØµØ­ÙØ­.
error.ValueCannotBeZero=ÙØ§ ÙÙÙÙ Ø£Ù ØªÙÙÙ Ø§ÙÙÙÙØ© ØµÙØ±ÙØ§ !.
error.InvalidAnswerId=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ÙØ¹Ø±Ù Ø¥Ø¬Ø§Ø¨Ø© ØºÙØ± ØµØ§ÙØ­.
error.Username.Registered=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­ Ø§Ø³Ù Ø§ÙÙØ³ØªØ®Ø¯Ù ÙØ³Ø¬Ù Ø¨Ø§ÙÙØ¹Ù
error.InvalidVerificationCode=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø±ÙØ² Ø§ÙØªØ­ÙÙ ØºÙØ± ØµØ§ÙØ­ Ø£Ù ÙÙØªÙÙ Ø§ÙØµÙØ§Ø­ÙØ©!
error.ClassRoom.Delete=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙØ§ ÙÙÙÙ Ø­Ø°Ù ØºØ±ÙØ© Ø§ÙØ§Ø¬ØªÙØ§Ø¹Ø§Øª ÙØ°Ù Ø ÙÙØ¯ ÙØ§ÙØª Ø¨ØªØ¹ÙÙÙ ÙØµÙÙ Ø¯Ø±Ø§Ø³ÙØ© {0}
error.InvalidInput1=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªÙ ØªÙÙÙØ± ÙÙØ¹ Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­
error.ClassroomSchedule.Update=ØªØ¹Ø°Ø±Øª ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ ØªØ­Ø¯ÙØ« Ø¬Ø¯ÙÙ Ø§ÙÙØµÙ Ø§ÙØ¯Ø±Ø§Ø³Ù
error.Teacher.Exist=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø§ÙÙØ¹ÙÙ ØºÙØ± ÙÙØ¬ÙØ¯ !!
error.InvalidStudentDetails=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªÙØ§ØµÙÙ Ø§ÙØ·Ø§ÙØ¨ ØºÙØ± ØµØ­ÙØ­Ø© Ø§ÙÙØ±Ø³ÙØ© ÙÙ Ø§ÙØ·ÙØ¨.
error.UnknownStudentDetails=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ØªÙ Ø¥Ø±Ø³Ø§Ù ØªÙØ§ØµÙÙ Ø·Ø§ÙØ¨ ØºÙØ± ÙØ¹Ø±ÙÙØ© ÙÙ Ø§ÙØ·ÙØ¨
error.Calendar=ØªØ¹Ø°Ø± ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ÙÙ ÙØªÙ ÙÙØ¡ Ø§ÙØªÙÙÙÙ Ø¨Ø´ÙÙ ØµØ­ÙØ­. ÙØ±Ø¬Ù Ø§ÙØ§ØªØµØ§Ù Ø¨Ø§ÙÙØ³Ø¤ÙÙ
error.CouldntProcessRequest=ÙØ§ ÙÙÙÙ ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨.
error.RazorPay=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙÙØ§Ù Ø®Ø·Ø£ ÙØ§.
error.Test.Taken=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ØªÙ Ø¥Ø¬Ø±Ø§Ø¡ Ø§ÙØ§Ø®ØªØ¨Ø§Ø± Ø¨Ø§ÙÙØ¹Ù.
error.ScheduledTest.Unauthorized=ÙØµÙÙ ØºÙØ± ÙØµØ±Ø­ Ø¨Ù. ØºÙØ± ÙØµØ±Ø­ ÙÙ Ø¨Ø§ÙÙØµÙÙ Ø¥ÙÙ Ø§ÙØ§Ø®ØªØ¨Ø§Ø± Ø§ÙÙØ¬Ø¯ÙÙ!
error.Exam.Completed=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø§ÙØªÙÙ Ø§ÙØ§Ø®ØªØ¨Ø§Ø± Ø¨Ø§ÙÙØ¹Ù!
error.Exam.Config=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙØ¹Ø±ÙÙ Ø§Ø®ØªØ¨Ø§Ø± ØºÙØ± ÙØ§Ø¦Ù Ø ÙÙ ÙØªÙ ØªÙÙÙÙ Ø§Ø®ØªØ¨Ø§Ø± ScheduleTest Ø£Ù TestDefinition ÙÙ Ø§ÙØ§Ø®ØªØ¨Ø§Ø±
error.StudentSubmit.Questions=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙØ­ØªØ§Ø¬ Ø§ÙØ·Ø§ÙØ¨ Ø¥ÙÙ Ø¥Ø±Ø³Ø§Ù Ø¬ÙÙØ¹ Ø§ÙØ£Ø³Ø¦ÙØ©.
error.Questions.Display=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙØ§ ÙØªÙ Ø¹Ø±Ø¶ Ø§ÙØ£Ø³Ø¦ÙØ© | ÙÙ ÙØªÙ Ø§ÙØ±Ø¯ Ø¹ÙÙ Ø£Ù Ø³Ø¤Ø§Ù
error.StudentAssigned.Test=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø¬Ø¯ÙÙ Ø§ÙØ§Ø®ØªØ¨Ø§Ø±: ÙÙ ÙØªÙ ØªØ¹ÙÙÙÙª s ÙÙØ·ÙØ§Ø¨
error.TestEventDetails.Empty=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø¨ÙØ§ÙØ§Øª Ø§Ø³ØªØ¬Ø§Ø¨Ø© ØªÙØ§ØµÙÙ Ø­Ø¯Ø« Ø§ÙØ§Ø®ØªØ¨Ø§Ø± ÙØ§Ø±ØºØ©.
error.Test.Allowed=Ø§ÙÙØµÙÙ ØºÙØ± Ø§ÙÙØµØ±Ø­ Ø¨Ù. ÙØ§ ÙÙØ³ÙØ­ ÙÙ Ø¨Ø¥Ø¬Ø±Ø§Ø¡ ÙØ°Ø§ Ø§ÙØ§Ø®ØªØ¨Ø§Ø±
error.WoohooGet.Status=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙØ§ ÙÙÙÙ Ø§ÙØ§ØªØµØ§Ù Ø¨Ù woohoo ÙÙØ­ØµÙÙ Ø¹ÙÙ Ø­Ø§ÙØ© Ø§ÙØ·ÙØ¨ {0}
error.GiftCardOrder.Request=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.Ø­Ø¯Ø« Ø®Ø·Ø£ Ø£Ø«ÙØ§Ø¡ ÙØ¹Ø§ÙØ¬Ø© Ø·ÙØ¨ Ø¨Ø·Ø§ÙØ© Ø§ÙÙØ¯Ø§ÙØ§.
error.InsufficientPoint=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­.ÙÙØ·Ø© ØºÙØ± ÙØ§ÙÙØ©!
error.ProcessingRequest=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ØºÙØ± ÙØ§Ø¯Ø± Ø¹ÙÙ ÙØ¹Ø§ÙØ¬Ø© Ø·ÙØ¨Ù!
error.InvalidRewardTransaction=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. ÙØ¹Ø±Ù ÙØ¹Ø§ÙÙØ© Ø§ÙÙÙØ§ÙØ£Ø© ØºÙØ± ØµØ§ÙØ­.
error.InvalidPaymentDetails=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ­ÙØ­ ØªÙØ§ØµÙÙ Ø§ÙØ¯ÙØ¹ ØºÙØ± ØµØ­ÙØ­Ø©.
error.Wallet.Exist=ÙØ¯Ø®Ù ØºÙØ± ØµØ§ÙØ­. Ø§ÙÙØ­ÙØ¸Ø© ØºÙØ± ÙÙØ¬ÙØ¯Ø©.
error.Strapi.SubTopics=Ø¥Ø¯Ø®Ø§Ù ØºÙØ± ØµØ§ÙØ­. ÙØ§ ÙÙÙÙ Ø§Ø³ØªØ¯Ø¹Ø§Ø¡ Strai Ø§ÙØ­ØµÙÙ Ø¹ÙÙ ÙÙØ§Ø¶ÙØ¹ ÙØ±Ø¹ÙØ©.
error.Question.Media=ØªØ¹Ø°Ø±Øª ÙØ¹Ø§ÙØ¬Ø© Ø§ÙØ·ÙØ¨. ÙØ´Ù ØªØ­ÙÙÙ Ø§ÙÙØ³Ø§Ø¦Ø· ÙÙØ£Ø³Ø¦ÙØ©
