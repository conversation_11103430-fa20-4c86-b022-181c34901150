apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "retail-service.fullname" . }}-test-connection"
  labels:
    {{- include "retail-service.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test-success
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "retail-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
