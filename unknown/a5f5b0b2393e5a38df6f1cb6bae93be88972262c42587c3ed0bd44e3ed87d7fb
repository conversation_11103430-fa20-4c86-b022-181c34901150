package com.wexl.chatbot;

import com.wexl.chatbot.dto.WhatsAppBotDto;
import com.wexl.chatbot.service.Msg91WebhookService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/public/api/whatsapp")
@RequiredArgsConstructor
@Slf4j
public class Msg91WebhookController {

  private final Msg91WebhookService msg91WebhookService;

  @PostMapping("/msg91-webhook")
  @ResponseStatus(org.springframework.http.HttpStatus.ACCEPTED)
  public void onMsg91Event(@RequestBody WhatsAppBotDto.Msg91WebhookEvent evt) {
    msg91WebhookService.handleWebhook(evt);
  }

  @PostMapping("/create-test")
  @ResponseStatus(HttpStatus.CREATED)
  public WhatsAppBotDto.CreateTestResponse createTest(
      @RequestBody WhatsAppBotDto.CreateTestRequest request) {
    return msg91WebhookService.createTest(request);
  }
}
