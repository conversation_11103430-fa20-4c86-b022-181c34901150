<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="14.7mm"/>
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="0cm"  border="4pt solid black" padding="4mm">
                <fo:block margin-top="1" font-weight="bold" color="#2730fe" text-align="right" padding-top="-11mm" padding-after="6mm">Student Id:
                    <fo:inline th:text="${model.header.studentId}"/></fo:block>
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="300%" content-height="300%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="watermarkFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="1"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="1"/>
                                            <feFuncB type="linear" slope="1"/>
                                            <feFuncA type="linear" slope="0.8"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                                <image filter="url(#watermarkFilter)" x="0" y="-3" opacity="0.3" width="100%" height="100%" xlink:href="https://images.wexledu.com/live-worksheets/wexl-internal/20240901043131721.png"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none">
                    <fo:table-column column-width="20mm" />
                    <fo:table-column column-width="140mm" />
                    <fo:table-body color="#83101c">
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block >
                                    <fo:external-graphic src='url("https://images.wexledu.com/live-worksheets/wexl-internal/20240901043131721.png")'
                                                         content-width="60px"  content-height="60px"  />
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="1pt"
                                          th:text="${model.header.schoolName}">

                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold"  font-family="Times New Roman, serif" text-align="center" space-after="4pt"
                                          th:text="${model.header.address}">
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="10pt"
                                          th:text="${model.header.isoData}">
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt">Record
                                    of
                                    Academic Performance
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="10pt"
                                          th:text="${model.header.academicYear}">
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" space-after="10pt">
                    <fo:table border="none" color="#2730fe">
                        <fo:table-column column-width="52mm" />
                        <fo:table-column column-width="72mm" />
                        <fo:table-column column-width="37mm" />
                        <fo:table-column column-width="58mm" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Student Id </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" >:  <fo:inline th:text="${model.header.studentId}"></fo:inline></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Roll No</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" >:  <fo:inline th:text="${model.body.rollNumber}"></fo:inline></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Name of the Student</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" >:  <fo:inline th:text="${model.body.name}"></fo:inline></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Class &amp; Section</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" >:  <fo:inline th:text="${model.body.className}"></fo:inline></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Mother's/Father's/Guardian's Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" >:  <fo:inline th:text="${model.body.fathersName}"></fo:inline></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Date of Birth</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" >:  <fo:inline th:text="${model.body.dateOfBirth}"></fo:inline></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <!-- Report Card Table -->
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" space-after="12pt">
                    <fo:block th:text="${model.body.firstTable.title}"  color="#83101c" font-size="9" font-weight="bold" font-family="Times New Roman, serif"></fo:block>
                    <fo:table border="1pt solid red">
                        <fo:table-column column-width="40mm"/>
                        <fo:table-column column-width="23mm" />
                        <fo:table-column column-width="23mm"  />
                        <fo:table-column column-width="23mm"  />
                        <fo:table-column column-width="23mm"  />
                        <fo:table-column column-width="24mm"  />
                        <fo:table-column column-width="24mm"  />
                        <fo:table-header font-size="9pt" >
                            <fo:table-row >
                                <fo:table-cell border="none" font-weight="bold" padding="2mm" padding-top="5mm" text-align="left"  number-rows-spanned="2">
                                    <fo:block>SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid red" font-weight="bold" padding="1mm"  number-columns-spanned="6" >
                                    <fo:block >TERM-I</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row font-size="7pt">
                                <fo:table-cell border="1pt solid red" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column.pt1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid red" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column.nb1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid red" font-weight="bold" padding="1mm"  >
                                    <fo:block th:text="${model.body.firstTable.column.se1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid red" font-weight="bold" padding="1mm"  >
                                    <fo:block th:text="${model.body.firstTable.column.hye}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid red" font-weight="bold" padding="1mm"  >
                                    <fo:block >TOTAL (100)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid red" font-weight="bold" padding="1mm"  >
                                    <fo:block >Grade</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body font-size="7pt"  text-align="left">
                            <fo:table-row th:each="marks : ${model.body.firstTable.firstTableMarks}" >
                                <fo:table-cell border="1pt solid red" padding="1mm" font-weight="bold">
                                    <fo:block th:text="${marks.subject}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid red" padding="1mm">
                                    <fo:block th:text="${marks.pt1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid red" padding="1mm">
                                    <fo:block th:text="${marks.nb1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid red" padding="1mm">
                                    <fo:block th:text="${marks.se1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid red" padding="1mm">
                                    <fo:block th:text="${marks.hye}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid red" padding="1mm">
                                    <fo:block th:text="${marks.term1total}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid red" padding="1mm">
                                    <fo:block th:text="${marks.term1Grade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell padding="1mm" text-align="center" number-columns-spanned="5">
                                    <fo:block >Percentage / Grade</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid red" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.percentageGrade.term1total}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid red" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.percentageGrade.term1Grade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="9pt" space-before="10" text-align="center" font-family="Times New Roman, serif" >
                    <fo:table border="1pt solid red" text-align="center"  >
                        <fo:table-column column-width="86mm" />
                        <fo:table-column column-width="47mm" />
                        <fo:table-column column-width="47mm" />
                        <fo:table-header>
                            <fo:table-row >
                                <fo:table-cell border="1pt solid red" font-weight="bold" padding="1mm" text-align="left" number-rows-spanned="2">
                                    <fo:block>SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid red" font-weight="bold" padding="1mm" number-columns-spanned="2">
                                    <fo:block>TERM-I</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row font-size="7pt">
                                <fo:table-cell border="1pt solid red" font-weight="bold" padding="1mm">
                                    <fo:block>MARKS(100)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid red" font-weight="bold" padding="1mm"  >
                                    <fo:block>GRADE</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body font-size="7pt">
                            <fo:table-row th:each="marks : ${model.body.secondTable.secondTableMarks}">
                                <fo:table-cell border="1pt solid red" padding="1mm" text-align="left" font-weight="bold">
                                    <fo:block th:text="${marks.subjectName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid red" padding="1mm">
                                    <fo:block th:text="${marks.term1Marks}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid red" padding="1mm">
                                    <fo:block th:text="${marks.term1Grade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block th:text="${model.body.thirdTable.title}"  space-before="10"  font-size="9" font-weight="bold" text-align="center" font-family="Times New Roman, serif"></fo:block>
                <fo:block border-width="1mm" font-size="9pt" margin-left="1.6cm" font-family="Times New Roman, serif">
                    <fo:table border="1pt solid black">
                        <fo:table-column column-width="80mm" />
                        <fo:table-column column-width="50mm" />
                        <fo:table-header>
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" margin-left="0cm" padding="1mm" text-align="left">
                                    <fo:block>SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" margin-left="0cm" text-align="center">
                                    <fo:block>TERM-I</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body  font-size="7pt">
                            <fo:table-row th:each="marks : ${model.body.thirdTable.thirdTableMarks}">
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" margin-left="0cm">
                                    <fo:block th:text="${marks.subjectName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" margin-left="0cm" text-align="center">
                                    <fo:block th:text="${marks.term1Grade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="10pt" space-after="5pt" space-before="3pt" font-family="Times New Roman, serif">
                    <fo:block font-weight="bold" space-before="8pt" font-family="Times New Roman, serif">Attendance:</fo:block>
                    <fo:table border="none">
                        <fo:table-column column-width="58mm" />
                        <fo:table-column column-width="61mm" />
                        <fo:table-column column-width="61mm" />
                        <fo:table-header>
                            <fo:table-row font-size="8pt">
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>Total No. of Working days</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>No. of days Present</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block>Percentage of Attendance(%)</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body font-family="Times New Roman, serif"  font-size="7pt">
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.attendance.workingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.attendance.daysPresent}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.attendance.attendancePer}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="10pt" space-after="27pt">
                    <fo:block font-weight="bold" font-family="Times New Roman, serif" >Remarks:
                    <fo:inline font-weight="normal" th:text="${model.body.attendance.remarks}"></fo:inline>
                    </fo:block>
                </fo:block>
                <fo:block border-width="1mm" font-size="10pt" space-after="2pt"  font-family="Times New Roman, serif">
                    <fo:table border="none">
                        <fo:table-column column-width="75mm" />
                        <fo:table-column column-width="63mm" />
                        <fo:table-column column-width="35mm" />
                        <fo:table-body font-family="Times New Roman, serif" font-size="10pt">
                            <fo:table-row >
                                <fo:table-cell text-align="left" font-weight="bold">
                                    <fo:block>Class Teacher Signature   :</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold">
                                    <fo:block></fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="right" font-weight="bold">
                                    <fo:block>Principal</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block font-weight="bold" border-width="1mm" font-size="10pt" space-after="6pt" font-family="Times New Roman, serif">Name of the Class Teacher :
                    <fo:inline font-weight="normal" th:text="${model.body.teacherName}"></fo:inline>
                </fo:block>
                <fo:block font-weight="bold" space-before="20pt" font-family="Times New Roman, serif">Grading Scale:</fo:block>
                <fo:block border-width="1mm" font-size="10pt" font-family="Times New Roman, serif">
                    <fo:table border="none">
                        <fo:table-column column-width="22mm" />
                        <fo:table-column column-width="22mm" />
                        <fo:table-column column-width="22mm" />
                        <fo:table-column column-width="22mm" />
                        <fo:table-column column-width="22mm" />
                        <fo:table-column column-width="22mm" />
                        <fo:table-column column-width="24mm" />
                        <fo:table-column column-width="24mm" />
                        <fo:table-body font-family="Times New Roman, serif"  font-size="7pt" font-weight="bold">
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">91-100</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">81-90.99</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">71-80.99</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">61-70.99</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">51-60.99</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">41-50.99</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">33-40.99</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">0-32.99</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">A1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">A2</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">B1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">B2</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">C1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">C2</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">D</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">E</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="10pt" space-before="2mm" >
                    <fo:block font-size="8pt" font-family="Times New Roman, serif">
                        <fo:inline font-weight="bold">PT :</fo:inline> PERIODIC TEST , <fo:inline font-weight="bold">NB </fo:inline>: NOTEBOOK, <fo:inline font-weight="bold">SE </fo:inline> : SUBJECT ENRICHMENT , <fo:inline font-weight="bold">YE </fo:inline> : YEARLY
                    </fo:block>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>