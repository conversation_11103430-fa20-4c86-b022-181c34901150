<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="12mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="98%" margin-top="-0.5cm" border="2pt solid black" padding="6mm">
                <fo:table border="none" space-after="4pt">
                    <fo:table-column column-width="5mm" />
                    <fo:table-column column-width="28mm"/>
                    <fo:table-column column-width="130mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block >
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block >
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block margin-top="2mm" font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="3pt"
                                          th:text="${model.header.schoolName}">
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block >
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block margin-top="-9mm">
                                    <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS.png")'
                                                         content-width="70px"  content-height="70px"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block margin-top="2mm" font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="3pt">
                                    ACADEMIC SESSION:
                                    <fo:inline th:text="${model.header.academicSection}">
                                    </fo:inline>
                                </fo:block>
                                <fo:block  font-size="15pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-before="10pt" >
                                    PROGRESS CARD
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block border-width="1mm" font-size="12pt" space-before="30pt" space-after="6pt" font-family="Times New Roman, serif">
                    <fo:table border="none">
                        <fo:table-column column-width="53mm" />
                        <fo:table-column column-width="80mm" />
                        <fo:table-column column-width="28mm" />
                        <fo:table-column column-width="20mm" />
                        <fo:table-body >
                            <fo:table-row >
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block >
                                        Student's Name  &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:
                                    </fo:block>
                                </fo:table-cell >
                                <fo:table-cell text-align="left" padding="0mm">
                                    <fo:block th:text="${#strings.toUpperCase(model.body.name)}">
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block >
                                        Roll No &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left"  padding="0mm">
                                    <fo:block th:text="${model.body.rollNumber}">
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row >
                                <fo:table-cell >
                                    <fo:block padding-top="3mm" text-align="left" font-weight="bold" padding="0mm" >
                                        Mother's Name &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" padding="0mm">
                                    <fo:block padding-top="3mm" th:text="${model.body.motherName}">
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block padding-top="3mm" text-align="left" font-weight="bold" padding="0mm" >
                                        Date of Birth &#160;:
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" padding="0mm">
                                    <fo:block padding-top="3mm" th:text="${model.body.dateOfBirth}">
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell >
                                    <fo:block padding-top="3mm" text-align="left" font-weight="bold" padding="0mm" >
                                        Father's/ Guardian's Name :
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" padding="0mm">
                                    <fo:block padding-top="3mm" th:text="${model.body.fatherName}">
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block padding-top="3mm" text-align="left" font-weight="bold" padding="0mm" >
                                        Class/Section &#160;:
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" padding="0mm">
                                    <fo:block padding-top="3mm" th:text="${model.body.classAndSection}">
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block border-width="1mm" font-size="8pt" space-before="25pt" font-family="Times New Roman, serif">
                    <fo:table >
                        <fo:table-column column-width="8mm" border="1pt solid black"/>
                        <fo:table-column column-width="37mm" border="1pt solid black"/>
                        <fo:table-column column-width="9mm" border="1pt solid black"/>
                        <fo:table-column column-width="9mm" border="1pt solid black"/>
                        <fo:table-column column-width="11mm" border="1pt solid black"/>
                        <fo:table-column column-width="12mm" border="1pt solid black"/>
                        <fo:table-column column-width="10mm" border="1pt solid black"/>
                        <fo:table-column column-width="9mm" border="1pt solid black"/>
                        <fo:table-column column-width="10mm" border="1pt solid black"/>
                        <fo:table-column column-width="11mm" border="1pt solid black"/>
                        <fo:table-column column-width="11mm" border="1pt solid black"/>
                        <fo:table-column column-width="11mm" border="1pt solid black"/>
                        <fo:table-column column-width="12mm" border="1pt solid black"/>
                        <fo:table-column column-width="14mm" border="1pt solid black"/>
                        <fo:table-column column-width="12mm" border="1pt solid black"/>

                        <fo:table-header >
                            <fo:table-row font-weight="bold">
                                <fo:table-cell padding-top="2mm" number-rows-spanned="2">
                                    <fo:block text-align="center"> SNO</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="2mm" number-rows-spanned="2">
                                    <fo:block text-align="center"> SUBJECT </fo:block>
                                </fo:table-cell>
                                <fo:table-cell number-columns-spanned="5">
                                    <fo:block text-align="center"> TERM-1(100M) </fo:block>
                                </fo:table-cell>
                                <fo:table-cell number-columns-spanned="7">
                                    <fo:block text-align="center"> TERM-2(100M) </fo:block>
                                </fo:table-cell>
                                <fo:table-cell number-columns-spanned="1">
                                    <fo:block text-align="center"> OVERALL TOTAL </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row font-weight="bold" border="1pt solid black">
                                <fo:table-cell>
                                    <fo:block text-align="center">CYCLIC TEST-1(10M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center">CYCLIC TEST-2(10M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center">BEST OF CTS EXAM(20M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center">TERM 1 EXAM (80)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center">TOTAL (100) (CTS + TERM-1)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center">CYCLIC TEST-3(10M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center">CYCLIC TEST-4(10M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center">BEST OF CTS EXAM(20M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center">PB-1 (80M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center">PB-2 (80M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center">BEST OF PBS (80M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center">TOTAL (100M)(AVG OF CTS + BEST OF PBS)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center"> OVERALL TOTAL (TERM-1 &amp; TERM-2) </fo:block>
                                </fo:table-cell>
                            </fo:table-row >
                        </fo:table-header>
                        <fo:table-body th:each="first : ${model.body.firstTable}">
                            <fo:table-row text-align="center" border="1pt solid black">
                                <fo:table-cell >
                                    <fo:block th:text="${first.sno}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block th:text="${first.subjectName}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.ct1}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.ct2}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.BestCts1And2}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.term1}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.totalTerm1AndCts}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.ct3}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.ct4}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.BestCts3And4}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.pb1}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.pb2}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.bestPb}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.totalAvgOfCtsAndPbs}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.overAll}"> </fo:block>
                                </fo:table-cell>

                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block border-width="1mm" font-size="9pt" space-before="20pt" font-family="Times New Roman, serif">
                    <fo:table>
                        <fo:table-column column-width="93mm" border="1pt solid black"/>
                        <fo:table-column column-width="40mm" border="1pt solid black"/>
                        <fo:table-column column-width="40mm" border="1pt solid black"/>
                        <fo:table-header>
                            <fo:table-row font-weight="bold" border="1pt solid black">
                                <fo:table-cell text-align="center" number-columns-spanned="2">
                                    <fo:block> Co-Scholastic Area:Term-1 [On a 3 point (A-C) Grading Scale]</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row border="1pt solid black">
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" text-align="center">
                                    <fo:block> TERM-1 </fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" text-align="center">
                                    <fo:block> TERM-2 </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body th:each="second : ${model.body.secondTable}">
                            <fo:table-row border="1pt solid black">
                                <fo:table-cell>
                                    <fo:block th:text="${second.subjectName}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center">
                                    <fo:block th:text="${second.term1Grade}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center">
                                    <fo:block th:text="${second.term1Grade}"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block font-weight="bold" border-width="1mm" font-size="10pt" space-before="15pt" font-family="Times New Roman, serif">
                    Class Teacher's Remark :
                    <fo:inline font-weight="normal" border-bottom="1pt solid black"  th:text="${model.body.remarks}"> </fo:inline>
                </fo:block>


                <fo:block font-weight="bold" border-width="1mm" font-size="10pt" space-before="20pt" font-family="Times New Roman, serif">
                    Place :
                    <fo:inline font-weight="normal" border-bottom="1pt solid black"  th:text="${model.body.place}"> </fo:inline>
                </fo:block>

                <fo:block font-weight="bold" border-width="1mm" font-size="10pt" space-before="5pt" font-family="Times New Roman, serif">
                    Date :
                    <fo:inline font-weight="normal" border-bottom="1pt solid black" th:text="${model.body.date}"> </fo:inline>
                </fo:block>

            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>
