<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="10mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="98%" margin-top="-0.5cm" border="2pt solid black" padding="6mm">
                <fo:table border="none" space-after="4pt">
                    <fo:table-column column-width="1mm" />
                    <fo:table-column column-width="25mm"/>
                    <fo:table-column column-width="127mm" />
                    <fo:table-body>

                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block >
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block >
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="3pt"
                                          th:if="${model.body.orgSlug == 'pal454783'}">
                                    PALLAVI INTERNATIONAL SCHOOL
                                    <fo:block font-size="18">GANDIPET</fo:block>
                                </fo:block>
                                <fo:block margin-top="2mm" font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="3pt"
                                          th:if="${model.body.orgSlug != 'pal454783'}"
                                          th:text="${model.header.schoolName}">
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block >
                                </fo:block>
                            </fo:table-cell>

                            <fo:table-cell padding-top="-11mm">
                                <fo:block th:if="${model.body.orgSlug != null && !(#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}">
                                    <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg")'
                                                         content-width="75px"  content-height="75px"  />
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal988947', 'pal233196'}, model.body.orgSlug))}">
                                    <fo:external-graphic src='url("https://images.wexledu.com/live-worksheets/wexl-internal/20240901043131721.png")'
                                                         content-width="75px" scaling="non-uniform"  />
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug != null && (#lists.contains({'pal174599', 'pal332908', 'pal454783'}, model.body.orgSlug))}">
                                    <fo:external-graphic src='url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS.png)'
                                                         content-width="75px" scaling="non-uniform"  />
                                </fo:block>
                                <fo:block margin-left="160mm" padding-top="-28mm" th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}" >
                                    <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/30+Years+Logo+White.png")'
                                                         content-width="75px" scaling="non-uniform"  />
                                </fo:block>
                            </fo:table-cell>

                            <fo:table-cell>
                                <fo:block font-size="8pt" font-weight="bold"  space-before="3mm" font-family="Times New Roman, serif" text-align="center" space-after="3pt"
                                          th:if="${model.body.orgSlug == 'pal454783'}">
                                    <fo:inline th:replace="report-card/dps/fragment.xml :: ${model.body.orgSlug}"></fo:inline>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal454783'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630333 </fo:block>
                                </fo:block>

                                <fo:block th:if="${model.body.orgSlug == 'pal454783'}" font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt">Record
                                    of
                                    Academic Performance
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug == 'pal454783'}" font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt">
                                    Session:2024-25
                                </fo:block>

                                <fo:block th:if="${model.body.orgSlug != 'pal454783'}" margin-top="2mm" font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="3pt">
                                    ACADEMIC SESSION:
                                    <fo:inline th:text="${model.header.academicSection}">
                                    </fo:inline>
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug != 'pal454783'}" font-size="15pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-before="10pt" >
                                    PROGRESS CARD
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                    </fo:table-body>
                </fo:table>
                <fo:block th:if="${model.body.orgSlug != 'pal454783'}" padding-top="-28mm" border-width="1mm" font-size="12pt" space-before="30pt" space-after="6pt" font-family="Times New Roman, serif">
                    <fo:table border="none">
                        <fo:table-column column-width="53mm" />
                        <fo:table-column column-width="80mm" />
                        <fo:table-column column-width="28mm" />
                        <fo:table-column column-width="20mm" />
                        <fo:table-body >
                            <fo:table-row >
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block >
                                        Student's Name  &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:
                                    </fo:block>
                                </fo:table-cell >
                                <fo:table-cell text-align="left" padding="0mm">
                                    <fo:block th:text="${#strings.toUpperCase(model.body.name)}">
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block >
                                        Roll No &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left"  padding="0mm">
                                    <fo:block th:text="${model.body.rollNumber}">
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row >
                                <fo:table-cell >
                                    <fo:block padding-top="3mm" text-align="left" font-weight="bold" padding="0mm" >
                                        Mother's Name &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" padding="0mm">
                                    <fo:block padding-top="3mm" th:text="${model.body.motherName}">
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block padding-top="3mm" text-align="left" font-weight="bold" padding="0mm" >
                                        Date of Birth &#160;:
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" padding="0mm">
                                    <fo:block padding-top="3mm" th:text="${model.body.dateOfBirth}">
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell >
                                    <fo:block padding-top="3mm" text-align="left" font-weight="bold" padding="0mm" >
                                        Father's/ Guardian's Name :
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" padding="0mm">
                                    <fo:block padding-top="3mm" th:text="${model.body.fatherName}">
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block padding-top="3mm" text-align="left" font-weight="bold" padding="0mm" >
                                        Class/Section &#160;:
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" padding="0mm">
                                    <fo:block padding-top="3mm" th:text="${model.body.classAndSection}">
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block th:if="${model.body.orgSlug == 'pal454783'}" padding-top="-30mm" border-width="1mm" font-size="12pt" space-before="30pt" space-after="6pt" font-family="Times New Roman, serif">
                    <fo:table border="none">
                        <fo:table-column column-width="53mm" />
                        <fo:table-column column-width="80mm" />
                        <fo:table-column column-width="28mm" />
                        <fo:table-column column-width="20mm" />
                        <fo:table-body >
                            <fo:table-row >
                                <fo:table-cell >
                                    <fo:block text-align="left" font-weight="bold" padding="0mm" >
                                        Student Id&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" padding="0mm">
                                    <fo:block th:text="${model.header.studentId}">
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block >
                                        Class/Section &#160;:
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left"  padding="0mm">
                                    <fo:block th:text="${model.body.classAndSection}">
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row >
                                <fo:table-cell text-align="left" font-weight="bold" padding="0mm">
                                    <fo:block padding-top="3mm">
                                        Student's Name  &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:
                                    </fo:block>
                                </fo:table-cell >
                                <fo:table-cell text-align="left" padding="0mm">
                                    <fo:block padding-top="3mm" th:text="${#strings.toUpperCase(model.body.name)}">
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block padding-top="3mm" text-align="left" font-weight="bold" padding="0mm" >
                                        Roll No &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" padding="0mm">
                                    <fo:block padding-top="3mm" th:text="${model.body.rollNumber}">
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell >
                                    <fo:block padding-top="3mm" text-align="left" font-weight="bold" padding="0mm" >
                                        Mother's Name &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" padding="0mm">
                                    <fo:block padding-top="3mm" th:text="${model.body.motherName}">
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block padding-top="3mm" text-align="left" font-weight="bold" padding="0mm" >
                                        Date of Birth &#160;:
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" padding="0mm">
                                    <fo:block padding-top="3mm" th:text="${model.body.dateOfBirth}">
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block padding-top="3mm">Father's/ Guardian's Name :</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block padding-top="3mm" th:text="${model.body.fatherName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block th:if="${model.body.orgSlug == 'pal454783'}" font-size="13" text-align="center" space-before="10pt" font-family="Times New Roman, serif"> PART-I: SCHOLASTIC AREAS </fo:block>
                <fo:block th:if="${model.body.orgSlug != 'pal454783'}" space-before="25pt"></fo:block>
                <fo:block border-width="1mm" font-size="8pt" font-family="Times New Roman, serif">
                    <fo:table >
                        <fo:table-column column-width="8mm" border="1pt solid black"/>
                        <fo:table-column column-width="36.5mm" border="1pt solid black"/>
                        <fo:table-column column-width="9mm" border="1pt solid black"/>
                        <fo:table-column column-width="9mm" border="1pt solid black"/>
                        <fo:table-column column-width="11mm" border="1pt solid black"/>
                        <fo:table-column column-width="12mm" border="1pt solid black"/>
                        <fo:table-column column-width="12mm" border="1pt solid black"/>
                        <fo:table-column column-width="9mm" border="1pt solid black"/>
                        <fo:table-column column-width="10mm" border="1pt solid black"/>
                        <fo:table-column column-width="11mm" border="1pt solid black"/>
                        <fo:table-column column-width="12mm" border="1pt solid black"/>
                        <fo:table-column column-width="12mm" border="1pt solid black"/>
                        <fo:table-column column-width="12mm" border="1pt solid black"/>
                        <fo:table-column column-width="14.5mm" border="1pt solid black"/>
                        <fo:table-column column-width="12mm" border="1pt solid black"/>
                        <fo:table-header >
                            <fo:table-row font-weight="bold">
                                <fo:table-cell padding-top="2mm" number-rows-spanned="2">
                                    <fo:block text-align="center"> SNO</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="2mm" number-rows-spanned="2">
                                    <fo:block text-align="center"> SUBJECT </fo:block>
                                </fo:table-cell>
                                <fo:table-cell number-columns-spanned="5">
                                    <fo:block text-align="center"> TERM-1(100M) </fo:block>
                                </fo:table-cell>
                                <fo:table-cell number-columns-spanned="5">
                                    <fo:block text-align="center"> TERM-2(100M) </fo:block>
                                </fo:table-cell>
                                <fo:table-cell number-columns-spanned="3">
                                    <fo:block text-align="center"> OVERALL TERM1 + TERM2 </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row font-weight="bold" border="1pt solid black">

                                <fo:table-cell>
                                    <fo:block text-align="center"> PA-1 </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center"> PA-2 </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center"> HALF </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center"> MARKS </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center"> GRADE </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center"> PA-3 </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center"> PA-4 </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center"> FINAL </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center"> MARKS </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center"> GRADE </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center"> T1+T2 </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center"> OVERALL </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block text-align="center"> GRADE </fo:block>
                                </fo:table-cell>
                            </fo:table-row >
                        </fo:table-header>

                        <fo:table-body th:if="${model.body.firstTable != null and not #lists.isEmpty(model.body.firstTable)}" >
                            <fo:table-row text-align="center" border="1pt solid black" th:each="first : ${model.body.firstTable}">
                                <fo:table-cell >
                                    <fo:block th:text="${first.sno}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block th:text="${first.subjectName}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.pa1}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.pa2}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.half}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.term1Marks}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.term1Grade}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.pa3}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.pa4}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.finalMarks}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.term2Marks}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.term2Grade}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.totalMarks}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.overallMarks}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${first.overallGrade}"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>

                        <fo:table-body th:if="${model.body.firstTable == null or #lists.isEmpty(model.body.firstTable)}" >
                            <fo:table-row text-align="center" border="1pt solid black" >
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell >
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block th:if="${model.body.secondTable != null and #lists.size(model.body.secondTable) > 0 and model.body.orgSlug == 'pal454783'}"
                          font-size="12" text-align="center" font-family="Times New Roman, serif"> Additional Subjects </fo:block>
                <fo:block th:if="${model.body.orgSlug != 'pal454783'}" space-before="3mm"></fo:block>
                <fo:block th:if="${model.body.fourthTable != null and #lists.size(model.body.fourthTable) > 0}"
                        border-width="1mm" font-size="8pt" font-family="Times New Roman, serif">
                <fo:table >
                <fo:table-column column-width="8mm" border="1pt solid black"/>
                <fo:table-column column-width="36.5mm" border="1pt solid black"/>
                <fo:table-column column-width="9mm" border="1pt solid black"/>
                <fo:table-column column-width="9mm" border="1pt solid black"/>
                <fo:table-column column-width="11mm" border="1pt solid black"/>
                <fo:table-column column-width="12mm" border="1pt solid black"/>
                <fo:table-column column-width="12mm" border="1pt solid black"/>
                <fo:table-column column-width="9mm" border="1pt solid black"/>
                <fo:table-column column-width="10mm" border="1pt solid black"/>
                <fo:table-column column-width="11mm" border="1pt solid black"/>
                <fo:table-column column-width="12mm" border="1pt solid black"/>
                <fo:table-column column-width="12mm" border="1pt solid black"/>
                <fo:table-column column-width="12mm" border="1pt solid black"/>
                <fo:table-column column-width="14.5mm" border="1pt solid black"/>
                <fo:table-column column-width="12mm" border="1pt solid black"/>

                <fo:table-body >
                    <fo:table-row text-align="center" border="1pt solid black" th:each="first : ${model.body.fourthTable}">
                        <fo:table-cell >
                            <fo:block th:text="${first.sno}"> </fo:block>
                        </fo:table-cell>
                        <fo:table-cell >
                            <fo:block th:text="${first.subjectName}"> </fo:block>
                        </fo:table-cell>
                        <fo:table-cell>
                            <fo:block > </fo:block>
                        </fo:table-cell>
                        <fo:table-cell>
                            <fo:block > </fo:block>
                        </fo:table-cell>
                        <fo:table-cell>
                            <fo:block th:text="${first.half}"> </fo:block>
                        </fo:table-cell>
                        <fo:table-cell>
                            <fo:block th:text="${first.term1Marks}"> </fo:block>
                        </fo:table-cell>
                        <fo:table-cell>
                            <fo:block th:text="${first.term1Grade}"> </fo:block>
                        </fo:table-cell>
                        <fo:table-cell>
                            <fo:block > </fo:block>
                        </fo:table-cell>
                        <fo:table-cell>
                            <fo:block > </fo:block>
                        </fo:table-cell>
                        <fo:table-cell>
                            <fo:block th:text="${first.finalMarks}"> </fo:block>
                        </fo:table-cell>
                        <fo:table-cell>
                            <fo:block th:text="${first.term2Marks}"> </fo:block>
                        </fo:table-cell>
                        <fo:table-cell>
                            <fo:block th:text="${first.term2Grade}"> </fo:block>
                        </fo:table-cell>
                        <fo:table-cell>
                            <fo:block th:text="${first.totalMarks}"> </fo:block>
                        </fo:table-cell>
                        <fo:table-cell>
                            <fo:block th:text="${first.overallMarks}"> </fo:block>
                        </fo:table-cell>
                        <fo:table-cell>
                            <fo:block th:text="${first.overallGrade}"> </fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                </fo:table-body>
                </fo:table>
                </fo:block>

                <fo:block th:if="${model.body.secondTable != null and #lists.size(model.body.secondTable) > 0 and model.body.orgSlug == 'pal454783'}"
                          border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" padding-bottom="1mm" padding-top="1mm"> PART-II: CO-SCHOLASTIC AREAS </fo:block>
                <fo:block th:if="${model.body.orgSlug != 'pal454783'}" space-before="20pt"></fo:block>
                <fo:block th:if="${model.body.secondTable != null and #lists.size(model.body.secondTable) > 0}" border-width="1mm" font-size="9pt" font-family="Times New Roman, serif">
                    <fo:table>
                        <fo:table-column column-width="75mm" border="1pt solid black"/>
                        <fo:table-column column-width="20mm" border="1pt solid black"/>
                        <fo:table-column column-width="75mm" border="1pt solid black"/>
                        <fo:table-column column-width="20mm" border="1pt solid black"/>
                        <fo:table-header>
                            <fo:table-row font-weight="bold" border="1pt solid black">
                                <fo:table-cell text-align="center" number-columns-spanned="2">
                                    <fo:block> Co-Scholastic Area:Term-1 [On a 3 point (A-C) Grading Scale]</fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" text-align="center" number-columns-spanned="2">
                                    <fo:block> Co-Scholastic Area:Term-2 [On a 3 point (A-C) Grading Scale]</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row border="1pt solid black">
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" text-align="center">
                                    <fo:block> Grade </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" text-align="center">
                                    <fo:block> Grade </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body th:each="second : ${model.body.secondTable}">
                            <fo:table-row border="1pt solid black">
                                <fo:table-cell text-align="left" padding-left="2mm">
                                    <fo:block  th:text="${second.subjectName}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center">
                                    <fo:block th:text="${second.term1Grade}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" padding-left="2mm">
                                    <fo:block th:text="${second.subjectName}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center">
                                    <fo:block th:text="${second.term2Grade}"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block th:if="${model.body.thirdTable != null and #lists.size(model.body.thirdTable) > 0}" border-width="1mm" font-size="9pt" space-before="20pt" space-after="6pt" font-family="Times New Roman, serif">
                    <fo:table>
                        <fo:table-column column-width="75mm" border="1pt solid black"/>
                        <fo:table-column column-width="20mm" border="1pt solid black"/>
                        <fo:table-column column-width="75mm" border="1pt solid black"/>
                        <fo:table-column column-width="20mm" border="1pt solid black"/>
                        <fo:table-header>
                            <fo:table-row font-weight="bold" border="1pt solid black">
                                <fo:table-cell text-align="center" number-columns-spanned="2">
                                    <fo:block> Discipline: Term-1 [On a 3 point (A-C) Grading Scale] </fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" text-align="center" number-columns-spanned="2">
                                    <fo:block> Discipline: Term-2 [On a 3 point (A-C) Grading Scale] </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row border="1pt solid black">
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" text-align="center">
                                    <fo:block> Grade </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" text-align="center">
                                    <fo:block> Grade </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body th:each="third : ${model.body.thirdTable}">
                            <fo:table-row border="1pt solid black">
                                <fo:table-cell>
                                    <fo:block th:text="${third.subjectName}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center">
                                    <fo:block th:text="${third.term1Grade}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${third.subjectName}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center">
                                    <fo:block th:text="${third.term2Grade}"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <!-- Attendance -->
                <fo:block border-width="1mm" font-size="10pt" space-after="5pt" space-before="3pt" font-family="Times New Roman, serif">
                    <fo:block font-weight="bold" space-before="8pt" font-family="Times New Roman, serif">Attendance:</fo:block>
                    <fo:table border="none">
                        <fo:table-column column-width="63.5mm" />
                        <fo:table-column column-width="63.5mm" />
                        <fo:table-column column-width="63mm" />
                        <fo:table-header>
                            <fo:table-row font-size="8pt">
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>Total No. of Working days</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>No. of days Present</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block>Percentage of Attendance(%)</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body font-family="Times New Roman, serif"  font-size="7pt">
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.attendance.workingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.attendance.daysPresent}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.attendance.attendancePercentage}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block font-weight="bold" font-size="10pt" space-before="15pt" font-family="Times New Roman, serif">
                    Class Teacher's Remark :
                    <fo:inline font-weight="normal" th:text="${model.body.attendance.remarks}"> </fo:inline>
                </fo:block>

                <fo:block th:if="${model.body.orgSlug != null && !(#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}"
                          border-width="1mm" font-size="10pt" space-before="13mm" font-family="Arial, sans-serif">
                    <fo:table>
                        <fo:table-column/>
                        <fo:table-column/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell padding="0 2mm" font-weight="bold">
                                    <fo:block>Teacher Sign</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-left="58mm" font-weight="bold">
                                    <fo:block>Principal Sign</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block th:if="${model.body.orgSlug != null && (#lists.contains({'pal454783'}, model.body.orgSlug))}"
                          border-width="1mm" font-size="10pt" space-after="2mm" font-family="Arial, sans-serif">
                    <fo:table>
                        <fo:table-column column-width="60%"/>
                        <fo:table-column column-width="40%"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>
                                        <fo:block-container padding-left="25mm" padding-top="-7mm" width="100mm" height="35mm" display-align="center" text-align="center">
                                            <fo:block>
                                                <fo:external-graphic src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/signature.png')"
                                                                     scaling="non-uniform"
                                                                     content-width="32mm"
                                                                     content-height="14mm" />
                                            </fo:block>
                                        </fo:block-container>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell padding-top="-12mm" padding="0 2mm" font-weight="bold">
                                    <fo:block>Class Teacher Sign</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="-12mm" padding-left="40mm" font-weight="bold">
                                    <fo:block>Principal</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell number-columns-spanned="2" padding-top="-8mm">
                                    <fo:block border="0.3mm solid black" space-after="5" space-before="2"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block th:if="${(model.body.orgSlug matches 'pal174599|pal556078')}">
                    <fo:block border-width="1mm" font-size="10pt" space-before="10mm" space-after="2mm" font-family="Times New Roman, serif">
                        <fo:table border="none">
                            <fo:table-column column-width="50mm" />
                            <fo:table-column column-width="210mm" />
                            <fo:table-body font-family="Times New Roman, serif">
                                <fo:table-row >
                                    <fo:table-cell text-align="left" font-weight="bold">
                                        <fo:block>Class Teacher</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="center" font-weight="bold">
                                        <fo:block>Principal</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>

                <fo:block th:if="${(model.body.orgSlug matches 'pal233196')}">
                    <fo:block border-width="1mm" font-size="10pt" space-before="20mm" space-after="6mm" font-family="Times New Roman, serif">
                        <fo:table border="none">
                            <fo:table-column column-width="60mm" />
                            <fo:table-column column-width="7    0mm" />
                            <fo:table-column column-width="50mm" />
                            <fo:table-body font-family="Times New Roman, serif">
                                <fo:table-row >
                                    <fo:table-cell text-align="left" font-weight="bold">
                                        <fo:block>Class Teacher</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="center" font-weight="bold">
                                        <fo:block>Head Mistress</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="right" font-weight="bold">
                                        <fo:block>Principal</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>

                <fo:block th:if="${(model.body.orgSlug matches 'pal332908')}">
                    <fo:block border-width="1mm" font-size="10pt" space-before="20mm" space-after="6mm" font-family="Times New Roman, serif">
                        <fo:table border="none">
                            <fo:table-column column-width="50mm" />
                            <fo:table-column column-width="210mm" />
                            <fo:table-body font-family="Times New Roman, serif">
                                <fo:table-row >
                                    <fo:table-cell text-align="left" font-weight="bold">
                                        <fo:block>Principal / Headmaster</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="center" font-weight="bold">
                                        <fo:block>Class Teacher</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>

                <fo:block th:if="${(model.body.orgSlug matches 'pal988947')}">
                    <fo:block th:replace="report-card/dps/pallavi-signature.xml :: ${model.body.gradeSlug}"></fo:block>
                </fo:block>

                <fo:block th:if="${model.body.orgSlug == 'pal454783'}" padding-top="-3mm" font-weight="bold" font-size="10pt" font-family="Times New Roman, serif">
                    Name of the Class Teacher :
                    <fo:inline font-weight="normal" th:text="${model.body.classTeacherName}"> </fo:inline>
                </fo:block>

                <fo:block font-weight="bold" border-width="1mm" font-size="10pt" space-before="5pt" font-family="Times New Roman, serif">
                    Promoted to Class:
                    <fo:inline font-weight="normal" border-bottom="1pt solid black" th:text="${model.body.promotedClass}"> </fo:inline>
                </fo:block>

                <fo:block th:if="${model.body.orgSlug != 'pal454783'}" font-weight="bold" border-width="1mm" font-size="10pt" space-before="15pt" font-family="Times New Roman, serif">
                    Place :
                    <fo:inline font-weight="normal" border-bottom="1pt solid black"  th:text="${model.body.place}"> </fo:inline>
                </fo:block>

                <fo:block th:if="${model.body.orgSlug != 'pal454783'}" font-weight="bold" border-width="1mm" font-size="10pt" space-before="2pt" font-family="Times New Roman, serif">
                    Date :
                    <fo:inline font-weight="normal" border-bottom="1pt solid black" th:text="${model.body.date}"> </fo:inline>
                </fo:block>
                <!-- Report Card Result Table -->
                <fo:block border-width="1mm" font-size="10pt" padding-top="-2mm" space-before="1mm" font-family="Times New Roman, serif" >
                    <fo:block th:replace="report-card/dps/fragment.xml :: ${model.body.gradingScale}"></fo:block>
                </fo:block>
                <fo:block border-width="1mm" font-size="10pt" space-before="1mm" >
                    <fo:block  font-size="8"  font-family="Times New Roman, serif">PA : PERIODIC ASSESSMENT, HALF : HALF YEARLY, FINAL : FINAL EXAM, NI : NEEDS IMPROVEMENT</fo:block>
                </fo:block>

            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>
