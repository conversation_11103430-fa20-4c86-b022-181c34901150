<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="0mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container absolute-position="absolute" width="210mm" height="278mm">
                <fo:block>
                    <fo:external-graphic src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/first-bnr.png')"
                                         scaling="non-uniform"
                                         content-width="210mm"
                                         content-height="278mm"/>
                </fo:block>
                <fo:block-container absolute-position="absolute" top="13mm" left="55mm" width="100mm" height="100mm" display-align="center" text-align="center">
                    <fo:block>
                        <fo:external-graphic src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/sports-pic.png')"
                                             scaling="non-uniform"
                                             content-width="60mm"
                                             content-height="60mm"/>
                    </fo:block>
                </fo:block-container>
                <fo:block-container absolute-position="absolute" th:attr="top=${#strings.length(model.body.name) > 30 ? '165mm' : '155mm'}" left="55mm" width="100mm" height="100mm" display-align="center" text-align="center">
                    <fo:block>
                        <fo:external-graphic src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/Olympic_rings_without_rims.svg.png')"
                                             scaling="non-uniform"
                                             content-width="65mm"
                                             content-height="33mm"/>
                    </fo:block>
                </fo:block-container>
            </fo:block-container>
            <fo:block-container width="100%" height="85%" margin-top="0mm" padding="4mm">
                <fo:table border="none" >
                    <fo:table-column column-width="20mm" />
                    <fo:table-column column-width="160mm" />
                    <fo:table-column column-width="15mm"/>
                    <fo:table-body>
                        <fo:table-row>

                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block padding-top="5mm" font-size="20pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center"
                                          th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block>
                                    <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps-logo.png")'
                                                         content-width="75px"  content-height="75px"/>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row >
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block color="#38733c" padding-top="72mm" font-size="20pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="3.5mm">
                                    PHYSICAL EDUCATION &amp; SPORTS </fo:block>

                                <fo:block text-align="center" margin-left="80mm" margin-right="50mm" space-after="10pt">
                                    <fo:instream-foreign-object content-width="80%" content-height="80%">
                                        <svg width="350" height="35" xmlns="http://www.w3.org/2000/svg">
                                            <polygon points="30,50 270,50 230,0 0,0" fill="#38733c" stroke="#138808" stroke-width="1"/>
                                            <text x="36%" y="68%" font-size="20pt" font-family="Helvetica" font-weight="900" fill="white" text-anchor="middle">
                                                REPORT CARD
                                            </text>
                                        </svg>
                                    </fo:instream-foreign-object>
                                </fo:block>
                                <fo:block space-before="8mm" font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center">
                                    Name of the Student
                                </fo:block>
                                <fo:block color="#38733c" font-size="25pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center"
                                          th:text="${#strings.toUpperCase(model.body.name)}">
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block space-before="4mm" color="#38733c">
                    <fo:table table-layout="fixed" width="100%">
                        <fo:table-column column-width="19mm"/>
                        <fo:table-column column-width="51mm"/>
                        <fo:table-column column-width="10mm"/>
                        <fo:table-column column-width="51mm"/>
                        <fo:table-column column-width="10mm"/>
                        <fo:table-column column-width="51mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell background-color="#38733c" color="white" border="1pt solid black" padding="5pt">
                                    <fo:block text-align="center" >
                                        <fo:block font-weight="bold" margin-left="2mm" margin-right="2mm" border-bottom="2pt dotted white" padding-bottom="2pt">Class &amp; Section</fo:block>
                                        <fo:block space-before="1mm" th:text="${model.body.sectionName}"> </fo:block>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell background-color="#38733c" color="white" border="1pt solid black" padding="5pt">
                                    <fo:block text-align="center">
                                        <fo:block font-weight="bold" margin-left="3mm" margin-right="3mm" border-bottom="2pt dotted white" padding-bottom="2pt">Student ID</fo:block>
                                        <fo:block space-before="1mm" th:text="${model.body.studentId}"> </fo:block>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell background-color="#38733c" color="white" border="1pt solid black" padding="5pt">
                                    <fo:block text-align="center">
                                        <fo:block font-weight="bold" margin-left="3mm" margin-right="3mm" border-bottom="2pt dotted white" padding-bottom="2pt">Roll No</fo:block>
                                        <fo:block space-before="1mm" th:text="${model.body.rollNumber}"> </fo:block>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                    <fo:block margin-right="6mm" margin-left="6mm" space-before="6mm" border-bottom="4pt dotted green" line-height="1.5">
                    </fo:block>

                    <fo:block space-before="48mm" color="#441778" font-size="14pt" font-weight="bold" text-align="center"> "Faster, Higher, Stronger - Together" </fo:block>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>


    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container absolute-position="absolute" width="278mm" height="278mm">
                <fo:block>
                    <fo:external-graphic src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/second-bnr.png')"
                                         scaling="non-uniform"
                                         content-width="210mm"
                                         content-height="278mm"/>
                </fo:block>
            </fo:block-container>
            <fo:block-container width="100%" height="85%" margin-top="0mm" padding="4mm">
                <fo:table border="none" >
                    <fo:table-column column-width="30mm"/>
                    <fo:table-column column-width="150mm" />
                    <fo:table-column column-width="15mm"/>
                    <fo:table-body>
                        <fo:table-cell>
                            <fo:block> </fo:block>
                        </fo:table-cell>
                        <fo:table-cell>
                            <fo:block padding-top="4mm" font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center"
                                      th:text="${#strings.toUpperCase(model.header.schoolName)}">
                            </fo:block>
                        </fo:table-cell>
                        <fo:table-cell padding-top="-1mm">
                            <fo:block>
                                <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps-logo.png")'
                                                     content-width="75px"  content-height="75px"/>
                            </fo:block>
                        </fo:table-cell>
                    </fo:table-body>
                </fo:table>
                <fo:table border="none" padding-top="1mm" padding-left="4mm" padding-right="4mm">
                    <fo:table-column column-width="25mm"/>
                    <fo:table-column column-width="160mm" />
                    <fo:table-body>
                        <fo:table-cell>
                            <fo:block></fo:block>
                        </fo:table-cell>
                        <fo:table-cell color="#10823d">
                            <fo:block padding-top="-5mm" font-size="11pt" font-weight="bold" font-family="Dancing Script" text-align="center" space-after="4pt" padding-left="60pt">
                                DEPARTMENT OF PHYSICAL EDUCATION &amp; SPORTS SCIENCES
                                <fo:block>
                                    P.E &amp; SPORTS PERFORMANCE REPORT CARD
                                </fo:block>
                            </fo:block>
                            <fo:block font-size="11pt" font-weight="bold" font-family="Dancing Script" text-align="center" space-before="3pt" space-after="6pt">
                                <fo:inline >
                                    AREA OF SPECIALIZATION -
                                    <fo:inline th:text="${#strings.toUpperCase(model.body.gameTitle)}" color="#10823d"></fo:inline>
                                </fo:inline>
                            </fo:block>
                        </fo:table-cell>
                    </fo:table-body>
                </fo:table>



                <fo:block margin-left="8mm" font-family="Times New Roman, serif" border-width="2mm" font-size="7pt" text-align="center" space-before="3mm" >
                    <fo:table border="1pt solid black" >
                        <fo:table-column column-width="28mm" />
                        <fo:table-column column-width="41.5mm" />
                        <fo:table-column column-width="41.5mm" />
                        <fo:table-column column-width="41mm" />
                        <fo:table-column column-width="41mm" />
                        <fo:table-header background-color="#2f633a" color="white">
                            <fo:table-row>
                                <fo:table-cell number-columns-spanned="5" border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.table[0].tableTitle}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>Area of Learning</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block th:text="${model.body.table[0].columns.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block th:text="${model.body.table[0].columns.column2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block th:text="${model.body.table[0].columns.column3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block th:text="${model.body.table[0].columns.column4}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body  >
                            <fo:table-row th:each="dets : ${model.body.table[0].attributes}">
                                <fo:table-cell  border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${dets.areaOfLearning}" font-weight="bold"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${dets.ratings.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${dets.ratings.column2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${dets.ratings.column3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${dets.ratings.column4}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>



                <fo:block margin-left="8mm" font-family="Times New Roman, serif" border-width="2mm" font-size="7pt" text-align="center" space-before="3mm" th:if="${#lists.size(model.body.table) > 1 }">
                    <fo:table border="1pt solid black" >
                        <fo:table-column column-width="28mm" />
                        <fo:table-column column-width="41.5mm" />
                        <fo:table-column column-width="41.5mm" />
                        <fo:table-column column-width="41mm" />
                        <fo:table-column column-width="41mm" />
                        <fo:table-header background-color="#3e8bc7" color="white">
                            <fo:table-row>
                                <fo:table-cell number-columns-spanned="5" border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.table[1].tableTitle}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>Area of Learning</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block th:text="${model.body.table[1].columns.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block th:text="${model.body.table[1].columns.column2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block th:text="${model.body.table[1].columns.column3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block th:text="${model.body.table[1].columns.column4}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body >
                            <fo:table-row th:each="dets : ${model.body.table[1].attributes}">
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${dets.areaOfLearning}" font-weight="bold"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${dets.ratings.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${dets.ratings.column2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${dets.ratings.column3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${dets.ratings.column4}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>



                <fo:block margin-left="8mm" font-family="Times New Roman, serif" border-width="2mm" font-size="7pt" text-align="center" space-before="3mm" th:if="${ #lists.size(model.body.table) > 2}">
                    <fo:table border="1pt solid black" >
                        <fo:table-column column-width="28mm" />
                        <fo:table-column column-width="41.5mm" />
                        <fo:table-column column-width="41.5mm" />
                        <fo:table-column column-width="41mm" />
                        <fo:table-column column-width="41mm" />
                        <fo:table-header background-color="#259c8e" color="white">
                            <fo:table-row>
                                <fo:table-cell number-columns-spanned="5" border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block th:text="${model.body.table[2].tableTitle}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>Area of Learning</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block th:text="${model.body.table[2].columns.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block th:text="${model.body.table[2].columns.column2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block th:text="${model.body.table[2].columns.column3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block th:text="${model.body.table[2].columns.column4}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body >
                            <fo:table-row th:each="dets : ${model.body.table[2].attributes}">
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${dets.areaOfLearning}" font-weight="bold"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${dets.ratings.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${dets.ratings.column2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${dets.ratings.column3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${dets.ratings.column4}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block margin-left="8mm" font-family="Times New Roman, serif" border-width="2mm" font-size="7pt" text-align="center" space-before="3mm" >
                    <fo:table border="1pt solid black">
                        <fo:table-column column-width="28mm" />
                        <fo:table-column column-width="46.5mm" />
                        <th:block th:if="${model.body.mainTable != null and model.body.mainTable.size() > 1 and model.body.mainTable[1]?.tableName != null}">
                            <fo:table-column column-width="46.5mm" />
                        </th:block>
                        <th:block th:if="${model.body.mainTable != null and model.body.mainTable.size() > 2 and model.body.mainTable[2]?.tableName != null}">
                            <fo:table-column column-width="47mm" />
                        </th:block>
                        <fo:table-column column-width="25mm" />

                        <fo:table-header background-color="#565657" color="white">
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" number-rows-spanned="2">
                                    <fo:block padding-top="5mm" text-align="center">Area of Learning</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.mainTable[0].tableName}" text-align="left" padding-top="3mm"></fo:block>
                                </fo:table-cell>
                                <th:block th:if="${model.body.mainTable != null and model.body.mainTable.size() > 1 and model.body.mainTable[1]?.tableName != null}">
                                    <fo:table-cell border="1pt solid black" font-weight="bold">
                                        <fo:block th:text="${model.body.mainTable[1].tableName}" text-align="left" padding-top="3mm"></fo:block>
                                    </fo:table-cell>
                                </th:block>
                                <th:block th:if="${model.body.mainTable != null and model.body.mainTable.size() > 2 and model.body.mainTable[2]?.tableName != null}">
                                    <fo:table-cell border="1pt solid black" font-weight="bold">
                                        <fo:block th:text="${model.body.mainTable[2].tableName}" text-align="left" padding-top="3mm"></fo:block>
                                    </fo:table-cell>
                                </th:block>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" number-rows-spanned="2">
                                    <fo:block padding-top="6mm" text-align="center">Final Grade</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold">
                                    <fo:block th:text="${model.body.mainTable[0].AreaOfLearning}" padding-top="3mm" text-align="left"></fo:block>
                                </fo:table-cell>
                                <th:block th:if="${model.body.mainTable != null and model.body.mainTable.size() > 1 and model.body.mainTable[1]?.tableName != null}">
                                    <fo:table-cell border="1pt solid black" font-weight="bold">
                                        <fo:block th:text="${model.body.mainTable[1].AreaOfLearning}" padding-top="3mm" text-align="left"></fo:block>
                                    </fo:table-cell>
                                </th:block>
                                <th:block th:if="${model.body.mainTable != null and model.body.mainTable.size() > 2 and model.body.mainTable[2]?.tableName != null}">
                                    <fo:table-cell border="1pt solid black" font-weight="bold">
                                        <fo:block th:text="${model.body.mainTable[2].AreaOfLearning}" padding-top="3mm" text-align="left"></fo:block>
                                    </fo:table-cell>
                                </th:block>
                            </fo:table-row>
                        </fo:table-header>

                        <fo:table-body >
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block font-weight="bold" text-align="center">Learning Level</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${model.body.mainTable[0].LearningLevel}"></fo:block>
                                </fo:table-cell>
                                <th:block th:if="${model.body.mainTable != null and model.body.mainTable.size() > 1 and model.body.mainTable[1]?.tableName != null}">
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                        <fo:block th:text="${model.body.mainTable[1].LearningLevel}"></fo:block>
                                    </fo:table-cell>
                                </th:block>
                                <th:block th:if="${model.body.mainTable != null and model.body.mainTable.size() > 2 and model.body.mainTable[2]?.tableName != null}">
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                        <fo:block th:text="${model.body.mainTable[2].LearningLevel}"></fo:block>
                                    </fo:table-cell>
                                </th:block>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${model.body.overallLevel}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block font-weight="bold" text-align="center"> Grade</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${model.body.mainTable[0].Grade}"></fo:block>
                                </fo:table-cell>
                                <th:block th:if="${model.body.mainTable != null and model.body.mainTable.size() > 1 and model.body.mainTable[1]?.tableName != null}">
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                        <fo:block th:text="${model.body.mainTable[1].Grade}"></fo:block>
                                    </fo:table-cell>
                                </th:block>
                                <th:block th:if="${model.body.mainTable != null and model.body.mainTable.size() > 2 and model.body.mainTable[2]?.tableName != null}">
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                        <fo:block th:text="${model.body.mainTable[2].Grade}"></fo:block>
                                    </fo:table-cell>
                                </th:block>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${model.body.overallGrade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>
