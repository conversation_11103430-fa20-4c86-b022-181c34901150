<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="bordered-page" page-width="210mm" page-height="297mm" margin="0mm">
            <fo:region-body margin="5mm"/>
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="bordered-page">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" border="1pt solid #A9A9A9">

            <!-- Title and student details content -->
            <fo:block font-family="Arial" font-size="15pt" space-after="10mm" padding-top="5mm">
                <fo:inline> </fo:inline>
                <fo:inline padding-left="5mm" th:text="${model.header.testName}"> </fo:inline>
            </fo:block>
            <fo:block border-bottom="1pt solid #A9A9A9" space-after="5mm"/>
            <fo:block space-after="5mm">
                <fo:inline> </fo:inline>
                <fo:inline padding-left="5mm">Name : </fo:inline>
                <fo:inline th:text="${model.header.studentName}"> </fo:inline>
            </fo:block>
                <fo:block space-after="5mm">
                    <fo:inline> </fo:inline>
                    <fo:inline padding-left="5mm">Father's Name : </fo:inline>
                    <fo:inline th:text="${model.header.fatherName}"> </fo:inline>
                </fo:block>
                <fo:block space-after="5mm">
                    <fo:inline> </fo:inline>
                    <fo:inline padding-left="5mm">Date of Birth : </fo:inline>
                    <fo:inline th:text="${model.header.dob}"> </fo:inline>
                </fo:block>
            <fo:block space-after="5mm">
                <fo:inline> </fo:inline>
                <fo:inline padding-left="5mm">Admission Number : </fo:inline>
                <fo:inline th:text="${model.header.admissionNo}"> </fo:inline>
            </fo:block>
            <fo:block space-after="5mm">
                <fo:inline> </fo:inline>
                <fo:inline padding-left="5mm">Academic Year : </fo:inline>
                <fo:inline th:text="${model.header.academicYear}"> </fo:inline>
            </fo:block>

            <!-- Subject Marks Table -->
            <fo:table space-before="10mm" space-after="3mm">
                <fo:table-column column-width="50mm"/>
                <fo:table-column column-width="150mm"/>
                <fo:table-body>
                    <fo:table-row border-bottom="1pt solid #A9A9A9" height="5mm">
                        <fo:table-cell>
                            <fo:block font-size="12pt" font-family="Times New Roman, serif" padding-left="2mm">
                                <fo:inline> </fo:inline>
                                <fo:inline padding-left="5mm">Subject</fo:inline>
                            </fo:block>
                        </fo:table-cell>
                        <fo:table-cell>
                            <fo:block font-size="12pt" font-family="Times New Roman, serif">Marks</fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                    <fo:table-row border-bottom="1pt solid #A9A9A9" height="10mm" th:each="data : ${model.body.firstTable}">
                        <fo:table-cell >
                            <fo:block font-size="12pt" font-family="Times New Roman, serif" padding-top="3mm" >
                            <fo:inline> </fo:inline>
                                <fo:inline padding-left="5mm" th:text="${data.subject}"> </fo:inline>
                            </fo:block>
                        </fo:table-cell>
                        <fo:table-cell>
                            <fo:block font-size="12pt" font-family="Times New Roman, serif" padding-top="3mm" th:text="${data.marks}"> </fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                </fo:table-body>
            </fo:table>
            <fo:table space-before="1mm" space-after="5mm">
                <fo:table-column column-width="50mm" />
                <fo:table-column column-width="150mm" />
                <fo:table-body>
                    <fo:table-row border-bottom="1pt solid black" height="7mm">
                        <fo:table-cell >
                            <fo:block font-size="12pt" font-family="Times New Roman, serif" text-align="left">
                                <fo:inline> </fo:inline>
                                <fo:inline padding-left="5mm">Total </fo:inline>
                            </fo:block>
                        </fo:table-cell>
                        <fo:table-cell >
                            <fo:block font-size="12pt" font-family="Times New Roman, serif" text-align="left" th:text="${model.body.total}">
                            </fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                    <fo:table-row  border-bottom="1pt solid black" height="10mm">
                        <fo:table-cell >
                            <fo:block font-size="12pt"
                                      font-family="Times New Roman, serif" text-align="left" padding-top="2mm">
                                <fo:inline> </fo:inline>
                                <fo:inline padding-left="5mm">Average</fo:inline>
                            </fo:block>
                        </fo:table-cell>
                        <fo:table-cell >
                            <fo:block font-size="12pt"
                                      font-family="Times New Roman, serif" text-align="left" padding-top="2mm" th:text="${model.body.average}">
                            </fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                </fo:table-body>
            </fo:table>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>
