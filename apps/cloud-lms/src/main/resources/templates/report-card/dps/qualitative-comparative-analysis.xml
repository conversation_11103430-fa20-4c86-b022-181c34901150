<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="report-card" page-width="210mm" page-height="297mm">
            <fo:region-body margin="16mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="report-card">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" border="2pt solid black" padding="6mm">
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="300%" content-height="300%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter)" x="0" y="0" width="100%" height="100%"
                                       th:xlink:href="@{${model.header.schoolWaterMark}}"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none">
                    <fo:table-column column-width="26mm" />
                    <fo:table-column column-width="120mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block >
                                    <fo:external-graphic th:src="@{${model.header.schoolLogo}}"
                                                         content-width="23mm" scaling="non-uniform" />
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="3pt"
                                          th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold"  font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:text="${model.header.address}">
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:text="${model.header.isoData}">
                                </fo:block>
                                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="3pt">Record
                                    of
                                    Academic Performance
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt"
                                          th:text="${model.body.testType}">
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt"
                                >PEN &amp; PAPER
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="6pt"
                                          th:text="${model.header.academicYear}">
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" space-before="10mm" space-after="2pt">
                    <fo:table border="none">
                        <fo:table-column column-width="33mm" />
                        <fo:table-column column-width="85mm" />
                        <fo:table-column column-width="26mm" />
                        <fo:table-column column-width="30mm" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Name of the Student&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.name}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Class &amp; Section &#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.body.className}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block   margin-bottom="2mm">Student Id&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.header.admissionNumber}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Roll No&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.body.rollNumber}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Mother's Name&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.mothersName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Date of Birth&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.dateOfBirth}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>

                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" space-after="30pt">
                    <fo:table border="none">
                        <fo:table-column column-width="40mm" />
                        <fo:table-column column-width="85mm" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Father's/Guardian's Name&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" th:text="${model.body.fathersName}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <!-- Report Card Table -->
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" >
                    <fo:block  font-size="9" font-weight="bold" font-family="Times New Roman, serif">PART I : SCHOLASTIC AREAS</fo:block>
                    <fo:table border="1pt solid black">
                        <fo:table-column  column-width="9mm"  />
                        <fo:table-column column-width="70mm" />
                        <fo:table-column column-width="31mm"  />
                        <fo:table-column column-width="31mm"  />
                        <fo:table-column column-width="31mm"  />
                        <fo:table-header font-size="9pt" >
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>S.NO</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>Maximum Marks</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>Marks Obtained</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>Grade Obtained</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row th:each="marks : ${model.body.firstTable.marks}">
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.sno}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${marks.subject}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.maximumMarks}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.marksObtained}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.gradeObtained}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding-left="10mm" padding="1mm" font-family="Times New Roman, serif"
                                               number-columns-spanned="2" text-align="left"  font-weight="bold" >
                                    <fo:block>TOTAL</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.totalMaximumMarks}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.totalMarksObtained}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.totalGradeObtained}"></fo:block>
                                </fo:table-cell>

                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding-left="10mm" padding="1mm" font-family="Times New Roman, serif"
                                               number-columns-spanned="3" text-align="left"  font-weight="bold" >
                                    <fo:block>OVERALL PERCENTAGE/GRADE</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.overallPercentage}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.overallGrade}"></fo:block>
                                </fo:table-cell>

                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block border-width="1mm" font-size="9pt" text-align="center"  space-before="15mm"
                          th:if="${model.body.secondTable.marks != null and #lists.size(model.body.secondTable.marks) > 0}">
                    <fo:block   font-size="9" font-weight="bold" text-align="left" font-family="Times New Roman, serif" space-after="1mm">
                        ADDITIONAL SUBJECT :
                    </fo:block>
                    <fo:table border="1pt solid black" >
                        <fo:table-column  column-width="9mm"  />
                        <fo:table-column column-width="70mm" />
                        <fo:table-column column-width="31mm"  />
                        <fo:table-column column-width="31mm"  />
                        <fo:table-column column-width="31mm"  />


                        <fo:table-body  font-family="Times New Roman, serif">
                            <fo:table-row th:each="marks : ${model.body.secondTable.marks}" >
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.sno}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${marks.subject}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm"  >
                                    <fo:block th:text="${marks.maximumMarks}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.marksObtained}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.gradeObtained}"></fo:block>
                                </fo:table-cell>

                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block th:if="${ model.body.orgSlug != 'dps688668'  and model.body.testType != 'PERIODIC TEST-3' and (model.body.gradeSlug != 'x' or model.body.gradeSlug != 'ix')}">
                <fo:block border-width="1mm" font-size="10pt" space-before="25mm" space-after="2mm">
                    <fo:table border="none">
                        <fo:table-body font-family="Times New Roman, serif">
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Remarks&#160;:</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block>
                    <fo:inline>
                        <fo:block-container>
                            <fo:block border-bottom="0.2mm solid black" line-height="2.5" padding-bottom="-3mm" th:text="${model.body.attendance.remarks}">
                            </fo:block>
                        </fo:block-container>
                    </fo:inline>
                </fo:block>
                </fo:block>



                <!-- Attendance -->
                <fo:block border-width="1mm" font-size="10pt" space-after="7pt" font-family="Times New Roman, serif" space-before="10mm">
                    <fo:block font-weight="bold" space-after="5pt" font-family="Times New Roman, serif">Attendance&#160;:</fo:block>
                    <fo:table border="none">
                        <fo:table-column column-width="52mm" />
                        <fo:table-column column-width="60mm" />
                        <fo:table-column column-width="41mm" />
                        <fo:table-column column-width="39mm" />
                        <fo:table-body font-family="Times New Roman, serif" >
                            <fo:table-row space-after="5pt">
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Total Number of Working Days&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.attendance.workingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Number of Days Present&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.attendance.daysPresent}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <!-- Signature Block-->
                <fo:block th:if="${model.body.orgSlug} == 'dps688668'">
                <fo:block border-width="1mm" font-size="10pt" space-before="40pt" space-after="15mm" font-family="Times New Roman, serif">
                    <fo:table border="none">
                        <fo:table-column column-width="55mm" />
                        <fo:table-column column-width="55mm" />
                        <fo:table-column column-width="50mm" />
                        <fo:table-body font-family="Times New Roman, serif">
                            <fo:table-row >
                                <fo:table-cell text-align="left" font-weight="bold">
                                    <fo:block>Sr.Principal</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center" font-weight="bold">
                                    <fo:block>Vice Principal</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="right" font-weight="bold">
                                    <fo:block>Class Teacher</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} != 'dps688668' and ${model.body.orgSlug} != 'del909850'">
                    <fo:block border-width="1mm" font-size="10pt" space-before="40pt" space-after="15mm" font-family="Times New Roman, serif">
                        <fo:table border="none">
                            <fo:table-column column-width="55mm" />
                            <fo:table-column column-width="55mm" />
                            <fo:table-column column-width="50mm" />
                            <fo:table-body font-family="Times New Roman, serif">
                                <fo:table-row >
                                    <fo:table-cell text-align="left" font-weight="bold">
                                        <fo:block>Principal</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="center" font-weight="bold">
                                        <fo:block>Vice Principal</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="right" font-weight="bold">
                                        <fo:block>Class Teacher</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'del909850'" space-after="15mm">
                    <fo:block th:replace="report-card/dps/signatureForMahendra.xml :: ${model.body.gradeSlug}"></fo:block>
                </fo:block>
                <fo:block th:replace="report-card/dps/fragment.xml :: ${model.body.gradingScale}"></fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <fo:page-sequence master-reference="report-card">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" border="2pt solid black" padding="6mm">
                <!-- Logo -->
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="400%" content-height="400%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="8"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="8"/>
                                            <feFuncB type="linear" slope="8"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter)" x="0" y="0" width="100%" height="100%"  th:xlink:href="@{${model.header.schoolWaterMark}}"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
            <fo:block-container position="absolute" top="0.5mm" left="0.1mm" width="15mm" height="15mm" >
                <fo:block>
                    <fo:external-graphic th:src="@{${model.header.schoolLogo}}"
                                         content-width="23mm" scaling="non-uniform"/>
                </fo:block>
            </fo:block-container>

            <!-- Header Section -->
            <fo:block font-family="Times New Roman, serif" font-size="12pt" text-align="center" space-after="1cm">
                <fo:block font-size="18pt" font-weight="bold" space-after="0.5cm"  th:text="${#strings.toUpperCase(model.header.schoolName)}"></fo:block>
                <fo:block font-size="8pt" font-weight="bold" space-after="0.5cm" th:text="${model.header.address}"></fo:block>
                <fo:block font-size="8pt" font-weight="bold" space-after="0.5cm" th:text="${model.header.isoData}"></fo:block>
                <fo:block font-size="14pt" font-weight="bold" space-after="0.5cm">QUALITATIVE COMPARATIVE ANALYSIS</fo:block>
                <fo:block font-size="17pt" space-after="0.5cm">Scholastic Assessment</fo:block>
            </fo:block>

                <fo:block border-width="1mm" font-size="10pt" font-family="Times New Roman, serif" space-before="5mm" space-after="2pt">
                    <fo:table border="none">
                        <fo:table-column column-width="36mm" />
                        <fo:table-column column-width="60mm" />
                        <fo:table-column column-width="30mm" />
                        <fo:table-column column-width="60mm" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Academic Year&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.header.academicYear}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Campus &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.header.campus}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block   margin-bottom="2mm">Student Id&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.header.admissionNumber}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Curriculum&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.header.curriculum}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Name of the Student&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.header.studentName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Class &amp; Section&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.className}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

            <!-- Student Information -->

                <!-- Bar Chart -->
                <fo:block text-align="center" space-before="1cm">
                    <fo:instream-foreign-object content-width="200%" content-height="400%">
                        <svg xmlns="http://www.w3.org/2000/svg"
                             th:attr="width=${(model.body.subjectPercentages.size() * 120) + 50} + 'px',
                       viewBox='0 0 ' + (${(model.body.subjectPercentages.size() * 120) + 100}) + ' 400'">
                            <!-- Y-Axis -->
                            <line x1="40" y1="60" x2="40" y2="370" stroke="black" stroke-width="3"/>
                            <!-- X-Axis -->
                            <line x1="40" y1="370"
                                  th:attr="x2=${40 + (model.body.subjectPercentages.size() * 120)}"
                                  y2="370" stroke="black" stroke-width="3"/>

                            <!-- Dynamically Render Bars -->
                            <th:block th:each="subject, iter : ${model.body.subjectPercentages}">
                                <!-- Bars for PT1B -->
                                <rect th:if="${subject.pt1ActualPercentage != null && subject.pt1ActualPercentage > 0}"
                                      th:attr="x=${75 + (iter.index * 120)},
                                 y=${370 - subject.pt1ActualPercentage},
                                 height=${subject.pt1ActualPercentage}"
                                      width="25" fill="grey" />

                                <text th:attr="x=${85 + (iter.index * 120)},
                                      y=${370 - (subject.pt1ActualPercentage / 100.0 * 300)/3 - 5}"
                                      font-size="9"
                                      text-anchor="middle"
                                      font-weight="bold"
                                      fill="black"
                                      th:text="${subject.pt1ActualPercentage/3}" />


                                <!-- Bars for PT2B -->
                                <rect th:if="${subject.pt2ActualPercentage != null && subject.pt2ActualPercentage > 0}"
                                      th:attr="x=${100 + (iter.index * 120)},
                                 y=${370 - subject.pt2ActualPercentage},
                                 height=${subject.pt2ActualPercentage}"
                                      width="25" fill="black" />
                                <text th:attr="x=${110 + (iter.index * 120)},
                                      y=${370 - (subject.pt2ActualPercentage / 100.0 * 300)/3 - 5}"
                                      font-size="9"
                                      text-anchor="middle"
                                      font-weight="bold"
                                      fill="black"
                                      th:text="${subject.pt2ActualPercentage/3}" />

                                <!-- Bars for PT3B -->
                                <rect th:if="${subject.pt3ActualPercentage != null && subject.pt3ActualPercentage > 0}"
                                      th:attr="x=${125 + (iter.index * 120)},
                                 y=${370 - subject.pt3ActualPercentage},
                                 height=${subject.pt3ActualPercentage}"
                                      width="25" fill="#D2B48C" />
                                <text th:attr="x=${140 + (iter.index * 120)},
                                      y=${370 - (subject.pt3ActualPercentage / 100.0 * 300)/3 - 5}"
                                      font-size="9"
                                      text-anchor="middle"
                                      font-weight="bold"
                                      fill="black"
                                      th:text="${subject.pt3ActualPercentage/3}" />

                                <!-- Bars for PT4B -->
                                <rect th:if="${subject.pt4ActualPercentage != null && subject.pt4ActualPercentage > 0}"
                                      th:attr="x=${150 + (iter.index * 120)},
                                 y=${370 - subject.pt4ActualPercentage},
                                 height=${subject.pt4ActualPercentage}"
                                      width="25" fill="#D3D3D3" />


                                <!-- X-Axis Labels -->
                                <text th:attr="x=${45 + (iter.index * 120) + 30}"
                                      y="390" font-size="12"
                                      text-align="center" font-weight="600"
                                      th:text="${subject.subjectName}"></text>
                            </th:block>

                            <!-- Y-Axis Labels -->
                            <text x="30" y="370" font-size="12" text-anchor="end">0</text>
                            <text x="30" y="340" font-size="12" text-anchor="end">10</text>
                            <text x="30" y="310" font-size="12" text-anchor="end">20</text>
                            <text x="30" y="280" font-size="12" text-anchor="end">30</text>
                            <text x="30" y="250" font-size="12" text-anchor="end">40</text>
                            <text x="30" y="220" font-size="12" text-anchor="end">50</text>
                            <text x="30" y="190" font-size="12" text-anchor="end">60</text>
                            <text x="30" y="160" font-size="12" text-anchor="end">70</text>
                            <text x="30" y="130" font-size="12" text-anchor="end">80</text>
                            <text x="30" y="100" font-size="12" text-anchor="end">90</text>
                            <text x="30" y="70" font-size="12" text-anchor="end">100</text>
                        </svg>
                    </fo:instream-foreign-object>

                    <fo:instream-foreign-object content-width="200%" content-height="200%" text-align="center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 400 500">

                            <!-- Legend -->
                            <g>
                                <!-- Border Rectangle -->
                                <rect x="20" y="130" width="380" height="80" fill="none" stroke="black" stroke-width="3"/>

                                <!-- PT1B -->
                                <th:block th:if="${model.body.colors.gray == 'gray'}">
                                    <g>
                                        <rect x="30" y="150" width="30" height="30" fill="grey"/>
                                        <text x="70" y="170" font-size="16" th:text="${model.body.testNames[0]}"></text>
                                    </g>
                                </th:block>

                                <!-- PT2B -->
                                <th:block th:if="${model.body.colors.black == 'black' && model.body.colors.gray == 'gray'}">
                                    <g>
                                        <rect x="120" y="150" width="30" height="30" fill="black"/>
                                        <text x="160" y="170" font-size="16" th:text="${model.body.testNames[1]}"></text>
                                    </g>
                                </th:block>

                                <!-- PT2B Fallback -->
                                <th:block th:if="${model.body.colors.black == 'black' && model.body.colors.gray != 'gray'}">
                                    <g>
                                        <rect x="120" y="150" width="30" height="30" fill="black"/>
                                        <text x="160" y="170" font-size="16" th:text="${model.body.testNames[0]}"></text>
                                    </g>
                                </th:block>

                                <!-- PT3B -->
                                <th:block th:if="${model.body.colors.pink == 'pink' && model.body.colors.gray == 'gray' && model.body.colors.black == 'black'}">
                                    <g>
                                        <rect x="210" y="150" width="30" height="30" fill="#D2B48C"/>
                                        <text x="250" y="170" font-size="16" th:text="${model.body.testNames[2]}"></text>
                                    </g>
                                </th:block>

                                <!-- PT3B Fallback -->
                                <th:block th:if="${model.body.colors.pink == 'pink' && model.body.colors.gray != 'gray' && model.body.colors.black != 'black'}">
                                    <g>
                                        <rect x="210" y="150" width="30" height="30" fill="#D2B48C"/>
                                        <text x="250" y="170" font-size="16" th:text="${model.body.testNames[0]}"></text>
                                    </g>
                                </th:block>

                                <!-- PT3B Alternate Fallback -->
                                <th:block th:if="${model.body.colors.pink == 'pink' && ((model.body.colors.gray == 'gray' && model.body.colors.black != 'black')
        || (model.body.colors.gray != 'gray' && model.body.colors.black == 'black'))}">
                                    <g>
                                        <rect x="210" y="150" width="30" height="30" fill="#D2B48C"/>
                                        <text x="250" y="170" font-size="16" th:text="${model.body.testNames[1]}"></text>
                                    </g>
                                </th:block>

                                <!-- PT4B -->
                                <th:block th:if="${model.body.colors.green == 'green' && model.body.colors.gray == 'gray'
        && model.body.colors.black == 'black' && model.body.colors.pink == 'pink'
        && model.body.testNames[3] != null && !#strings.isEmpty(model.body.testNames[3])}">
                                    <g>
                                        <rect x="300" y="150" width="30" height="30" fill="#D3D3D3"/> <!-- Gray for PT4B -->
                                        <text x="340" y="170" font-size="16" th:text="${model.body.testNames[3]}"></text>
                                    </g>
                                </th:block>
                            </g>

                        </svg>
                    </fo:instream-foreign-object>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>
