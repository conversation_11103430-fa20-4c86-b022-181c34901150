<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format"
         xmlns:th="http://www.thymeleaf.org"
         xmlns:xsl="http://www.w3.org/1999/XSL/Transform">

    <fo:layout-master-set>
        <fo:simple-page-master master-name="gate-pass"
                               page-height="29.7cm" page-width="21cm"
                               margin="1.5cm">
            <fo:region-body background-color="#ffffff"/>
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="gate-pass">
        <fo:flow flow-name="xsl-region-body" th:if="${model.status == 'APPROVED'}">

            <fo:block-container width="100%" padding="6mm" border="1pt solid #b3e5fc">

                <fo:table table-layout="fixed" width="100%" margin-bottom="5mm">
                    <fo:table-column column-width="25%"/>
                    <fo:table-column column-width="75%"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell padding="2mm">
                                <fo:block th:if="${model.schoolLogo != null}">
                                    <fo:external-graphic th:src="'url(' + @{${model.schoolLogo}} + ')'"
                                                         content-width="40mm" content-height="scale-to-fit"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding="2mm" text-align="center">
                                <fo:block font-size="18pt" font-weight="bold" color="#0277bd" font-family="Arial, sans-serif"
                                          th:text="${model.schoolName}">SCHOOL NAME</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:table width="100%" margin-bottom="5mm" border="1pt solid #0288d1">
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell padding="4mm" text-align="center" background-color="#b3e5fc">
                                <fo:block font-size="16pt" font-weight="bold" color="#01579b"
                                          th:text="${model.type == 'GATEPASS' ? 'GATE PASS' : (model.type == 'APPOINTMENT' ? 'APPOINTMENT' : '')}"/>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:table width="100%" table-layout="fixed" margin-bottom="5mm" border="1pt solid #ccc">
                    <fo:table-column column-width="35%"/>
                    <fo:table-column column-width="65%"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell padding="2mm" background-color="#e1f5fe" border="1pt solid #ccc">
                                <fo:block font-weight="bold" font-size="10pt">Pass Number:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding="2mm" border="1pt solid #ccc">
                                <fo:block th:text="${model.gatePassNumber}" font-size="10pt"/>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell padding="2mm" background-color="#e1f5fe" border="1pt solid #ccc">
                                <fo:block font-weight="bold" font-size="10pt">Date:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding="2mm" border="1pt solid #ccc">
                                <fo:block th:text="${model.gatePassDate}" font-size="10pt"/>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:block font-size="12pt" font-weight="bold" color="#0288d1"
                          font-family="Arial, sans-serif" border-bottom="1pt solid #bdbdbd"
                          margin-bottom="4mm" padding-bottom="2mm">STUDENT INFORMATION</fo:block>

                <fo:table table-layout="fixed" width="100%" margin-bottom="5mm" border="1pt solid #ccc">
                    <fo:table-column column-width="25%"/>
                    <fo:table-column column-width="25%"/>
                    <fo:table-column column-width="25%"/>
                    <fo:table-column column-width="25%"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell padding="2mm" border="1pt solid #ccc"><fo:block font-weight="bold">Name:</fo:block></fo:table-cell>
                            <fo:table-cell padding="2mm" border="1pt solid #ccc"><fo:block th:text="${model.studentName}"/></fo:table-cell>
                            <fo:table-cell padding="2mm" border="1pt solid #ccc"><fo:block font-weight="bold">Class:</fo:block></fo:table-cell>
                            <fo:table-cell padding="2mm" border="1pt solid #ccc"><fo:block th:text="${model.className}"/></fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell padding="2mm" border="1pt solid #ccc"><fo:block font-weight="bold">Section:</fo:block></fo:table-cell>
                            <fo:table-cell padding="2mm" border="1pt solid #ccc"><fo:block th:text="${model.sectionName}"/></fo:table-cell>
                            <fo:table-cell padding="2mm" border="1pt solid #ccc"><fo:block font-weight="bold">Roll No:</fo:block></fo:table-cell>
                            <fo:table-cell padding="2mm" border="1pt solid #ccc"><fo:block th:text="${model.rollNumber}"/></fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:block th:if="${model.type == 'GATEPASS'}" margin-bottom="5mm">
                    <fo:block font-size="12pt" font-weight="bold" color="#0288d1"
                              font-family="Arial, sans-serif" border-bottom="1pt solid #bdbdbd"
                              margin-bottom="4mm" padding-bottom="2mm">PICKUP INFORMATION</fo:block>

                    <fo:table width="100%" table-layout="fixed" border="1pt solid #ccc">
                        <fo:table-column column-width="33.33%"/>
                        <fo:table-column column-width="33.33%"/>
                        <fo:table-column column-width="33.33%"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell padding="2mm" border="1pt solid #ccc">
                                    <fo:block font-weight="bold">Relation:</fo:block>
                                    <fo:block th:text="${model.pickupPersonRelation}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm" border="1pt solid #ccc">
                                    <fo:block font-weight="bold">Name:</fo:block>
                                    <fo:block th:text="${model.pickupPersonName}"/>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm" border="1pt solid #ccc">
                                    <fo:block font-weight="bold">Mobile:</fo:block>
                                    <fo:block th:text="${model.pickupPersonMobile}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block th:if="${model.type == 'APPOINTMENT'}" margin-bottom="5mm">
                    <fo:block font-size="12pt" font-weight="bold" color="#0288d1"
                              font-family="Arial, sans-serif" border-bottom="1pt solid #bdbdbd"
                              margin-bottom="4mm" padding-bottom="2mm">APPOINTMENT INFORMATION</fo:block>

                    <fo:table width="100%" table-layout="fixed" border="1pt solid #ccc">
                        <fo:table-column column-width="50%"/>
                        <fo:table-column column-width="50%"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell padding="2mm" border="1pt solid #ccc">
                                    <fo:block font-weight="bold">Meeting Person:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm" border="1pt solid #ccc">
                                    <fo:block font-weight="bold">Teacher:</fo:block>
                                    <fo:block th:text="${model.approvedBy}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block font-size="11pt" font-weight="bold" margin-top="3mm" margin-bottom="1mm">Reason:</fo:block>
                <fo:block th:text="${model.reason}" font-size="10pt" margin-bottom="5mm"/>

                <fo:block font-size="12pt" font-weight="bold" color="#0288d1"
                          font-family="Arial, sans-serif" border-bottom="1pt solid #bdbdbd"
                          margin-bottom="4mm" padding-bottom="2mm">APPROVAL DETAILS</fo:block>

                <fo:table width="100%" table-layout="fixed" border="1pt solid #ccc">
                    <fo:table-column column-width="33.3%"/>
                    <fo:table-column column-width="33.3%"/>
                    <fo:table-column column-width="33.3%"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell padding="2mm" border="1pt solid #ccc"><fo:block font-weight="bold">Status:</fo:block></fo:table-cell>
                            <fo:table-cell padding="2mm" border="1pt solid #ccc"><fo:block font-weight="bold">Approved By:</fo:block></fo:table-cell>
                            <fo:table-cell padding="2mm" border="1pt solid #ccc"><fo:block font-weight="bold">Approval Date:</fo:block></fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell padding="2mm" border="1pt solid #ccc"><fo:block th:text="${model.status}"/></fo:table-cell>
                            <fo:table-cell padding="2mm" border="1pt solid #ccc"><fo:block th:text="${model.approvedBy}"/></fo:table-cell>
                            <fo:table-cell padding="2mm" border="1pt solid #ccc"><fo:block th:text="${model.approvalDate}"/></fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:block text-align="center" font-size="8pt" font-style="italic" color="#616161"
                          margin-top="5mm" border-top="1pt solid #bdbdbd" padding-top="2mm">
                    This pass is valid only for the date mentioned above. Please present this document at the school gate.
                </fo:block>

            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>