<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="16mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" border="2pt solid black" padding="6mm">
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="300%" content-height="300%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="25"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="25"/>
                                            <feFuncB type="linear" slope="25"/>
                                        </feComponentTransfer>
                                    </filter>
                                    <filter id="brightnessFilter1">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/>
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter1)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug != null && (#lists.contains({'pal174599', 'pal332908', 'pal454783'}, model.body.orgSlug))}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/Pallavi-model-school_25.png"/>
                                <image filter="url(#brightnessFilter1)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal988947', 'pal233196'}, model.body.orgSlug))}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/Pallavi-Model-School-Favicon_5.png"/>
                                <image filter="url(#brightnessFilter)"
                                       x="0" y="0" width="100%" height="100%"
                                       th:if="${model.body.orgSlug != null && !(#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}"
                                       xlink:href="https://dpsgurugram84.com/wp-content/uploads/2019/07/logo_dps.png"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none">
                    <fo:table-column column-width="26mm" />
                    <fo:table-column column-width="126mm" />
                    <fo:table-body >
                        <fo:table-row>
                            <fo:table-cell>

                                <fo:block th:if="${model.body.orgSlug != null && !(#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}">
                                    <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg")'
                                                         content-width="75px"  content-height="75px"  />
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal988947', 'pal233196'}, model.body.orgSlug))}">
                                    <fo:external-graphic src='url("https://images.wexledu.com/live-worksheets/wexl-internal/20240901043131721.png")'
                                                         content-width="75px" scaling="non-uniform"  />
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug != null && (#lists.contains({'pal174599', 'pal332908', 'pal454783'}, model.body.orgSlug))}">
                                    <fo:external-graphic src='url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS.png)'
                                                         content-width="75px" scaling="non-uniform"  />
                                </fo:block>
                                <fo:block margin-left="154mm" padding-top="-29mm" th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}" >
                                    <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/30+Years+Logo+White.png")'
                                                         content-width="75px" scaling="non-uniform"  />
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" padding-top="-5mm" space-after="0pt" padding-bottom="2mm"
                                          th:if="${model.body.orgSlug == 'pal332908'}">
                                    PALLAVI INTERNATIONAL SCHOOL
                                    <fo:block font-size="14">SAGAR ROAD, HYDERABAD</fo:block>
                                </fo:block>
                                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" padding-top="-5mm" space-after="0pt"
                                          th:if="${model.body.orgSlug == 'pal454783'}">
                                    PALLAVI INTERNATIONAL SCHOOL
                                    <fo:block font-size="18">GANDIPET</fo:block>
                                </fo:block>
                                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" padding-top="-5mm" space-after="0pt"
                                          th:if="${model.body.orgSlug != 'pal332908' and model.body.orgSlug != 'pal454783' }"
                                          th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block>
                                <fo:block-container width="100%" height="100%" margin-top="0cm"  th:if="${model.body.orgSlug == 'dps688668'}">
                                    <fo:block border-width="1mm" font-size="10pt" space-before="5mm"
                                              font-family="Times New Roman, serif" padding-top="-5mm">
                                        <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
                                            Plot No.44, 42A, BEHIND NACHARAM TELEPHONE EXCHANGE, NACHARAM,
                                        </fo:block>
                                        <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
                                            UPPAL(M),MEDCHAL DISTRICT,HYDERABAD-500076</fo:block>
                                    </fo:block>
                                </fo:block-container>
                                <fo:block font-size="8pt" font-weight="bold"  space-before="3mm" font-family="Times New Roman, serif" text-align="center" space-after="3pt" padding-top="-5mm"
                                          th:if="${model.body.orgSlug != 'dps688668'}">
                                    <fo:inline th:replace="report-card/dps/fragment.xml :: ${model.body.orgSlug}"></fo:inline>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${ model.body.orgSlug == 'del189476' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630520 </fo:block>
                                    <fo:block> School Code:56955 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal454783'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630333 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal988947'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630095
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal233196'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :130145
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal174599'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630290
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal556078'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-before="5pt"
                                          th:if="${ model.body.orgSlug == 'del765517' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630285 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-before="5pt"
                                          th:if="${ model.body.orgSlug == 'del909850' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630448 </fo:block>
                                    <fo:block>  ISO 9001:2005, ISO 45001:2018, ISO 21001:2018 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${!(model.body.orgSlug matches 'pal556078|del765517|del909850|del189476|pal332908|pal174599|pal233196|pal988947|pal454783')}">
                                    <fo:block th:text="${model.header.isoData}"></fo:block>
                                </fo:block>
                                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt">Record
                                    of
                                    Academic Performance
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt">

                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt"
                                          th:text="${model.header.academicYear}">
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif"  space-after="2pt"
                          th:padding-top="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug)) ? '-25mm' : '-7mm'}">
                    <fo:table border="none">
                        <fo:table-column column-width="33mm" />
                        <fo:table-column column-width="93mm" />
                        <fo:table-column column-width="26mm" />
                        <fo:table-column column-width="30mm" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="1mm">Name of the Student&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="1mm" th:text="${model.body.name}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="1mm">Class &amp; Section &#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" margin-bottom="1mm" th:text="${model.body.className}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block   margin-bottom="1mm">Student Id&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.header.studentId}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="1mm">Roll No&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" margin-bottom="1mm" th:text="${model.body.rollNumber}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="1mm">Mother's Name&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="1mm" th:text="${#strings.toUpperCase(model.body.mothersName)}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="1mm">Date of Birth&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="1mm" th:text="${model.body.dateOfBirth}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>

                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" space-after="3pt">
                    <fo:table border="none">
                        <fo:table-column column-width="40mm" />
                        <fo:table-column column-width="85mm" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Father's/Guardian's Name&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" th:text="${#strings.toUpperCase(model.body.fathersName)}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <!-- Report Card Table -->
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" >
                    <fo:block th:text="${model.body.firstTable.title}"  font-size="9" font-weight="bold" font-family="Times New Roman, serif"></fo:block>
                    <fo:table border="1pt solid black">
                        <fo:table-column  column-width="8mm"  />
                        <fo:table-column column-width="28mm" />
                        <fo:table-column column-width="10mm"  />
                        <fo:table-column column-width="7mm"  />
                        <fo:table-column column-width="7mm"  />
                        <fo:table-column column-width="9mm"  />
                        <fo:table-column column-width="13mm"  />
                        <fo:table-column column-width="10mm"  />
                        <fo:table-column column-width="9.5mm"  />
                        <fo:table-column column-width="7mm"  />
                        <fo:table-column column-width="7mm"  />
                        <fo:table-column column-width="9mm"  />
                        <fo:table-column column-width="13.5mm"  />
                        <fo:table-column column-width="9.5mm"  />
                        <fo:table-column column-width="11mm" />
                        <fo:table-column column-width="11.5mm" />
                        <fo:table-column column-width="9.5mm" />
                        <fo:table-header font-size="9pt" >
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" number-rows-spanned="2">
                                    <fo:block>S.NO</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left" number-rows-spanned="2">
                                    <fo:block>SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  number-columns-spanned="6" >
                                    <fo:block >TERM-I</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" number-columns-spanned="6" >
                                    <fo:block>TERM-II</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" number-columns-spanned="3">
                                    <fo:block>TERM-1+TERM-II </fo:block>
                                </fo:table-cell>

                            </fo:table-row>
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column1}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block th:text="${model.body.firstTable.column2}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block th:text="${model.body.firstTable.column3}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block th:text="${model.body.firstTable.column4}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block >Marks obtained (100)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block >Grade</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column5}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block th:text="${model.body.firstTable.column6}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block th:text="${model.body.firstTable.column7}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block th:text="${model.body.firstTable.column8}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block >Marks Obtained (100)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block >Grade</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block >T1+T2 (200)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block >Overall Marks (100)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block >Grade</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row th:each="marks : ${model.body.firstTable.marks}">
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.sno}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${marks.subject}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.pt}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.se1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.nb1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.hye}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.marksObtained1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.grade1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.pa2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.nb2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.se2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.ye}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.marksObtained2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.grade2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.t1t2Total}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.overAllMarks}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.overAllGrade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding-left="10mm" padding="1mm" font-family="Times New Roman, serif"
                                               number-columns-spanned="6" text-align="left"  font-weight="bold" >
                                    <fo:block>OVERALL GRADE</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.overallPercentage}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.grade}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif" number-columns-spanned="4">
                                    <fo:block ></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.term2OverallPercentage}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.term2Grade}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.totalT1AndT2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.averageT1AndT2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.totalGrade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block th:if="${model.body.orgSlug != 'pal233196'}">
                <fo:block border-width="1mm" font-size="9pt" text-align="center"  space-before="3mm"
                          th:if="${model.body.firstTable.external != null and #lists.size(model.body.firstTable.external) > 0}">

                    <fo:table border="1pt solid black" >
                        <fo:table-column column-width="8mm" />
                        <fo:table-column column-width="28mm" />

                        <fo:table-column column-width="10mm"  />
                        <fo:table-column column-width="7mm"  />
                        <fo:table-column column-width="7mm"  />
                        <fo:table-column column-width="9mm"  />
                        <fo:table-column column-width="13mm"  />
                        <fo:table-column column-width="9.5mm"  />
                        <fo:table-column column-width="10mm"  />
                        <fo:table-column column-width="7mm"  />
                        <fo:table-column column-width="7mm"  />
                        <fo:table-column column-width="9mm"  />
                        <fo:table-column column-width="13.5mm"  />
                        <fo:table-column column-width="9.5mm"  />
                        <fo:table-column column-width="11mm" />
                        <fo:table-column column-width="11.5mm" />
                        <fo:table-column column-width="9.5mm" />
                        <fo:table-body  font-family="Times New Roman, serif">
                            <fo:table-row th:each="external : ${model.body.firstTable.external}" >
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${external.sno}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${external.subject}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${external.pt}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${external.se1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${external.nb1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${external.hye}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${external.pa2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${external.nb2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${external.se2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${external.ye}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug == 'pal233196'}">
                    <fo:block border-width="1mm" font-size="9pt" text-align="center"  space-before="3mm"
                              th:if="${model.body.firstTable.external != null and #lists.size(model.body.firstTable.external) > 0}">

                        <fo:table border="1pt solid black" >
                            <fo:table-column column-width="8mm" />
                            <fo:table-column column-width="45mm" />
                            <fo:table-column column-width="20mm"  />
                            <fo:table-column column-width="18.5mm"  />
                            <fo:table-column column-width="21.5mm"  />
                            <fo:table-column column-width="21mm"  />
                            <fo:table-column column-width="23mm"  />
                            <fo:table-column column-width="22.5mm" />
                            <fo:table-header font-family="Times New Roman, serif">
                                <fo:table-row text-align="center">
                                    <fo:table-cell number-rows-spanned="2" border="1pt solid black" >
                                        <fo:block padding-top="3mm" font-weight="bold"> S.NO </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell number-rows-spanned="2" border="1pt solid black" text-align="left">
                                        <fo:block padding-top="3mm" font-weight="bold" margin-left="1mm">SUBJECT</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell number-columns-spanned="2" border="1pt solid black">
                                        <fo:block font-weight="bold" padding-top="0.5mm">TERM - I</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell number-columns-spanned="2" border="1pt solid black">
                                        <fo:block font-weight="bold" padding-top="0.5mm">TERM - II</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell number-columns-spanned="2" border="1pt solid black">
                                        <fo:block font-weight="bold" padding-top="0.5mm"> OVERALL </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row text-align="center">
                                    <fo:table-cell border="1pt solid black">
                                        <fo:block font-weight="bold" padding-top="0.5mm">MARKS(50)</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black">
                                        <fo:block font-weight="bold" padding-top="0.5mm">GRADE</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black">
                                        <fo:block font-weight="bold" padding-top="0.5mm">MARKS(50)</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black">
                                        <fo:block font-weight="bold" padding-top="0.5mm">GRADE</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black">
                                        <fo:block font-weight="bold" padding-top="0.5mm">MARKS(100)</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black">
                                        <fo:block font-weight="bold" padding-top="0.5mm">GRADE</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body  font-family="Times New Roman, serif">
                                <fo:table-row th:each="external : ${model.body.firstTable.external}" >
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${external.sno}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                        <fo:block th:text="${external.subject}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${external.hye1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${external.hyeGrade}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${external.ye1}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${external.yeGrade}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${external.hyeGradeAndYeMarks}"></fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding="1mm">
                                        <fo:block th:text="${external.hyeGradeAndYeGrade}"></fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>

                <!--third Table-->
                <fo:block border-width="1mm" font-size="9pt"  space-before="6" text-align="center" font-family="Times New Roman, serif" display-align="center">
                    <fo:block th:text="${model.body.secondTable.title}"  font-size="9" font-weight="bold" text-align="center" font-family="Times New Roman, serif"></fo:block>

                    <fo:table border="1pt solid black" text-align="center"  >
                        <fo:table-column column-width="81mm" />
                        <fo:table-column column-width="49mm" />
                        <fo:table-column column-width="49mm" />
                        <fo:table-header>
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left" padding-left="2mm">
                                    <fo:block>SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>TERM-I</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block >TERM-II</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row th:each="marks : ${model.body.secondTable.marks}">
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" padding-left="2mm">
                                    <fo:block th:text="${marks.subjectName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" >
                                    <fo:block th:text="${marks.term1Grade}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" >
                                    <fo:block th:text="${marks.term2Grade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <!-- Attendance -->
                <fo:block border-width="1mm" font-size="10pt" space-after="3pt" font-family="Times New Roman, serif">
                    <fo:block font-weight="bold" space-before="5pt" font-family="Times New Roman, serif">Attendance:</fo:block>
                    <fo:table border="none">
                        <fo:table-column column-width="32mm" />
                        <fo:table-column column-width="33mm" />
                        <fo:table-column column-width="23mm" />
                        <fo:table-column column-width="39mm" />
                        <fo:table-column column-width="26mm" />
                        <fo:table-column column-width="30mm" />

                        <fo:table-body font-family="Times New Roman, serif">
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Total working days:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.attendance.workingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Days Present:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.attendance.daysPresent}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Attendance %:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.attendance.attendancePercentage}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="10pt" space-after="7mm">
                    <fo:inline font-weight="bold">Remarks&#160;:</fo:inline>
                    <fo:inline border-bottom="0.2mm solid black" font-size="9pt" th:text="${model.body.attendance.remarks}"/>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug == 'pal454783'}">
                    <fo:block border-width="1mm" font-size="10pt" font-family="Times New Roman, serif" space-before="10mm">
                        <fo:table border="none">
                            <fo:table-column column-width="45mm" />
                            <fo:table-column column-width="60mm" />
                            <fo:table-column column-width="95mm" />
                            <fo:table-body font-family="Times New Roman, serif">
                                <fo:table-row>
                                    <fo:table-cell>
                                        <fo:block padding-top="-7mm"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block padding-top="-7mm"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block>
                                            <fo:block-container padding-top="-5mm" width="100mm" height="35mm" display-align="center" text-align="center">
                                                <fo:block>
                                                    <fo:external-graphic src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/signature.png')"
                                                                         scaling="non-uniform"
                                                                         content-width="35mm"
                                                                         content-height="15mm" />
                                                </fo:block>
                                            </fo:block-container>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>

                                <fo:table-row >
                                    <fo:table-cell padding-top="-12mm" text-align="left" font-weight="bold">
                                        <fo:block>Class Teacher Signature  </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block padding-top="-12mm" > </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-left="12mm" padding-top="-12mm"  text-align="center" font-weight="bold">
                                        <fo:block>Principal</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>


                <!--Signature Block-->
                <fo:block th:if="${!(model.body.orgSlug matches 'pal556078|pal174599|pal332908|pal988947|pal233196|pal454783|dps688668|del909850|del765517|del189476')}"
                          border-width="1mm" font-size="10pt" space-after="2pt" font-family="Times New Roman, serif">
                    <fo:table border="none">
                        <fo:table-column column-width="50mm" />
                        <fo:table-column column-width="50mm" />
                        <fo:table-column column-width="50mm" />
                        <fo:table-body font-family="Times New Roman, serif">
                            <fo:table-row >
                                <fo:table-cell text-align="left" font-weight="bold">
                                    <fo:block>Sr.Principal</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center" font-weight="bold">
                                    <fo:block>Head Mistress</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="right" font-weight="bold">
                                    <fo:block>Class Teacher</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>

                </fo:block>

                <fo:block th:if="${(model.body.orgSlug matches 'del909850|del765517')}"
                          border-width="1mm" font-size="10pt" space-after="2pt" font-family="Times New Roman, serif">
                    <fo:table border="none">
                        <fo:table-column column-width="60mm" />
                        <fo:table-column column-width="60mm" />
                        <fo:table-column column-width="50mm" />
                        <fo:table-body font-family="Times New Roman, serif">
                            <fo:table-row >
                                <fo:table-cell text-align="left" font-weight="bold">
                                    <fo:block>Principal</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center" font-weight="bold">
                                    <fo:block>Vice Principal</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="right" font-weight="bold">
                                    <fo:block>Class Teacher</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>

                </fo:block>

                <fo:block th:if="${(model.body.orgSlug == 'del189476')}"
                          border-width="1mm" font-size="10pt" space-after="2pt" font-family="Times New Roman, serif">
                    <fo:table border="none">
                        <fo:table-column column-width="60mm" />
                        <fo:table-column column-width="60mm" />
                        <fo:table-column column-width="50mm" />
                        <fo:table-body font-family="Times New Roman, serif">
                            <fo:table-row >
                                <fo:table-cell text-align="left" font-weight="bold">
                                    <fo:block>Class Teacher</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center" font-weight="bold">
                                    <fo:block>Head Mistress</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="right" font-weight="bold">
                                    <fo:block>Principal</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>

                </fo:block>

                <fo:block th:if="${model.body.orgSlug == 'dps688668' and model.body.gradeSlug == 'vi'}"
                          border-width="1mm" font-size="10pt" space-after="2pt" space-before="3mm" font-family="Times New Roman, serif">
                    <fo:table border="none">
                        <fo:table-column column-width="50mm" />
                        <fo:table-column column-width="50mm" />
                        <fo:table-column column-width="50mm" />
                        <fo:table-body font-family="Times New Roman, serif">
                            <fo:table-row >
                                <fo:table-cell text-align="left" font-weight="bold">
                                    <fo:block>Principal</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center" font-weight="bold">
                                    <fo:block>Sr Head Mistress</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="right" font-weight="bold">
                                    <fo:block>Class Teacher</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>

                </fo:block>

                <fo:block th:if="${model.body.orgSlug == 'dps688668' and (model.body.gradeSlug == 'vii' or model.body.gradeSlug == 'viii')}"
                          border-width="1mm" font-size="10pt" space-after="2pt" font-family="Times New Roman, serif" space-before="3mm">
                    <fo:table border="none">
                        <fo:table-column column-width="50mm" />
                        <fo:table-column column-width="50mm" />
                        <fo:table-column column-width="50mm" />
                        <fo:table-body font-family="Times New Roman, serif">
                            <fo:table-row >
                                <fo:table-cell text-align="left" font-weight="bold">
                                    <fo:block>Principal</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center" font-weight="bold">
                                    <fo:block>Vice Principal</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="right" font-weight="bold">
                                    <fo:block>Class Teacher</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>

                </fo:block>

                <fo:block th:if="${(model.body.orgSlug matches 'pal174599|pal233196|pal556078')}">
                    <fo:block border-width="1mm" font-size="10pt" space-before="18mm" space-after="5mm" font-family="Times New Roman, serif">
                        <fo:table border="none">
                            <fo:table-column column-width="50mm" />
                            <fo:table-column column-width="210mm" />
                            <fo:table-body font-family="Times New Roman, serif">
                                <fo:table-row >
                                    <fo:table-cell text-align="left" font-weight="bold">
                                        <fo:block>Class Teacher</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="center" font-weight="bold">
                                        <fo:block>Principal</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>

                <fo:block th:if="${(model.body.orgSlug matches 'pal332908')}">
                    <fo:block border-width="1mm" font-size="10pt" space-before="10mm" space-after="6mm" font-family="Times New Roman, serif">
                        <fo:table border="none">
                            <fo:table-column column-width="50mm" />
                            <fo:table-column column-width="210mm" />
                            <fo:table-body font-family="Times New Roman, serif">
                                <fo:table-row >
                                    <fo:table-cell text-align="left" font-weight="bold">
                                        <fo:block>Principal / Headmaster</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="center" font-weight="bold">
                                        <fo:block>Class Teacher</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>

                <fo:block th:if="${(model.body.orgSlug matches 'pal988947')}">
                    <fo:block th:replace="report-card/dps/pallavi-signature.xml :: ${model.body.gradeSlug}"></fo:block>
                </fo:block>

                <!--Report Card Result Table-->
                <fo:block border-width="1mm" font-size="10pt" font-family="Times New Roman, serif" padding-top="-6mm">
                    <fo:block th:replace="report-card/dps/fragment.xml :: ${model.body.gradingScale}"></fo:block>
                </fo:block>

                <fo:block border-width="1mm" font-size="10pt" space-before="1mm" th:if="${model.body.orgSlug != 'dps688668'}">
                    <fo:block  font-size="8"  font-family="Times New Roman, serif">PT : PERIODIC TEST,NB : NOTE BOOK,SE : SUBJECT ENRICHMENT,HYE : HALF YEARLY, YE : YEARLY, NI : NEEDS IMPROVEMENT</fo:block>
                </fo:block>

                <fo:block border-width="1mm" font-size="10pt" space-before="1mm" th:if="${model.body.orgSlug == 'dps688668'}">
                    <fo:block font-weight="bold" font-size="8"  font-family="Times New Roman, serif">PT : PERIODIC TEST,NB : NOTE BOOK,SE : SUBJECT ENRICHMENT,HYE : HALF YEARLY, YE : YEARLY, NI : NEEDS IMPROVEMENT</fo:block>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>


    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.8cm" border="2pt solid black" padding="6mm">
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="300%" content-height="300%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="25"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="25"/>
                                            <feFuncB type="linear" slope="25"/>
                                        </feComponentTransfer>
                                    </filter>
                                    <filter id="brightnessFilter1">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/>
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter1)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug != null && (#lists.contains({'pal174599', 'pal332908', 'pal454783'}, model.body.orgSlug))}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/Pallavi-model-school_25.png"/>
                                <image filter="url(#brightnessFilter1)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal988947', 'pal233196'}, model.body.orgSlug))}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/Pallavi-Model-School-Favicon_5.png"/>
                                <image filter="url(#brightnessFilter)"
                                       x="0" y="0" width="100%" height="100%"
                                       th:if="${model.body.orgSlug != null && !(#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}"
                                       xlink:href="https://dpsgurugram84.com/wp-content/uploads/2019/07/logo_dps.png"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>

                <!-- Header Section -->
                <fo:block th:if="${model.body.orgSlug == 'pal233196'}" >
                    <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="3pt"
                    >PALLAVI MODEL </fo:block>
                    <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="3pt">
                        SCHOOL BOWENPALLY</fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug == 'pal174599'}" >
                    <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="3pt"
                    >PALLAVI AWARE INTERNATIONAL </fo:block>
                    <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="3pt">
                        SCHOOL SAROOR NAGAR</fo:block>
                </fo:block>

                <fo:block th:if="${model.body.orgSlug != 'pal233196' and model.body.orgSlug != 'pal174599'}">
                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="3pt"
                          th:text="${#strings.toUpperCase(model.header.schoolName)}">

                </fo:block>
                </fo:block>
                <fo:table border="none">
                    <fo:table-column column-width="26mm" />
                    <fo:table-column column-width="126mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell padding-top="-9mm">

                                <fo:block th:if="${model.body.orgSlug != null && !(#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}">
                                    <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg")'
                                                         content-width="75px"  content-height="75px"  />
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal988947', 'pal233196'}, model.body.orgSlug))}">
                                    <fo:external-graphic src='url("https://images.wexledu.com/live-worksheets/wexl-internal/20240901043131721.png")'
                                                         content-width="75px" scaling="non-uniform"  />
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug != null && (#lists.contains({'pal174599', 'pal332908', 'pal454783'}, model.body.orgSlug))}">
                                    <fo:external-graphic src='url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS.png)'
                                                         content-width="75px" scaling="non-uniform"  />
                                </fo:block>
                                <fo:block margin-left="154mm" padding-top="-29mm" th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}" >
                                    <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/30+Years+Logo+White.png")'
                                                         content-width="75px" scaling="non-uniform"  />
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block-container width="100%" height="100%" margin-top="0cm"  th:if="${model.body.orgSlug == 'dps688668'}">
                                    <fo:block border-width="1mm" font-size="10pt" space-before="5mm"
                                              font-family="Times New Roman, serif" >
                                        <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
                                            Plot No.44, 42A, BEHIND NACHARAM TELEPHONE EXCHANGE, NACHARAM,
                                        </fo:block>
                                        <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
                                            UPPAL(M),MEDCHAL DISTRICT,HYDERABAD-500076</fo:block>
                                    </fo:block>
                                </fo:block-container>
                                <fo:block font-size="8pt" font-weight="bold"  space-before="3mm" font-family="Times New Roman, serif" text-align="center" space-after="3pt"
                                          th:if="${model.body.orgSlug != 'dps688668'}">
                                    <fo:inline th:replace="report-card/dps/fragment.xml :: ${model.body.orgSlug}"></fo:inline>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${ model.body.orgSlug == 'del189476' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630520 </fo:block>
                                    <fo:block> School Code:56955 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal233196'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :130145
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt"
                                          th:if="${model.body.orgSlug != 'pal332908' and model.body.orgSlug != 'del189476' and model.body.orgSlug != 'pal233196' }"
                                          th:text="${model.header.isoData}">
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt" padding-top="4mm">
                                    QUALITATIVE COMPARATIVE ANALYSIS
                                </fo:block>
                                <fo:block font-size="9pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt" padding-top="2mm">
                                    Scholastic Assessment
                                </fo:block>
                                <fo:block font-size="9pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt" padding-top="2mm">
                                    ( PERCENTAGE ANALYSIS OF TERM-1 AND TERM-2 )
                                </fo:block>

                                <fo:block border-width="1mm" font-size="10pt" font-family="Times New Roman, serif" space-before="5mm" space-after="2pt" margin-left="-13mm">
                                    <fo:table border="none">
                                        <fo:table-column column-width="36mm" />
                                        <fo:table-column column-width="60mm" />
                                        <fo:table-column column-width="2mm"/>
                                        <fo:table-column column-width="30mm" />
                                        <fo:table-column column-width="60mm" />
                                        <fo:table-body>
                                            <fo:table-row>
                                                <fo:table-cell font-weight="bold">
                                                    <fo:block margin-bottom="2mm">Academic Year&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.header.academicYear}"></fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell font-weight="bold">
                                                    <fo:block margin-bottom="2mm">Campus &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell th:if="${model.body.orgSlug=='pal174599'}">
                                                    <fo:block  font-weight="bold" margin-bottom="2mm">Pallavi Aware International School Saroornagar</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell th:if="${model.body.orgSlug !='pal174599'}">
                                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.header.schoolName}"></fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row>
                                                <fo:table-cell font-weight="bold">
                                                    <fo:block   margin-bottom="2mm">Student Id&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.header.studentId}"></fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell font-weight="bold">
                                                    <fo:block margin-bottom="2mm">Curriculum&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${#strings.toUpperCase(model.body.boardSlug)}"></fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row>
                                                <fo:table-cell font-weight="bold">
                                                    <fo:block margin-bottom="2mm">Name of the Student&#160;&#160;:</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.name}"></fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell font-weight="bold">
                                                    <fo:block margin-bottom="2mm">Class &amp; Section&#160;&#160;&#160;:</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.className}"></fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-body>
                                    </fo:table>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>


                <fo:block text-align="center" space-before="1cm">
                    <fo:instream-foreign-object content-width="200%" content-height="400%">
                        <svg xmlns="http://www.w3.org/2000/svg"
                             th:attr="width=${(model.body.graphTableMarks.marks.size() * 120) + 50} + 'px',
                                       viewBox='0 0 ' + (${(model.body.graphTableMarks.marks.size() * 120) + 100}) + ' 400'">
                            <!-- Y-Axis -->
                            <line x1="40" y1="60" x2="40" y2="370" stroke="black" stroke-width="3"/>

                            <!-- X-Axis -->
                            <line x1="40" y1="370"
                                  th:attr="x2=${80 + (model.body.graphTableMarks.marks.size() * 120)}"
                                  y2="370" stroke="black" stroke-width="3"/>
                            <line x1="40" y1="340"
                                  th:attr="x2=${80 + (model.body.graphTableMarks.marks.size() * 120)}"
                                  y2="340" stroke="black" stroke-width="1"/>
                            <line x1="40" y1="310"
                                  th:attr="x2=${80 + (model.body.graphTableMarks.marks.size() * 120)}"
                                  y2="310" stroke="black" stroke-width="1"/>
                            <line x1="40" y1="280"
                                  th:attr="x2=${80 + (model.body.graphTableMarks.marks.size() * 120)}"
                                  y2="280" stroke="black" stroke-width="1"/>
                            <line x1="40" y1="250"
                                  th:attr="x2=${80 + (model.body.graphTableMarks.marks.size() * 120)}"
                                  y2="250" stroke="black" stroke-width="1"/>
                            <line x1="40" y1="220"
                                  th:attr="x2=${80 + (model.body.graphTableMarks.marks.size() * 120)}"
                                  y2="220" stroke="black" stroke-width="1"/>
                            <line x1="40" y1="190"
                                  th:attr="x2=${80 + (model.body.graphTableMarks.marks.size() * 120)}"
                                  y2="190" stroke="black" stroke-width="1"/>
                            <line x1="40" y1="160"
                                  th:attr="x2=${80 + (model.body.graphTableMarks.marks.size() * 120)}"
                                  y2="160" stroke="black" stroke-width="1"/>
                            <line x1="40" y1="130"
                                  th:attr="x2=${80 + (model.body.graphTableMarks.marks.size() * 120)}"
                                  y2="130" stroke="black" stroke-width="1"/>
                            <line x1="40" y1="100"
                                  th:attr="x2=${80 + (model.body.graphTableMarks.marks.size() * 120)}"
                                  y2="100" stroke="black" stroke-width="1"/>
                            <line x1="40" y1="70"
                                  th:attr="x2=${80 + (model.body.graphTableMarks.marks.size() * 120)}"
                                  y2="70" stroke="black" stroke-width="1"/>


                            <!-- Dynamically Render Bars for PT1B and PT2B -->
                            <th:block th:each="subjects, item : ${model.body.graphTableMarks.marks}">
                                <!-- Bars for TERM-1 -->
                                <rect th:attr="x=${75 + (item.index * 120)},
                                               y=${370 - ((subjects.term1TotalMarks != null ? subjects.term1TotalMarks : 0) / 100 * 300)},
                                               height=${(subjects.term1TotalMarks != null ? subjects.term1TotalMarks : 0) / 100 * 300}"
                                      width="50" fill="grey" />

                                <text th:attr="x=${100 + (item.index * 120)},
                                      y=${370 - ((subjects.term1TotalMarks != null ? subjects.term1TotalMarks : 0) / 100.0 * 300) - 5}"
                                      font-size="12"
                                      text-anchor="middle"
                                      font-weight="bold"
                                      fill="black"
                                      th:text="${subjects.term1TotalMarks != null ? #numbers.formatInteger(subjects.term1TotalMarks, 0) : 0}" />

                                <!-- Bars for TERM-2 -->
                                <rect th:attr="x=${130 + (item.index * 120)},
                                               y=${370 - ((subjects.term2TotalMarks != null ? subjects.term2TotalMarks : 0) / 100 * 300)},
                                               height=${(subjects.term2TotalMarks != null ? subjects.term2TotalMarks : 0) / 100 * 300}"
                                      width="50" fill="black" />

                                <text th:attr="x=${155 + (item.index * 120)},
                                      y=${370 - ((subjects.term2TotalMarks != null ? subjects.term2TotalMarks : 0) / 100.0 * 300) - 5}"
                                      font-size="12"
                                      text-anchor="middle"
                                      font-weight="bold"
                                      fill="black"
                                      th:text="${subjects.term2TotalMarks != null ? #numbers.formatInteger(subjects.term2TotalMarks, 0) : 0}" />

                                <!-- X-Axis Labels -->
                                <text th:attr="x=${100 + (item.index * 120)}"
                                      y="390"
                                      font-size="12"
                                      text-anchor="middle"
                                      font-weight="600"
                                      th:text="${subjects.subject}"/>
                            </th:block>
                            <!-- Y-Axis Labels -->
                            <text x="30" y="370" font-size="12" text-anchor="end">0</text>
                            <text x="30" y="340" font-size="12" text-anchor="end">10</text>
                            <text x="30" y="310" font-size="12" text-anchor="end">20</text>
                            <text x="30" y="280" font-size="12" text-anchor="end">30</text>
                            <text x="30" y="250" font-size="12" text-anchor="end">40</text>
                            <text x="30" y="220" font-size="12" text-anchor="end">50</text>
                            <text x="30" y="190" font-size="12" text-anchor="end">60</text>
                            <text x="30" y="160" font-size="12" text-anchor="end">70</text>
                            <text x="30" y="130" font-size="12" text-anchor="end">80</text>
                            <text x="30" y="100" font-size="12" text-anchor="end">90</text>
                            <text x="30" y="70"  font-size="12" text-anchor="end">100</text>
                        </svg>
                    </fo:instream-foreign-object>

                    <fo:instream-foreign-object content-width="200%" content-height="200%" text-align="center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 400 500">

                            <!-- Grouping both rectangles and texts with a common border -->
                            <g>
                                <!-- Border Rectangle -->
                                <rect x="70" y="130" width="380" height="70" fill= "none" stroke="black" stroke-width="3"/>

                                <!-- Rectangle with text PT1B inside -->
                                <rect x="90" y="150" width="30" height="30" fill="grey"/>
                                <text x="180" y="175" font-size="30" text-anchor="middle" >TERM I</text>

                                <!-- Rectangle with text PT2B inside -->
                                <rect x="280" y="150" width="30" height="30" fill="black"/>
                                <text x="380" y="175" font-size="30" text-anchor="middle">TERM II</text>
                            </g>

                        </svg>
                    </fo:instream-foreign-object>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>


