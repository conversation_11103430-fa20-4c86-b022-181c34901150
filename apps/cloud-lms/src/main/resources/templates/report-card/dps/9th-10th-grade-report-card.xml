<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="16mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" border="2pt solid black" padding="6mm">
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="300%" content-height="300%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter1">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/>
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                    <filter id="brightnessFilter2">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7.5" />
                                            <feFuncG type="linear" slope="7.5"/>
                                            <feFuncB type="linear" slope="7.5"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter1)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug == 'pal332908'}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/Pallavi-model-school_25.png"/>
                                <image filter="url(#brightnessFilter2)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug != 'pal332908'}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/DPS.svg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none">
                    <fo:table-column column-width="26mm" />
                    <fo:table-column column-width="126mm" />
                    <fo:table-body >
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block padding-top="-4mm">
                                    <fo:external-graphic th:src="${model.body.orgSlug == 'pal332908' ? 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS.png)' : 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg)'}"
                                                         content-width="75px" content-height="75px"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" padding-top="-5mm" space-after="0pt"
                                          th:if="${model.body.orgSlug == 'pal332908'}">
                                    PALLAVI INTERNATIONAL SCHOOL
                                    <fo:block font-size="14">SAGAR ROAD, HYDERABAD</fo:block>
                                </fo:block>
                                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" padding-top="-5mm" space-after="0pt"
                                          th:if="${model.body.orgSlug != 'pal332908'}"
                                          th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block>
                                <fo:block-container width="100%" height="100%" margin-top="0cm"  th:if="${model.body.orgSlug == 'dps688668' and (model.body.gradeSlug == 'ix' or model.body.gradeSlug == 'x')}">
                                    <fo:block border-width="1mm" font-size="10pt" space-before="5mm"
                                              font-family="Times New Roman, serif" padding-top="-5mm">
                                        <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
                                            Plot No.44, 42A, BEHIND NACHARAM TELEPHONE EXCHANGE, NACHARAM,
                                        </fo:block>
                                        <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
                                            UPPAL(M),MEDCHAL DISTRICT,HYDERABAD-500076</fo:block>
                                    </fo:block>
                                </fo:block-container>
                                <fo:block font-size="8pt" font-weight="bold"  space-before="3mm" font-family="Times New Roman, serif" text-align="center" space-after="3pt" padding-top="-5mm"
                                          th:if="${model.body.orgSlug != 'dps688668' and (model.body.gradeSlug == 'ix' or model.body.gradeSlug == 'x')}">
                                    <fo:inline th:replace="report-card/dps/fragment.xml :: ${model.body.orgSlug}"></fo:inline>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${ model.body.orgSlug == 'del189476' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630520 </fo:block>
                                    <fo:block> School Code:56955 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal454783'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630333 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal988947'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630095
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal233196'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :130145
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal174599'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630290
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal556078'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-before="5pt"
                                          th:if="${ model.body.orgSlug == 'del765517' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630285 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-before="5pt"
                                          th:if="${ model.body.orgSlug == 'del909850' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630448 </fo:block>
                                    <fo:block>  ISO 9001:2005, ISO 45001:2018, ISO 21001:2018 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${!(model.body.orgSlug matches 'pal556078|del765517|del909850|del189476|pal332908|pal174599|pal233196|pal988947|pal454783')}">
                                    <fo:block th:text="${model.header.affiliationData}"></fo:block>
                                    <fo:block th:text="${model.header.isoDetails}"></fo:block>
                                </fo:block>
                                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt">Record
                                    of
                                    Academic Performance
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt">
                                    <fo:inline>Session : </fo:inline>
                                    <fo:inline th:text="${model.header.academicYear}" />
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif"  space-after="2pt" padding-top="-4mm" space-before="6mm">
                    <fo:table border="none">
                        <fo:table-column column-width="33mm" />
                        <fo:table-column column-width="93mm" />
                        <fo:table-column column-width="26mm" />
                        <fo:table-column column-width="30mm" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="1mm">Name of the Student&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="1mm" th:text="${model.body.name}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="1mm">Class &amp; Section &#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" margin-bottom="1mm" th:text="${model.body.className}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block   margin-bottom="1mm">Student Id&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.header.studentId}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="1mm">Roll No&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" margin-bottom="1mm" th:text="${model.body.rollNumber}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="1mm">Mother's Name&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="1mm" th:text="${model.body.mothersName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="1mm">Date of Birth&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="1mm" th:text="${model.body.dateOfBirth}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>

                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" space-after="10pt">
                    <fo:table border="none">
                        <fo:table-column column-width="40mm" />
                        <fo:table-column column-width="85mm" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Father's/Guardian's Name&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" th:text="${model.body.fathersName}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <!-- Report Card Table -->
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" >
                    <fo:block th:text="${model.body.firstTable.title}"  font-size="9" font-weight="bold" font-family="Times New Roman, serif"></fo:block>
                    <fo:table border="1pt solid black" space-before="2pt">
                        <fo:table-column column-width="9mm"  />
                        <fo:table-column column-width="40mm" />
                        <fo:table-column column-width="50mm"  />
                        <fo:table-column column-width="35mm"  />
                        <fo:table-column column-width="25mm"  />
                        <fo:table-column column-width="18.5mm"  />

                        <fo:table-header font-size="9pt" >
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" number-rows-spanned="2" text-align="center" >
                                    <fo:block>S.NO</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left" number-rows-spanned="2" >
                                    <fo:block>SUBJECT</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row >
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block >INTERNAL ASSESSMENT (20)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block >ANNUAL EXAM (80)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block >TOTAL (100)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block >GRADE</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row th:each="marks : ${model.body.firstTable.marks}">
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.sno}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${marks.subject}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.internalAssessmentMarks}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.annualMarks}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.totalMarksScored}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.grade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding-left="10mm" padding="1mm" font-family="Times New Roman, serif"
                                               number-columns-spanned="3" text-align="left"  font-weight="bold" >
                                    <fo:block>TOTAL MARKS</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.marksTotal}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.percentage}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.grade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding-left="10mm" padding="1mm" font-family="Times New Roman, serif"
                                               number-columns-spanned="4" text-align="left"  font-weight="bold" >
                                    <fo:block>OVERALL PERCENTAGE/GRADE</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.overallPercentage}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.grade}"></fo:block>
                                </fo:table-cell>

                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <!--  2nd table starts -->
                <fo:block border-width="1mm" font-size="9pt" text-align="center"  space-before="6mm"
                          th:if="${model.body.firstTable.external != null and #lists.size(model.body.firstTable.external) > 0}">
                    <fo:block font-weight="bold" space-before="5pt" font-family="Times New Roman, serif" text-align="left">
                        ADDITIONAL SUBJECT :
                    </fo:block>


                    <fo:table border="1pt solid black" space-before="2pt">
                        <fo:table-column  column-width="9mm"  />
                        <fo:table-column column-width="56mm" />
                        <fo:table-column column-width="27mm"  />
                        <fo:table-column column-width="25mm"  />
                        <fo:table-column column-width="29mm"  />
                        <fo:table-column column-width="31.5mm"  />


                        <fo:table-body  font-family="Times New Roman, serif">
                            <fo:table-row th:each="external : ${model.body.firstTable.external}" >
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${external.sno}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" number-columns-spanned="3">
                                    <fo:block th:text="${external.subject}"></fo:block>
                                </fo:table-cell>

                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${external.totalMarksScored}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${external.grade}"></fo:block>
                                </fo:table-cell>

                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block border-width="1mm" font-size="9pt"  space-before="9" text-align="center" font-family="Times New Roman, serif"
                          th:if="${model.body.secondTable.marks != null and #lists.size(model.body.secondTable.marks) > 0}">
                    <fo:block th:text="${model.body.secondTable.title}"  font-size="9" font-weight="bold" text-align="center" font-family="Times New Roman, serif"></fo:block>

                    <fo:table border="1pt solid black" text-align="center" space-before="2pt" >
                        <fo:table-column column-width="9mm" />
                        <fo:table-column column-width="136.5mm" />
                        <fo:table-column column-width="31.5mm" />
                        <fo:table-header>
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                                    <fo:block>S.NO</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>GRADE</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row th:each="marks : ${model.body.secondTable.marks}">
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.sno}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${marks.subjectName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.termGrade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block border-width="1mm" font-size="9pt"  space-before="9" text-align="center" font-family="Times New Roman, serif"
                          th:if="${model.body.thirdTable.marks != null and #lists.size(model.body.thirdTable.marks) > 0}">
                    <fo:block th:text="${model.body.thirdTable.title}"  font-size="9" font-weight="bold" text-align="center" font-family="Times New Roman, serif"></fo:block>

                    <fo:table border="1pt solid black" text-align="center" space-before="2pt" >
                        <fo:table-column column-width="9mm" />
                        <fo:table-column column-width="136.5mm" />
                        <fo:table-column column-width="31.5mm" />
                        <fo:table-header>
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                                    <fo:block>S.NO</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>GRADE</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row th:each="marks : ${model.body.thirdTable.marks}">
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.sno}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${marks.subjectName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.termGrade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block border-width="1mm" font-size="9pt"  space-before="4" text-align="center" font-family="Times New Roman, serif"
                          th:if="${#lists.size(model.body.fourthTable) > 0 and
                          model.body.fourthTable[0].term1Grade != null && model.body.fourthTable[0].term1Grade.trim() != ''}" >
                    <fo:block  font-size="9" font-weight="bold" text-align="center" font-family="Times New Roman, serif">
                        [ On a 4-Point (A<fo:inline vertical-align="super">+</fo:inline> to C) grading scale ]
                    </fo:block>

                    <fo:table border="1pt solid black" text-align="center"  >
                        <fo:table-column column-width="85mm" />
                        <fo:table-column column-width="45.5mm" />
                        <fo:table-column column-width="45.5mm"  />
                        <fo:table-body>
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" padding-left="5mm">
                                    <fo:block th:text="${model.body.fourthTable[0].subjectName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.fourthTable[0].term1Grade}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:if="${#lists.size(model.body.fourthTable) > 1}" th:text="${model.body.fourthTable[1].term1Grade}"></fo:block>
                                    <fo:block th:unless="${#lists.size(model.body.fourthTable) > 1}"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <!-- Attendance -->
                <fo:block border-width="1mm" font-size="10pt" space-after="2pt" font-family="Times New Roman, serif" space-before="6mm">
                    <fo:block font-weight="bold" space-before="5pt" font-family="Times New Roman, serif">Attendance:</fo:block>
                    <fo:table border="none">
                        <fo:table-column column-width="32mm" />
                        <fo:table-column column-width="33mm" />
                        <fo:table-column column-width="23mm" />
                        <fo:table-column column-width="39mm" />
                        <fo:table-column column-width="30mm" />
                        <fo:table-column column-width="30mm" />

                        <fo:table-body font-family="Times New Roman, serif">
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Total working days:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.attendance.workingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Days Present:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.attendance.daysPresent}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Attendance %:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.attendance.attendancePercentage}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="10pt"  space-after="1mm">
                    <fo:table border="none">
                        <fo:table-body font-family="Times New Roman, serif">
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Remarks&#160;:</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block space-after="6mm">
                    <fo:inline>
                        <fo:block-container font-size="9pt">
                            <fo:block border-bottom="0.2mm solid black" th:text="${model.body.attendance.remarks}">
                            </fo:block>
                        </fo:block-container>
                    </fo:inline>
                </fo:block>
                <!-- Signature Block-->
                <fo:block th:if="${model.body.orgSlug} == 'dps688668'">
                    <fo:block th:replace="report-card/dps/signature.xml :: ${model.body.gradeSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'del909850'" padding-top="-2mm">
                    <fo:block th:replace="report-card/dps/signatureForMahendra.xml :: ${model.body.gradeSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'del217242'">
                    <fo:block th:replace="report-card/dps/signature.xml :: ${model.body.orgSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'del765517'">
                    <fo:block th:replace="report-card/dps/nadergulSignature.xml :: ${model.body.gradeSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'del189476'">
                    <fo:block th:replace="report-card/dps/signature.xml :: ${model.body.orgSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'pal332908'">
                    <fo:block th:replace="report-card/dps/signature.xml :: ${model.body.orgSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} != 'dps688668' and ${model.body.orgSlug} != 'del909850' and ${model.body.orgSlug} != 'del217242'
                    and ${model.body.orgSlug} != 'del765517' and ${model.body.orgSlug} != 'del189476' and ${model.body.orgSlug} != 'pal332908' ">
                    <fo:block th:replace="report-card/dps/signature.xml :: ${model.body.gradeSlug}"></fo:block>
                </fo:block>
                <!-- Report Card Result Table -->
                <fo:block border-width="1mm" font-size="10pt" space-before="1mm" font-family="Times New Roman, serif" padding-top="-3mm">
                    <fo:block th:replace="report-card/dps/fragment.xml :: ${model.body.gradingScale}"></fo:block>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>


    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.8cm" border="2pt solid black" padding="6mm">
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="300%" content-height="300%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="25"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="25"/>
                                            <feFuncB type="linear" slope="25"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter)" x="0" y="0" width="100%" height="100%" xlink:href="https://dpsgurugram84.com/wp-content/uploads/2019/07/logo_dps.png"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>

                <!-- Header Section -->
                <fo:table border="none">
                    <fo:table-column column-width="26mm" />
                    <fo:table-column column-width="126mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell >
                                <fo:block padding-top="-4mm">
                                    <fo:external-graphic th:src="${model.body.orgSlug == 'pal332908' ? 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS.png)' : 'url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg)'}"
                                                         content-width="75px" content-height="75px"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>

                                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" padding-top="-2mm" space-after="6pt"
                                          th:if="${model.body.orgSlug == 'pal332908'}">
                                    PALLAVI INTERNATIONAL SCHOOL
                                    <fo:block font-size="14">SAGAR ROAD, HYDERABAD</fo:block>
                                </fo:block>
                                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" padding-top="-2mm" space-after="6pt"
                                          th:if="${model.body.orgSlug != 'pal332908'}"
                                          th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block>
                                <fo:block-container width="100%" height="100%" margin-top="0cm"  th:if="${model.body.orgSlug == 'dps688668' and (model.body.gradeSlug == 'ix' or model.body.gradeSlug == 'x')}">
                                    <fo:block border-width="1mm" font-size="10pt" space-before="5mm"
                                              font-family="Times New Roman, serif" padding-top="-5mm">
                                        <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
                                            Plot No.44, 42A, BEHIND NACHARAM TELEPHONE EXCHANGE, NACHARAM,
                                        </fo:block>
                                        <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
                                            UPPAL(M),MEDCHAL DISTRICT,HYDERABAD-500076</fo:block>
                                    </fo:block>
                                </fo:block-container>
                                <fo:block font-size="8pt" font-weight="bold"  space-before="3mm" font-family="Times New Roman, serif" text-align="center" space-after="3pt" padding-top="-5mm"
                                          th:if="${model.body.orgSlug != 'dps688668' and (model.body.gradeSlug == 'ix' or model.body.gradeSlug == 'x')}">
                                    <fo:inline th:replace="report-card/dps/fragment.xml :: ${model.body.orgSlug}"></fo:inline>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${ model.body.orgSlug == 'del189476' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630520 </fo:block>
                                    <fo:block> School Code:56955 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal454783'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630333 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal988947'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630095
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal233196'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :130145
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal174599'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630290
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal556078'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-before="5pt"
                                          th:if="${ model.body.orgSlug == 'del765517' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630285 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-before="5pt"
                                          th:if="${ model.body.orgSlug == 'del909850' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630448 </fo:block>
                                    <fo:block>  ISO 9001:2005, ISO 45001:2018, ISO 21001:2018 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${!(model.body.orgSlug matches 'pal556078|del765517|del909850|del189476|pal332908|pal174599|pal233196|pal988947|pal454783')}">
                                    <fo:block th:text="${model.header.affiliationData}"></fo:block>
                                    <fo:block th:text="${model.header.isoDetails}"></fo:block>
                                </fo:block>

                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt" padding-top="4mm">
                                    QUALITATIVE COMPARATIVE ANALYSIS
                                </fo:block>
                                <fo:block font-size="9pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt" padding-top="2mm">
                                    Scholastic Assessment
                                </fo:block>
                                <fo:block font-size="9pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt" padding-top="2mm">
                                    ( PERCENTAGE ANALYSIS OF PT-1B,PT-2B,PT-3 AND AE)
                                </fo:block>

                                <fo:block border-width="1mm" font-size="10pt" font-family="Times New Roman, serif" space-before="5mm" space-after="2pt" margin-left="-11mm">
                                    <fo:table border="none">
                                        <fo:table-column column-width="36mm" />
                                        <fo:table-column column-width="56mm" />
                                        <fo:table-column column-width="1mm" />
                                        <fo:table-column column-width="30mm" />
                                        <fo:table-column column-width="57mm" />
                                        <fo:table-body>
                                            <fo:table-row>
                                                <fo:table-cell font-weight="bold">
                                                    <fo:block margin-bottom="2mm">Academic Year&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.header.academicYear}"></fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell font-weight="bold">
                                                    <fo:block margin-bottom="2mm">Campus &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.header.schoolName}"></fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row>
                                                <fo:table-cell font-weight="bold">
                                                    <fo:block   margin-bottom="2mm">Student Id&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.header.studentId}"></fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell font-weight="bold">
                                                    <fo:block margin-bottom="2mm">Curriculum&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${#strings.toUpperCase(model.body.boardSlug)}"></fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row>
                                                <fo:table-cell font-weight="bold">
                                                    <fo:block margin-bottom="2mm">Name of the Student&#160;&#160;:</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.name}"></fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell font-weight="bold">
                                                    <fo:block margin-bottom="2mm">Class &amp; Section&#160;&#160;&#160;:</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.className}"></fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-body>
                                    </fo:table>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block margin-left="-15mm" text-align="center" space-before="1cm">
                    <fo:instream-foreign-object content-width="200%" content-height="400%">
                        <svg xmlns="http://www.w3.org/2000/svg"
                             th:attr="width=${(model.body.firstTable.marks.size() * 120) + 50} + 'px',
                                       viewBox='0 0 ' + (${(model.body.firstTable.marks.size() * 120) + 100}) + ' 400'">
                            <!-- Y-Axis -->
                            <line x1="40" y1="60" x2="40" y2="370" stroke="black" stroke-width="4"/>

                            <!-- X-Axis -->
                            <line x1="40" y1="370"
                                  th:attr="x2=${80 + (model.body.firstTable.marks.size() * 140)}"
                                  y2="370" stroke="black" stroke-width="3"/>
                            <line x1="40" y1="340"
                                  th:attr="x2=${80 + (model.body.firstTable.marks.size() * 140)}"
                                  y2="340" stroke="black" stroke-width="1"/>
                            <line x1="40" y1="310"
                                  th:attr="x2=${80 + (model.body.firstTable.marks.size() * 140)}"
                                  y2="310" stroke="black" stroke-width="1"/>
                            <line x1="40" y1="280"
                                  th:attr="x2=${80 + (model.body.firstTable.marks.size() * 140)}"
                                  y2="280" stroke="black" stroke-width="1"/>
                            <line x1="40" y1="250"
                                  th:attr="x2=${80 + (model.body.firstTable.marks.size() * 140)}"
                                  y2="250" stroke="black" stroke-width="1"/>
                            <line x1="40" y1="220"
                                  th:attr="x2=${80 + (model.body.firstTable.marks.size() * 140)}"
                                  y2="220" stroke="black" stroke-width="1"/>
                            <line x1="40" y1="190"
                                  th:attr="x2=${80 + (model.body.firstTable.marks.size() * 140)}"
                                  y2="190" stroke="black" stroke-width="1"/>
                            <line x1="40" y1="160"
                                  th:attr="x2=${80 + (model.body.firstTable.marks.size() * 140)}"
                                  y2="160" stroke="black" stroke-width="1"/>
                            <line x1="40" y1="130"
                                  th:attr="x2=${80 + (model.body.firstTable.marks.size() * 140)}"
                                  y2="130" stroke="black" stroke-width="1"/>
                            <line x1="40" y1="100"
                                  th:attr="x2=${80 + (model.body.firstTable.marks.size() * 140)}"
                                  y2="100" stroke="black" stroke-width="1"/>
                            <line x1="40" y1="70"
                                  th:attr="x2=${80 + (model.body.firstTable.marks.size() * 140)}"
                                  y2="70" stroke="black" stroke-width="1"/>


                            <!-- Dynamically Render Bars for PT1B and PT2B -->
                            <th:block th:each="subjects, item : ${model.body.firstTable.marks}">
                                <!-- Bars for PT1B -->
                                <rect th:attr="x=${75 + (item.index * 140)},
                                      y=${370 - (subjects.pt1 / 100.0 * 300)},
                                      height=${subjects.pt1 / 100.0 * 300}"
                                      width="30" fill="#A9A9A9" />  <!-- Medium Grey -->
                                <text th:attr="x=${75 + (item.index * 140) + 15},
                                      y=${370 - (subjects.pt1 / 100.0 * 300) - 5}"
                                      font-size="12"
                                      text-anchor="middle"
                                      font-weight="bold"
                                      fill="black"
                                      th:text="${subjects.pt1}" />

                                <!-- Bars for PT2B -->
                                <rect th:attr="x=${105 + (item.index * 140)},
                                      y=${370 - (subjects.pt2 / 100.0 * 300)},
                                      height=${subjects.pt2 / 100.0 * 300}"
                                      width="30" fill="black" />
                                <text th:attr="x=${105 + (item.index * 140) + 15},
                                      y=${370 - (subjects.pt2 / 100.0 * 300) - 5}"
                                      font-size="12"
                                      text-anchor="middle"
                                      font-weight="bold"
                                      fill="black"
                                      th:text="${subjects.pt2}" />

                                <!-- Bars for PT3 -->
                                <rect th:attr="x=${135 + (item.index * 140)},
                                      y=${370 - (subjects.pt3 / 100.0 * 300)},
                                      height=${subjects.pt3 / 100.0 * 300}"
                                      width="30" fill="grey" />

                                <text th:attr="x=${135 + (item.index * 140) + 15},
                                      y=${370 - (subjects.pt3 / 100.0 * 300) - 5}"
                                      font-size="12"
                                      text-anchor="middle"
                                      font-weight="bold"
                                      fill="black"
                                      th:text="${subjects.pt3}" />

                                <!-- Bars for AE -->
                                <rect th:attr="x=${165 + (item.index * 140)},
                                      y=${370 - (subjects.annualMarksInPercentage / 100.0 * 300)},
                                      height=${subjects.annualMarksInPercentage / 100.0 * 300}"
                                      width="30" fill="#696969" />  <!-- Dark Grey -->

                                <text th:attr="x=${165 + (item.index * 140) + 15},
                                      y=${370 - (subjects.annualMarksInPercentage / 100.0 * 300) - 5}"
                                      font-size="12"
                                      text-anchor="middle"
                                      font-weight="bold"
                                      fill="black"
                                      th:text="${subjects.annualMarksInPercentage}" />


                                X-Axis Labels
                                <text th:attr="x=${75 + (item.index * 135) + 25}"
                                      y="390" font-size="12"
                                      text-align="center" font-weight="600"
                                      th:text="${subjects.subject}"/>
                            </th:block>
                            <!-- Y-Axis Labels -->
                            <text x="30" y="370" font-size="12" text-anchor="end">0</text>
                            <text x="30" y="340" font-size="12" text-anchor="end">10</text>
                            <text x="30" y="310" font-size="12" text-anchor="end">20</text>
                            <text x="30" y="280" font-size="12" text-anchor="end">30</text>
                            <text x="30" y="250" font-size="12" text-anchor="end">40</text>
                            <text x="30" y="220" font-size="12" text-anchor="end">50</text>
                            <text x="30" y="190" font-size="12" text-anchor="end">60</text>
                            <text x="30" y="160" font-size="12" text-anchor="end">70</text>
                            <text x="30" y="130" font-size="12" text-anchor="end">80</text>
                            <text x="30" y="100" font-size="12" text-anchor="end">90</text>
                            <text x="30" y="70"  font-size="12" text-anchor="end">100</text>
                        </svg>
                    </fo:instream-foreign-object>

                    <fo:instream-foreign-object padding-left="5mm" content-width="200%" content-height="200%" text-align="center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 440 500">

                            <!-- Grouping both rectangles and texts with a common border -->
                            <g>
                                <!-- Border Rectangle -->
                                <rect x="70" y="130" width="400" height="70" fill="none" stroke="black" stroke-width="3" />

                                <!-- Rectangle with text PT1B inside -->
                                <rect x="85" y="150" width="30" height="30" fill="#A9A9A9" />
                                <text x="150" y="170" font-size="14" text-anchor="middle" font-weight="bold">PT1B</text>

                                <!-- Rectangle with text PT2B inside -->
                                <rect x="185" y="150" width="30" height="30" fill="black" />
                                <text x="240" y="170" font-size="14" text-anchor="middle" font-weight="bold">PT2B</text>

                                <!-- Rectangle with text PT3 inside -->
                                <rect x="275" y="150" width="30" height="30" fill="grey" />
                                <text x="325" y="170" font-size="14" text-anchor="middle" font-weight="bold">PT3</text>

                                <!-- Rectangle with text AE inside -->
                                <rect x="360" y="150" width="30" height="30" fill="#696969" />
                                <text x="410" y="170" font-size="14" text-anchor="middle" font-weight="bold">AE</text>
                            </g>

                        </svg>
                    </fo:instream-foreign-object>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>


