<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="10mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="0cm">
                <fo:table border="none">
                    <fo:table-column column-width="32.4mm" />
                    <fo:table-column column-width="125mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block>
                                    <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg")'
                                                         content-width="75px"  content-height="75px"  />
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="2mm">
                                <fo:block font-size="15pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center" space-after="2pt"
                                          th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block >
                                <fo:block font-size="14pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center" space-after="4pt" >
                                    Cambridge International – Early Years
                                </fo:block>
                                <fo:block  th:replace="report-card/dps/fragment.xml :: ${model.body.orgSlug}"></fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block border="0.3mm solid black" space-after="5" space-before="3mm"></fo:block>
                <fo:block border-width="1mm" font-size="9pt" font-family="Arial, sans-serif" space-after="5pt">
                    <fo:block font-size="11pt" font-weight="bold" font-family="Arial, sans-serif" space-after="5pt" text-align="center">
                        STUDENT E - ADMIT CARD
                    </fo:block>
                    <fo:table border="none">
                        <fo:table-column column-width="32mm"/>
                        <fo:table-column column-width="2mm"/>
                        <fo:table-column column-width="60mm"/>
                        <fo:table-column column-width="27mm"/>
                        <fo:table-column column-width="2mm"/>
                        <fo:table-column column-width="65mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding="2mm 0">
                                    <fo:block>Class Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">
                                        <fo:inline th:text="${model.body.sectionName}"></fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding="2mm 0">
                                    <fo:block>Section Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">
                                        <fo:inline th:text="${model.body.className}"></fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding="2mm 0">
                                    <fo:block>Student ID</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">
                                        <fo:inline th:text="${model.body.admissionNumber}"></fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding="2mm 0">
                                    <fo:block>Roll No.</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">
                                        <fo:inline th:text="${model.body.rollNumber}"></fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding="2mm 0">
                                    <fo:block>Student Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">
                                        <fo:inline th:text="${model.body.name}"></fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding="2mm 0">
                                    <fo:block>Father's Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0" >
                                    <fo:block font-weight="bold">
                                        <fo:inline th:text="${model.body.fathersName}" ></fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block space-after="2pt">
                    <fo:table border="none" font-size="9pt">
                        <fo:table-column column-width="32mm"/>
                        <fo:table-column column-width="2mm"/>
                        <fo:table-column column-width="auto"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding="0.5mm 0">
                                    <fo:block>Examination Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="0.5mm 0">
                                    <fo:block font-weight="bold">:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="0.5mm 0">
                                    <fo:block font-weight="bold">
                                        <fo:inline th:text="${model.body.examinationName}"></fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block border-width="1mm" font-size="9pt" space-before="4" text-align="center"
                          font-family="Arial, sans-serif" space-after="15mm">
                    <fo:table border="1pt solid black" text-align="center">
                        <fo:table-column column-width="39mm" />
                        <fo:table-column column-width="70mm" />
                        <fo:table-column column-width="40mm" />
                        <fo:table-column column-width="40mm" />
                        <fo:table-header>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold"
                                               padding="1mm" >
                                    <fo:block>Date</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold"
                                               padding="1mm">
                                    <fo:block>Subject Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold"
                                               padding="1mm">
                                    <fo:block>Start Time</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold"
                                               padding="1mm">
                                    <fo:block>End Time</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body font-size="9pt">
                            <fo:table-row
                                    font-weight="bold"
                                    text-align="center">
                                <fo:table-cell padding="10mm" border-right="1pt solid black" text-align="center">
                                    <fo:block th:text="${model.body.date}" font-size="9pt" font-weight="bold"></fo:block>
                                </fo:table-cell>

                                <fo:table-cell padding="0.5mm" border-right="1pt solid black">
                                    <fo:block th:each="mark : ${model.body.firstTable.marks}" padding="0.5mm">
                                        <fo:block th:each="subject : ${mark.subjects}" padding="0.5mm">
                                            <fo:block font-size="7pt" th:text="${subject.subjectNames}"></fo:block>
                                        </fo:block>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="10mm"
                                               number-columns-spanned="2" font-size="9pt" font-weight="bold">
                                    <fo:block>During School Hours
                                    </fo:block>
                                    <fo:block>8:00 am to 12:15 pm
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block th:if="${model.body.orgSlug == 'dps688668'}">
                    <fo:block border-width="1mm" font-size="9pt"
                              font-family="Arial, sans-serif">
                        <fo:table>
                            <fo:table-column column-width="80mm"/>
                            <fo:table-column column-width="80mm"/>
                            <fo:table-column column-width="75mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell font-weight="bold">
                                        <fo:block>Principal's Sign</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  font-weight="bold">
                                        <fo:block>Headmistress' Sign</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell font-weight="bold">
                                        <fo:block>Teacher's Sign</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                    <fo:block border="0.3mm solid black" space-after="5" space-before="2"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug != 'dps688668'}">
                <fo:block border-width="1mm" font-size="9pt"
                          font-family="Arial, sans-serif">
                    <fo:table>
                        <fo:table-column />
                        <fo:table-column />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell padding="0 2mm" font-weight="bold">
                                    <fo:block>Teacher Sign</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-left="70mm" font-weight="bold">
                                    <fo:block>Principal Sign</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border="0.3mm solid black" space-after="5" space-before="2"></fo:block>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>