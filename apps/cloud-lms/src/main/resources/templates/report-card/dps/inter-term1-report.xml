<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="18mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" border="2pt solid black" padding="6mm">
                <fo:block margin-top="1" font-weight="bold" color="#2730fe" text-align="right" padding-top="-11mm" padding-after="6mm">Student Id:
                    <fo:inline th:text="${model.header.studentId}"/></fo:block>
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="300%" content-height="300%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter1">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/>
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                    <filter id="brightnessFilter2">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="8.5" />
                                            <feFuncG type="linear" slope="8.5"/>
                                            <feFuncB type="linear" slope="8.5"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter1)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug != null && (#lists.contains({'pal174599', 'pal332908', 'pal454783'}, model.body.orgSlug))}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/Pallavi-model-school_25.png"/>
                                <image filter="url(#brightnessFilter1)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal988947', 'pal233196'}, model.body.orgSlug))}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/Pallavi-Model-School-Favicon_5.png"/>
                                <image filter="url(#brightnessFilter2)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug != null && !(#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/DPS.svg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none">
                    <fo:table-column column-width="26mm" />
                    <fo:table-column column-width="120mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block th:if="${model.body.orgSlug != null && !(#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}">
                                    <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg")'
                                                         content-width="75px"  content-height="75px"  />
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal988947', 'pal233196'}, model.body.orgSlug))}">
                                    <fo:external-graphic src='url("https://images.wexledu.com/live-worksheets/wexl-internal/20240901043131721.png")'
                                                         content-width="75px" scaling="non-uniform"  />
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug != null && (#lists.contains({'pal174599', 'pal332908', 'pal454783'}, model.body.orgSlug))}">
                                    <fo:external-graphic src='url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS.png)'
                                                         content-width="75px" scaling="non-uniform"  />
                                </fo:block>
                                <fo:block margin-left="150mm" padding-top="-28mm" th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}" >
                                    <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/30+Years+Logo+White.png")'
                                                         content-width="75px" scaling="non-uniform"  />
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="0pt"
                                          th:if="${model.body.orgSlug == 'pal454783'}">
                                    PALLAVI INTERNATIONAL SCHOOL
                                    <fo:block font-size="18">GANDIPET</fo:block>
                                </fo:block>
                                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="0pt"
                                          th:if="${model.body.orgSlug != 'pal454783'}"
                                          th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block>
                                <fo:block-container width="100%" height="100%" margin-top="0cm"  th:if="${model.body.orgSlug == 'dps688668'}" space-after="4mm">
                                    <fo:block border-width="1mm" font-size="10pt" space-before="5mm"
                                              font-family="Times New Roman, serif" padding-top="-5mm">
                                        <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
                                            Plot No.44, 42A, BEHIND NACHARAM TELEPHONE EXCHANGE, NACHARAM,
                                        </fo:block>
                                        <fo:block font-size="8pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center">
                                            UPPAL(M),MEDCHAL DISTRICT,HYDERABAD-500076</fo:block>
                                    </fo:block>
                                </fo:block-container>
                                <fo:block font-size="8pt" font-weight="bold"  space-before="3mm" font-family="Times New Roman, serif" text-align="center" space-after="4pt" padding-top="-3mm"
                                          th:if="${model.body.orgSlug != 'dps688668'}">
                                    <fo:inline th:replace="report-card/dps/fragment.xml :: ${model.body.orgSlug}"></fo:inline>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${ model.body.orgSlug == 'del189476' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630520 </fo:block>
                                    <fo:block> School Code:56955 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal454783'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630333 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal988947'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630095
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal233196'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :130145
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal174599'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630290
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal556078'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-before="5pt"
                                          th:if="${ model.body.orgSlug == 'del765517' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630285 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-before="5pt"
                                          th:if="${ model.body.orgSlug == 'del909850' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630448 </fo:block>
                                    <fo:block>  ISO 9001:2005, ISO 45001:2018, ISO 21001:2018 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${!(model.body.orgSlug matches 'pal556078|del765517|del909850|del189476|pal332908|pal174599|pal233196|pal988947|pal454783')}">
                                    <fo:block th:text="${model.header.isoData}"></fo:block>
                                </fo:block>
                                <fo:block font-size="13pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:text="${model.body.gradePerformanceText}">
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:text="${model.header.academicYear}">
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="20pt"
                                          th:if="${model.body.termSlug} == 't1'">
                                    TERM-I
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="18mm"
                                          th:if="${model.body.termSlug} != 't1'">
                                    FINAL EXAM RESULT
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" space-after="3pt" space-before="3mm"
                          th:padding-top="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug)) ? '-14mm' : '0mm'}" >
                    <fo:table border="none">
                        <fo:table-column column-width="33mm" />
                        <fo:table-column column-width="91mm" />
                        <fo:table-column column-width="26mm" />
                        <fo:table-column column-width="30mm" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Name of the Student&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.name}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Class &amp; Section &#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.body.className}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block   margin-bottom="2mm">Student Id&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.header.studentId}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Roll No&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" margin-bottom="2mm" th:text="${model.body.rollNumber}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Mother's Name&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.mothersName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Date of Birth&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.dateOfBirth}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" space-after="17pt">
                    <fo:table border="none">
                        <fo:table-column column-width="40mm" />
                        <fo:table-column column-width="85mm" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Father's/Guardian's Name&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block  font-weight="bold" th:text="${model.body.fathersName}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <!-- first Table -->
                <fo:block border-width="1mm" font-size="9pt"  text-align="center" font-family="Times New Roman, serif" >
                    <fo:block th:text="${model.body.firstTable.title}"  font-size="9" font-weight="bold" font-family="Times New Roman, serif"></fo:block>

                    <fo:table border="1pt solid black" text-align="center"  >
                        <fo:table-column column-width="60mm" />
                        <fo:table-column column-width="23mm" />
                        <fo:table-column column-width="23mm" />
                        <fo:table-column column-width="33mm" />
                        <fo:table-column column-width="33mm"  />
                        <fo:table-header>
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="4mm" text-align="left" number-rows-spanned="2">
                                    <fo:block>SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  number-columns-spanned="2">
                                    <fo:block th:if="${model.body.termSlug} == 't1'">TERM-I</fo:block>
                                    <fo:block th:if="${model.body.termSlug} != 't1'">FINAL RESULT</fo:block>
                                </fo:table-cell>

                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="4mm" number-rows-spanned="2" text-align="center">
                                    <fo:block>TOTAL (100)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="4mm" number-rows-spanned="2" text-align="center" >
                                    <fo:block >HIGHEST MARKS</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" >
                                    <fo:block>THEORY</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>PRACTICAL</fo:block>
                                </fo:table-cell>

                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row th:each="marks : ${model.body.firstTable.marks}">
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-left="4mm" text-align="left">
                                    <fo:block th:text="${marks.subject}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.theoryMarks}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.practicalMarks}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.total}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.highestMarks}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding-left="10mm" padding="1mm" font-family="Times New Roman, serif"
                                               number-columns-spanned="3" text-align="left"  font-weight="bold" >
                                    <fo:block>Total</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.total}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding-left="10mm" padding="1mm" font-family="Times New Roman, serif"
                                               number-columns-spanned="3" text-align="left"  font-weight="bold" >
                                    <fo:block>Overall Percentage</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.overallPercentage}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <!-- Fourth Table -->
                <fo:block border-width="1mm" font-size="9pt"  space-before="20" text-align="center" font-family="Times New Roman, serif"
                          th:if="${model.body.fourthTable.marks != null and #lists.size(model.body.fourthTable.marks) > 0}">
                    <fo:block th:text="${model.body.orgSlug == 'pal988947' ? 'SKILL SUBJECT' : model.body.fourthTable.title }"
                              font-size="9" text-align="left" font-weight="bold"  font-family="Times New Roman, serif" space-after="1mm"></fo:block>

                    <fo:table border="1pt solid black" text-align="center"  >
                        <fo:table-column column-width="60mm" />
                        <fo:table-column column-width="23mm" />
                        <fo:table-column column-width="23mm" />
                        <fo:table-column column-width="33mm" />
                        <fo:table-column column-width="33mm"  />

                        <fo:table-body>
                            <fo:table-row th:each="marks : ${model.body.fourthTable.marks}">
                                <fo:table-cell border="1pt solid black" padding="1mm" padding-left="4mm" text-align="left">
                                    <fo:block th:text="${marks.subject}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.theoryMarks}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.practicalMarks}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.total}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.highestMarks}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>



                <!-- second Table -->
                <fo:block border-width="1mm" font-size="9pt"  space-before="20" text-align="center" font-family="Times New Roman, serif"
                          th:if="${model.body.secondTable.marks != null and #lists.size(model.body.secondTable.marks) > 0}">
                    <fo:block th:text="${model.body.secondTable.title}"  font-size="9" text-align="left" font-weight="bold"  font-family="Times New Roman, serif" space-after="1mm"></fo:block>

                    <fo:table border="1pt solid black" text-align="center"  >
                        <fo:table-column column-width="102mm" />
                        <fo:table-column column-width="70mm" />

                        <fo:table-body>
                            <fo:table-row th:each="marks : ${model.body.secondTable.marks}">
                                <fo:table-cell border="1pt solid black" padding-left="4mm" padding="1mm" text-align="left">
                                    <fo:block th:text="${marks.subjectName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.term1Grade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <!-- Attendance -->
                <fo:block border-width="1mm" font-size="10pt" space-after="22pt" font-family="Times New Roman, serif" space-before="6mm">
                    <fo:block font-weight="bold" space-after="5pt" font-family="Times New Roman, serif">Attendance&#160;:</fo:block>
                    <fo:table border="none">
                        <fo:table-column column-width="35mm" />
                        <fo:table-column column-width="33mm" />
                        <fo:table-column column-width="23mm" />
                        <fo:table-column column-width="39mm" />
                        <fo:table-column column-width="25mm" />
                        <fo:table-column column-width="30mm" />
                        <fo:table-body font-family="Times New Roman, serif" >
                            <fo:table-row space-after="5pt">
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Total Working Days&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.attendance.workingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Days Present&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.attendance.daysPresent}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Attendance %:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.attendance.attendancePercentage}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <!--remarks-->
                <fo:block border-width="1mm" font-size="10pt" space-after="60pt">
                    <fo:block font-weight="bold" font-family="Times New Roman, serif" >Remarks :
                        <fo:inline font-weight="normal" th:text="${model.body.attendance.remarks}"/>
                    </fo:block>
                </fo:block >

                <fo:block th:if="${model.body.orgSlug == 'pal454783'}">
                    <fo:block border-width="1mm" font-size="10pt" font-family="Times New Roman, serif" space-before="10mm" padding-top="-20mm">
                        <fo:table border="none">
                            <fo:table-column column-width="45mm" />
                            <fo:table-column column-width="60mm" />
                            <fo:table-column column-width="95mm" />
                            <fo:table-body font-family="Times New Roman, serif">
                                <fo:table-row>
                                    <fo:table-cell>
                                        <fo:block padding-top="-7mm"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block padding-top="-7mm"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block>
                                            <fo:block-container padding-top="-5mm" width="100mm" height="35mm" display-align="center" text-align="center">
                                                <fo:block>
                                                    <fo:external-graphic src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/signature.png')"
                                                                         scaling="non-uniform"
                                                                         content-width="35mm"
                                                                         content-height="15mm" />
                                                </fo:block>
                                            </fo:block-container>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>

                                <fo:table-row >
                                    <fo:table-cell padding-top="-12mm" text-align="left" font-weight="bold">
                                        <fo:block>Class Teacher Signature  </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block padding-top="-12mm" > </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-left="12mm" padding-top="-12mm"  text-align="center" font-weight="bold">
                                        <fo:block>Principal</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>

                <fo:block th:if="${(model.body.orgSlug matches 'pal174599|pal233196|pal556078')}">
                    <fo:block border-width="1mm" font-size="10pt" space-before="20mm" space-after="6mm" font-family="Times New Roman, serif">
                        <fo:table border="none">
                            <fo:table-column column-width="50mm" />
                            <fo:table-column column-width="210mm" />
                            <fo:table-body font-family="Times New Roman, serif">
                                <fo:table-row >
                                    <fo:table-cell text-align="left" font-weight="bold">
                                        <fo:block>Class Teacher</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="center" font-weight="bold">
                                        <fo:block>Principal</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>

                <fo:block th:if="${(model.body.orgSlug matches 'pal332908')}">
                    <fo:block border-width="1mm" font-size="10pt" space-before="20mm" space-after="6mm" font-family="Times New Roman, serif">
                        <fo:table border="none">
                            <fo:table-column column-width="50mm" />
                            <fo:table-column column-width="210mm" />
                            <fo:table-body font-family="Times New Roman, serif">
                                <fo:table-row >
                                    <fo:table-cell text-align="left" font-weight="bold">
                                        <fo:block>Principal / Headmaster</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="center" font-weight="bold">
                                        <fo:block>Class Teacher</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>

                <!-- Signature Block-->
                <fo:block th:if="${model.body.orgSlug} == 'dps688668'" space-after="7mm">
                    <fo:block th:replace="report-card/dps/signature.xml :: ${model.body.gradeSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'del909850'" space-after="7mm">
                    <fo:block th:replace="report-card/dps/signatureForMahendra.xml :: ${model.body.gradeSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'del217242'" space-after="7mm">
                    <fo:block th:replace="report-card/dps/signature.xml :: ${model.body.orgSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'del765517'" space-after="7mm">
                    <fo:block th:replace="report-card/dps/nadergulSignature.xml :: ${model.body.gradeSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug} == 'del189476'" space-after="7mm">
                    <fo:block th:replace="report-card/dps/signature.xml :: ${model.body.orgSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${!(model.body.orgSlug matches 'dps688668|del909850|del217242|del765517|del189476|pal556078|pal454783|pal174599|pal332908|pal988947|pal233196')}">
                    <fo:block th:replace="report-card/dps/signature.xml :: ${model.body.gradeSlug}"></fo:block>
                </fo:block>
                <fo:block th:if="${(model.body.orgSlug matches 'pal988947')}">
                    <fo:block th:replace="report-card/dps/pallavi-signature.xml :: ${model.body.gradeSlug}"></fo:block>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>


    <fo:page-sequence master-reference="invoice" th:if="${model.body.thirdTable.marks != null and
                         (model.body.thirdTable.marks.subTable1 != null or
                          model.body.thirdTable.marks.subTable2 != null or
                          model.body.thirdTable.marks.subTable3 != null)}">
    <fo:flow flow-name="xsl-region-body" >
    <fo:block-container width="100%" height="100%" margin-top="-0.5cm" border="2pt solid black" padding="6mm">
        <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%">
            <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                <fo:instream-foreign-object content-width="300%" content-height="300%">
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                        <defs>
                            <filter id="brightnessFilter1">
                                <feComponentTransfer>
                                    <feFuncR type="linear" slope="7"/>
                                    <feFuncG type="linear" slope="7"/>
                                    <feFuncB type="linear" slope="7"/>
                                </feComponentTransfer>
                            </filter>
                            <filter id="brightnessFilter2">
                                <feComponentTransfer>
                                    <feFuncR type="linear" slope="8.5" />
                                    <feFuncG type="linear" slope="8.5"/>
                                    <feFuncB type="linear" slope="8.5"/>
                                </feComponentTransfer>
                            </filter>
                        </defs>
                        <image filter="url(#brightnessFilter1)"
                               x="0" y="0"
                               width="100%" height="100%"
                               th:if="${model.body.orgSlug != null && (#lists.contains({'pal174599', 'pal332908', 'pal454783'}, model.body.orgSlug))}"
                               xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/Pallavi-model-school_25.png"/>
                        <image filter="url(#brightnessFilter1)"
                               x="0" y="0"
                               width="100%" height="100%"
                               th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal988947', 'pal233196'}, model.body.orgSlug))}"
                               xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/Pallavi-Model-School-Favicon_5.png"/>
                        <image filter="url(#brightnessFilter2)"
                               x="0" y="0"
                               width="100%" height="100%"
                               th:if="${model.body.orgSlug != null && !(#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}"
                               xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/DPS.svg"/>
                    </svg>
                </fo:instream-foreign-object>
            </fo:block>
        </fo:block-container>
        <!-- third Table -->
        <fo:block border-width="1mm" font-size="9pt"  space-before="10" text-align="center" font-family="Times New Roman, serif"
                  th:if="${model.body.thirdTable.title != null}">
            <fo:block th:text="${model.body.thirdTable.title}"  font-size="9" font-weight="bold"  font-family="Times New Roman, serif" space-after="1mm"></fo:block>
            <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="3pt">
                (to be assessed on a 5 point scale once in a session)
            </fo:block>
            <fo:table border="1pt solid black" text-align="center"
                      th:if="${(model.body.thirdTable.marks.subTable1 != null && model.body.thirdTable.marks.subTable1.size() > 0)
                      or (model.body.thirdTable.marks.subTable2 != null && model.body.thirdTable.marks.subTable2.size() > 0)
                      or (model.body.thirdTable.marks.subTable3 != null && model.body.thirdTable.marks.subTable3.size() > 0)}" >
                <fo:table-column column-width="39mm" />
                <fo:table-column column-width="120mm" />
                <fo:table-column column-width="13mm" />

                <fo:table-body>
                    <fo:table-row font-weight="bold" th:if="${model.body.thirdTable.marks.subTable1 != null && model.body.thirdTable.marks.subTable1.size() > 0}">
                        <fo:table-cell border="1pt solid black" padding="1mm">
                            <fo:block>LIFE SKILLS</fo:block>
                        </fo:table-cell >
                        <fo:table-cell border="1pt solid black" padding="1mm">
                            <fo:block>DESCRIPTIVE INDICATOR</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" padding="1mm">
                            <fo:block>GRADE</fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                    <fo:table-row th:each="subTable1 : ${model.body.thirdTable.marks.subTable1}"
                                  th:if="${model.body.thirdTable.marks.subTable1 != null && model.body.thirdTable.marks.subTable1.size() > 0}">
                        <fo:table-cell border="1pt solid black" padding-left="1mm" padding="1mm" text-align="left" display-align="center">
                            <fo:block th:text="${subTable1.subjectName}"></fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" padding="1mm">
                            <fo:block font-size="10pt" text-align="left" th:text="${subTable1.descriptive}"></fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" padding="1mm" text-align="center" display-align="center">
                            <fo:block th:text="${subTable1.term1Grade}"></fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                    <fo:table-row font-weight="bold" th:if="${model.body.thirdTable.marks.subTable2 != null && model.body.thirdTable.marks.subTable2.size() > 0}">
                        <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" display-align="center">
                            <fo:block>HEALTH AND PHYSICAL EDUCATION</fo:block>
                        </fo:table-cell >
                        <fo:table-cell border="1pt solid black" padding="1mm">
                            <fo:block>DESCRIPTIVE INDICATOR</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" padding="1mm">
                            <fo:block>GRADE</fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                    <fo:table-row th:each="subTable2 : ${model.body.thirdTable.marks.subTable2}"
                                  th:if="${model.body.thirdTable.marks.subTable2 != null && model.body.thirdTable.marks.subTable2.size() > 0}">
                        <fo:table-cell border="1pt solid black" padding-left="1mm" padding="1mm" text-align="left" display-align="center">
                            <fo:block th:text="${subTable2.subjectName}"></fo:block>
                        </fo:table-cell>
                        <fo:table-cell font-size="10pt" border="1pt solid black" padding="1mm">
                            <fo:block  text-align="left" th:text="${subTable2.descriptive}"></fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" padding="1mm" text-align="center" display-align="center">
                            <fo:block th:text="${subTable2.term1Grade}"></fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                    <fo:table-row font-weight="bold" th:if="${model.body.thirdTable.marks.subTable3 != null && model.body.thirdTable.marks.subTable3.size() > 0}">
                        <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" display-align="center">
                            <fo:block>WORK EDUCATION</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" padding="1mm">
                            <fo:block>DESCRIPTIVE INDICATOR</fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" padding="1mm">
                            <fo:block>GRADE</fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                    <fo:table-row th:each="subTable3 : ${model.body.thirdTable.marks.subTable3}"
                                  th:if="${model.body.thirdTable.marks.subTable3 != null && model.body.thirdTable.marks.subTable3.size() > 0}">

                        <fo:table-cell border="1pt solid black" padding-left="1mm" padding="1mm" text-align="left" display-align="center">
                            <fo:block th:text="${subTable3.subjectName}"></fo:block>
                        </fo:table-cell>
                        <fo:table-cell font-size="10pt" border="1pt solid black" padding="1mm">
                            <fo:block text-align="left" th:text="${subTable3.descriptive}"></fo:block>
                        </fo:table-cell>
                        <fo:table-cell border="1pt solid black" padding="1mm" text-align="center" display-align="center">
                            <fo:block th:text="${subTable3.term1Grade}"></fo:block>
                        </fo:table-cell>
                    </fo:table-row>
                </fo:table-body>
            </fo:table>
        </fo:block>
    </fo:block-container>
    </fo:flow>
    </fo:page-sequence>
</fo:root>


