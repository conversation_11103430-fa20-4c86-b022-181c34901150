<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="report-card" page-width="210mm" page-height="297mm">
            <fo:region-body margin="15mm"/>
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="report-card">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" border="2pt solid black" padding="6mm"
                                padding-bottom="5mm">
                <!-- Logo -->
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="400%" content-height="400%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                 width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="25"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="25"/>
                                            <feFuncB type="linear" slope="25"/>
                                        </feComponentTransfer>
                                    </filter>
                                    <filter id="brightnessFilter1">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/>
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter1)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug != null && (#lists.contains({'pal174599', 'pal332908', 'pal454783'}, model.body.orgSlug))}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/Pallavi-model-school_25.png"/>
                                <image filter="url(#brightnessFilter1)"
                                       x="0" y="0"
                                       width="100%" height="100%"
                                       th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal988947', 'pal233196'}, model.body.orgSlug))}"
                                       xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/Pallavi-Model-School-Favicon_5.png"/>
                                <image filter="url(#brightnessFilter)"
                                       x="0" y="0" width="100%" height="100%"
                                       th:if="${model.body.orgSlug != null && !(#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}"
                                       xlink:href="https://dpsgurugram84.com/wp-content/uploads/2019/07/logo_dps.png"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>

                <fo:table border="none">
                    <fo:table-column column-width="26mm" />
                    <fo:table-column column-width="126mm" />
                    <fo:table-body >
                        <fo:table-row>
                            <fo:table-cell>

                                <fo:block th:if="${model.body.orgSlug != null && !(#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}">
                                    <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg")'
                                                         content-width="75px"  content-height="75px"  />
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal988947', 'pal233196'}, model.body.orgSlug))}">
                                    <fo:external-graphic src='url("https://images.wexledu.com/live-worksheets/wexl-internal/20240901043131721.png")'
                                                         content-width="75px" scaling="non-uniform"  />
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug != null && (#lists.contains({'pal174599', 'pal332908', 'pal454783'}, model.body.orgSlug))}">
                                    <fo:external-graphic src='url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS.png)'
                                                         content-width="75px" scaling="non-uniform"  />
                                </fo:block>
                                <fo:block margin-left="154mm" padding-top="-29mm" th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}" >
                                    <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/30+Years+Logo+White.png")'
                                                         content-width="75px" scaling="non-uniform"  />
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" padding-top="-5mm" space-after="0pt"
                                          th:if="${model.body.orgSlug == 'pal332908'}">
                                    PALLAVI INTERNATIONAL SCHOOL
                                    <fo:block font-size="14">SAGAR ROAD, HYDERABAD</fo:block>
                                </fo:block>
                                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" padding-top="-5mm" space-after="0pt"
                                          th:if="${model.body.orgSlug == 'pal454783'}">
                                    PALLAVI INTERNATIONAL SCHOOL
                                    <fo:block font-size="18">GANDIPET</fo:block>
                                </fo:block>
                                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" padding-top="-5mm" space-after="0pt"
                                          th:if="${model.body.orgSlug != 'pal332908' and model.body.orgSlug != 'pal454783' }"
                                          th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold"  space-before="3mm" font-family="Times New Roman, serif" text-align="center" space-after="3pt" padding-top="-5mm">
                                    <fo:inline th:replace="report-card/dps/fragment.xml :: ${model.body.orgSlug}"></fo:inline>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${ model.body.orgSlug == 'del189476' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630520 </fo:block>
                                    <fo:block> School Code:56955 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal454783'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630333 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal988947'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630095
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal233196'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :130145
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal174599'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630290
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal556078'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-before="5pt"
                                          th:if="${ model.body.orgSlug == 'del765517' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630285 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-before="5pt"
                                          th:if="${ model.body.orgSlug == 'del909850' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630448 </fo:block>
                                    <fo:block>  ISO 9001:2005, ISO 45001:2018, ISO 21001:2018 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${!(model.body.orgSlug matches 'pal556078|del765517|del909850|del189476|pal332908|pal174599|pal233196|pal988947|pal454783')}">
                                    <fo:block th:text="${model.header.isoData}"></fo:block>
                                </fo:block>
                                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt">Record
                                    of
                                    Academic Performance
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt">

                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt"
                                          th:text="${model.header.academicYear}">
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <!-- Student Information -->

                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" space-after="5pt"
                          th:padding-top="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug)) ? '-22mm' : '0mm'}">
                    <fo:table border="none" color="black">
                        <fo:table-column column-width="52mm" />
                        <fo:table-column column-width="70mm" />
                        <fo:table-column column-width="22mm" />
                        <fo:table-column column-width="38mm" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Student Id </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" >:  <fo:inline th:text="${model.header.studentId}"/></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Roll No</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" >:  <fo:inline th:text="${model.body.rollNumber}"/></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding-top="0.1cm">
                                    <fo:block>Name of the Student</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="0.1cm">
                                    <fo:block font-weight="bold" >:  <fo:inline th:text="${model.body.name}"/></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-top="0.1cm">
                                    <fo:block>Class &amp; Section</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="0.1cm">
                                    <fo:block font-weight="bold" >:
                                        <fo:inline th:text="${model.body.section}"/>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding-top="0.1cm">
                                    <fo:block>Mother's/Father's/Guardian's Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="0.1cm">
                                    <fo:block font-weight="bold" >:  <fo:inline th:text="${model.body.fathersName}"/></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-top="0.1cm">
                                    <fo:block>Date of Birth</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="0.1cm">
                                    <fo:block font-weight="bold" >:  <fo:inline th:text="${model.body.dateOfBirth}"/></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block font-family="Arial" font-size="12pt" text-align="center" space-after="0.2cm" font-weight="bold" color="black">
                    <fo:inline > PART I :</fo:inline>
                    <fo:inline th:text="${model.body.firstTable.title}"/>
                </fo:block>

                <fo:block border-width="5mm" font-size="9pt" font-family="Arial"
                          th:if="${model.body.gradeSlug != 'x'
                          and model.body.orgSlug != null and not #lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug)}" >
                    <fo:table border="none" >
                        <fo:table-column column-width="9mm"/>
                        <fo:table-column column-width="30mm"/>
                        <fo:table-column column-width="21mm"/>
                        <fo:table-column column-width="21mm"/>
                        <fo:table-column column-width="23mm"/>
                        <fo:table-column column-width="21mm"/>
                        <fo:table-column column-width="23mm"/>
                        <fo:table-column column-width="21mm"/>
                        <fo:table-column column-width="12.5mm"/>
                        <fo:table-body font-size="9pt">
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" height="15mm" >
                                    <fo:block text-align="center" padding-top="0.1cm">SNO</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" height="15mm" >
                                    <fo:block text-align="center" padding-top="0.1cm">SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center" padding-top="0.1cm">PT (PEN &amp; PAPER TEST) (5M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm">MULTIPLE ASSESSMENT (5M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center" padding-top="0.1cm">SUBJECT ENRICHMENT (5M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center" padding-top="0.1cm">PORTFOLIO (5M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm">ANNUAL EXAMINATION (80M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center" padding-top="0.1cm">MARKS OBTAINED (100M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center" padding-top="0.1cm">GRADE</fo:block>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row th:each="marks :${model.body.firstTable.marks}">
                                <fo:table-cell border="1pt solid black" height="5mm">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.sno}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" height="5mm">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.subject}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.bestOfPt}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.ma}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.se}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.po}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" height="5mm">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.ae}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" height="5mm">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.total}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" height="5mm">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.grade}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block space-after="3mm" font-size="9pt"
                          th:if="${model.body.gradeSlug != 'x'
                          and model.body.orgSlug != null and not #lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug)}">
                    <fo:table border="none">
                        <fo:table-column column-width="148mm"/>
                        <fo:table-column column-width="21mm"/>
                        <fo:table-column column-width="12.5mm"/>
                        <fo:table-body>
                            <fo:table-row font-size="9pt">
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="0.1cm" margin-left="0.2cm">TOTAL</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center">
                                    <fo:block padding-top="0.1cm" th:text="${model.body.firstTable.overAllMarksOfAllSubjects}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center">
                                    <fo:block padding-top="0.1cm" th:text="${model.body.firstTable.totalGrade}"/>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row font-size="9pt">
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="0.1cm" margin-left="0.2cm">OVERALL PERCENTAGE/GRADE</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center">
                                    <fo:block padding-top="0.1cm" th:text="${model.body.firstTable.overallPercentage}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center">
                                    <fo:block padding-top="0.1cm" th:text="${model.body.firstTable.totalGrade}"/>
                                </fo:table-cell>
                            </fo:table-row>

                        </fo:table-body>
                    </fo:table>
                </fo:block>



                <fo:block border-width="5mm" font-size="9pt" font-family="Arial"
                          th:if="${model.body.gradeSlug == 'x'
                          and model.body.orgSlug != null and not #lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug)}" >
                    <fo:table border="none" >
                        <fo:table-column column-width="9mm"/>
                        <fo:table-column column-width="30mm"/>
                        <fo:table-column column-width="21mm"/>
                        <fo:table-column column-width="21mm"/>
                        <fo:table-column column-width="23mm"/>
                        <fo:table-column column-width="21mm"/>
                        <fo:table-column column-width="23mm"/>
                        <fo:table-column column-width="21mm"/>
                        <fo:table-column column-width="12.5mm"/>
                        <fo:table-body font-size="9pt">
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" height="15mm" >
                                    <fo:block text-align="center" padding-top="0.1cm">SNO</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" height="15mm" >
                                    <fo:block text-align="center" padding-top="0.1cm">SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center" padding-top="0.1cm">PT (PEN &amp; PAPER TEST) (5M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm">MULTIPLE ASSESSMENT (5M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center" padding-top="0.1cm">SUBJECT ENRICHMENT (5M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center" padding-top="0.1cm">PORTFOLIO (5M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm">ANNUAL EXAMINATION (80M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center" padding-top="0.1cm">MARKS OBTAINED (100M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center" padding-top="0.1cm">GRADE</fo:block>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row th:each="marks :${model.body.firstTable.marks}">
                                <fo:table-cell border="1pt solid black" height="5mm">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.sno}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" height="5mm">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.subject}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.bestOfPt}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.ma}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.se}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.po}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" height="5mm">
                                    <fo:block />
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" height="5mm">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.internalAssessmentMarks}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" height="5mm">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.internalAssessmentGrade}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block space-after="3mm" font-size="9pt"
                          th:if="${model.body.gradeSlug == 'x'
                          and model.body.orgSlug != null and not #lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug)}">
                    <fo:table border="none">
                        <fo:table-column column-width="148mm"/>
                        <fo:table-column column-width="21mm"/>
                        <fo:table-column column-width="12.5mm"/>
                        <fo:table-body>
                            <fo:table-row font-size="9pt">
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="0.1cm" margin-left="0.2cm">TOTAL</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center">
                                    <fo:block padding-top="0.1cm" th:text="${model.body.firstTable.totalInternalSubjectMarks}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center">
                                    <fo:block padding-top="0.1cm" th:text="${model.body.firstTable.totalInternalSubjectGrade}"/>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row font-size="9pt">
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="0.1cm" margin-left="0.2cm">OVERALL PERCENTAGE/GRADE</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center">
                                    <fo:block padding-top="0.1cm" th:text="${model.body.firstTable.isPercentage}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center">
                                    <fo:block padding-top="0.1cm" th:text="${model.body.firstTable.overallInternalSubjectGrade}"/>
                                </fo:table-cell>
                            </fo:table-row>

                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block border-width="5mm" font-size="9pt" font-family="Times New Roman, serif"
                          th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}" >
                    <fo:table border="none" >
                        <fo:table-column column-width="9mm"/>
                        <fo:table-column column-width="30mm"/>
                        <fo:table-column column-width="21mm"/>
                        <fo:table-column column-width="21mm"/>
                        <fo:table-column column-width="23mm"/>
                        <fo:table-column column-width="21mm"/>
                        <fo:table-column column-width="23mm"/>
                        <fo:table-column column-width="21mm"/>
                        <fo:table-column column-width="12.5mm"/>
                        <fo:table-body font-size="9pt">
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" height="15mm" >
                                    <fo:block text-align="center" padding-top="0.1cm">SNO</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" height="15mm" >
                                    <fo:block text-align="center" padding-top="0.1cm">SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center" padding-top="0.1cm">PT (PEN &amp; PAPER TEST) (5M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm">MULTIPLE ASSESSMENT (5M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center" padding-top="0.1cm">SUBJECT ENRICHMENT (5M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center" padding-top="0.1cm">PORTFOLIO (5M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm">ANNUAL EXAMINATION (80M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center" padding-top="0.1cm">MARKS OBTAINED (100M)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center" padding-top="0.1cm">GRADE</fo:block>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row th:each="marks :${model.body.firstTable.marks}">
                                <fo:table-cell border="1pt solid black" height="5mm">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.sno}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" height="5mm">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.subject}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.bestOfPt}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.ma}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.se}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.po}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" height="5mm">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.ae}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" height="5mm">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.marksForPallavi}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" height="5mm">
                                    <fo:block text-align="center" padding-top="0.1cm" th:text="${marks.gradeForPallavi}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block space-after="3mm" font-size="9pt"
                          th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}">
                    <fo:table border="none">
                        <fo:table-column column-width="148mm"/>
                        <fo:table-column column-width="21mm"/>
                        <fo:table-column column-width="12.5mm"/>
                        <fo:table-body>
                            <fo:table-row font-size="9pt">
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="0.1cm" margin-left="0.2cm">TOTAL</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center">
                                    <fo:block padding-top="0.1cm" th:text="${model.body.firstTable.overAllMarksOfAllSubjectsForPallavi}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center">
                                    <fo:block padding-top="0.1cm" th:text="${model.body.firstTable.overAllGradeOfAllSubjectsForPallavi}"/>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row font-size="9pt">
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="0.1cm" margin-left="0.2cm">OVERALL PERCENTAGE/GRADE</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center">
                                    <fo:block padding-top="0.1cm" th:text="${model.body.firstTable.overAllPercentageOfPallavi}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center">
                                    <fo:block padding-top="0.1cm" th:text="${model.body.firstTable.overAllGradeOfAllSubjectsForPallavi}"/>
                                </fo:table-cell>
                            </fo:table-row>

                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block th:if="${model.body.secondTable.secondTableValues != null and #lists.size(model.body.secondTable.secondTableValues) > 0}"
                        font-family="Arial" font-size="12pt" text-align="center">
                    <fo:block space-before="0.5cm" font-weight="bold" color="black">Additional Subjects</fo:block>
                </fo:block>
                <fo:block space-before="0.1cm" th:if="${model.body.secondTable.secondTableValues != null and #lists.size(model.body.secondTable.secondTableValues) > 0}" >
                    <fo:table border="none" font-size="9pt">
                        <fo:table-column column-width="148mm"/>
                        <fo:table-column column-width="21mm"/>
                        <fo:table-column column-width="12.5mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="0.1cm" margin-left="0.2cm">SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm" >MARKS</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block text-align="center" padding-top="0.1cm" >GRADE</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row th:each="secondTableValues :${model.body.secondTable.secondTableValues}">
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="0.1cm" margin-left="0.2cm" th:text="${secondTableValues.subject}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block padding-top="0.1cm" text-align="center" th:text="${secondTableValues.marks}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block padding-top="0.1cm" text-align="center" th:text="${secondTableValues.grade}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <!-- CoScholostic Mandatory Table -->
                <fo:block th:if="${model.body.fourthTable.fourthTableValues != null and #lists.size(model.body.fourthTable.fourthTableValues) > 0}"
                          font-family="Arial" font-size="12pt" text-align="center" >
                    <fo:block space-before="0.5cm" font-weight="bold" color="black">
                        <fo:inline > PART II :</fo:inline>
                        <fo:inline  th:text="${model.body.fourthTable.title}"/>
                    </fo:block>
                </fo:block>
                <fo:block th:if="${model.body.fourthTable.fourthTableValues != null and #lists.size(model.body.fourthTable.fourthTableValues) > 0}" space-after="0.2cm">
                    <fo:table font-size="9pt" space-before="0.1mm">
                        <fo:table-column column-width="147.5mm"/>
                        <fo:table-column column-width="33.5mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="0.1cm" margin-left="0.2cm">SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center" padding-top="0.1cm">GRADE</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row th:each="fourthTableValues :${model.body.fourthTable.fourthTableValues}" >
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="0.1cm" margin-left="0.2cm" th:text="${fourthTableValues.subject}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block padding-top="0.1cm" text-align="center" th:text="${fourthTableValues.grade}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <!-- CoScholostic Optional Table -->
                <fo:block th:if="${model.body.thirdTable.thirdTableValues != null and #lists.size(model.body.thirdTable.thirdTableValues) > 0}"
                          font-family="Arial" font-size="12pt" text-align="center" >
                    <fo:block space-before="0.5cm" font-weight="bold" color="black">
                        <fo:inline > PART II :</fo:inline>
                        <fo:inline  >hi</fo:inline>
                    </fo:block>
                </fo:block>
                <fo:block th:if="${model.body.thirdTable.thirdTableValues != null and #lists.size(model.body.thirdTable.thirdTableValues) > 0}" space-after="0.2cm">
                    <fo:table font-size="9pt" space-before="0.1mm">
                        <fo:table-column column-width="147.5mm"/>
                        <fo:table-column column-width="33.5mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="0.1cm" margin-left="0.2cm">SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block text-align="center" padding-top="0.1cm">GRADE</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row th:each="thirdTableValues :${model.body.thirdTable.thirdTableValues}" >
                                <fo:table-cell border="1pt solid black" >
                                    <fo:block padding-top="0.1cm" margin-left="0.2cm" th:text="${thirdTableValues.subject}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black">
                                    <fo:block padding-top="0.1cm" text-align="center" th:text="${thirdTableValues.grade}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block font-family="Arial" font-size="12pt" text-align="left" font-weight="bold">
                    <fo:block space-before="0.3cm" font-weight="bold" th:text="${model.body.attendance.title}"/>
                </fo:block>
                <fo:block space-before="0.1cm" space-after="0.5cm" text-align="left" font-size="9pt">
                    <fo:table border="none">
                        <fo:table-column column-width="60.5mm"/>
                        <fo:table-column column-width="60mm"/>
                        <fo:table-column column-width="60.5mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" text-align="center">
                                    <fo:block>Total No. of Working days</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center">
                                    <fo:block>No. of days Present</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center">
                                    <fo:block>Percentage of Attendance(%)</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row text-align="center">
                                <fo:table-cell border="1pt solid black" text-align="center">
                                    <fo:block th:text="${model.body.attendance.totalWorkingDays}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center">
                                    <fo:block th:text="${model.body.attendance.daysPresent}"/>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" text-align="center">
                                    <fo:block th:text="${model.body.attendance.attendancePercentage}"/>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block font-family="Arial" font-size="12pt" text-align="left">
                    <fo:block space-after="0.7cm" >
                        <fo:inline font-weight="bold">Remarks :</fo:inline>
                        <fo:inline th:text="${model.body.attendance.remarks}"/>
                    </fo:block>
                </fo:block>

                <fo:block th:if="${model.body.orgSlug == 'pal454783'}">
                    <fo:block border-width="1mm" font-size="10pt" font-family="Times New Roman, serif" space-before="10mm" padding-top="-11mm">
                        <fo:table border="none">
                            <fo:table-column column-width="45mm" />
                            <fo:table-column column-width="60mm" />
                            <fo:table-column column-width="95mm" />
                            <fo:table-body font-family="Times New Roman, serif">
                                <fo:table-row>
                                    <fo:table-cell>
                                        <fo:block padding-top="-7mm"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block padding-top="-7mm"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block>
                                            <fo:block-container padding-top="-5mm" width="100mm" height="35mm" display-align="center" text-align="center">
                                                <fo:block>
                                                    <fo:external-graphic src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/signature.png')"
                                                                         scaling="non-uniform"
                                                                         content-width="35mm"
                                                                         content-height="15mm" />
                                                </fo:block>
                                            </fo:block-container>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>

                                <fo:table-row >
                                    <fo:table-cell padding-top="-12mm" text-align="left" font-weight="bold">
                                        <fo:block>Class Teacher Signature  </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block padding-top="-12mm" > </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-left="12mm" padding-top="-12mm"  text-align="center" font-weight="bold">
                                        <fo:block>Principal</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>

                <fo:block th:if="${(model.body.orgSlug matches 'pal174599|pal233196|pal556078')}">
                    <fo:block border-width="1mm" font-size="10pt" space-before="20mm" space-after="6mm" font-family="Times New Roman, serif">
                        <fo:table border="none">
                            <fo:table-column column-width="50mm" />
                            <fo:table-column column-width="210mm" />
                            <fo:table-body font-family="Times New Roman, serif">
                                <fo:table-row >
                                    <fo:table-cell text-align="left" font-weight="bold">
                                        <fo:block>Class Teacher</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="center" font-weight="bold">
                                        <fo:block>Principal</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>

                <fo:block th:if="${(model.body.orgSlug matches 'pal332908')}">
                    <fo:block border-width="1mm" font-size="10pt" space-before="20mm" space-after="6mm" font-family="Times New Roman, serif">
                        <fo:table border="none">
                            <fo:table-column column-width="50mm" />
                            <fo:table-column column-width="210mm" />
                            <fo:table-body font-family="Times New Roman, serif">
                                <fo:table-row >
                                    <fo:table-cell text-align="left" font-weight="bold">
                                        <fo:block>Principal / Headmaster</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="center" font-weight="bold">
                                        <fo:block>Class Teacher</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>

                <fo:block font-family="Arial" font-size="10pt" font-weight="bold" space-after="0.1cm"
                          th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}">
                    <fo:block th:if="${(model.body.orgSlug matches 'pal988947')}">
                        <fo:block th:replace="report-card/dps/pallavi-signature.xml :: ${model.body.gradeSlug}"></fo:block>
                    </fo:block>
                    <fo:block  space-after="0.05cm" th:padding-top="${model.body.orgSlug == 'pal988947'? '0mm': '-5mm'}">
                        <fo:block>Name of the Class Teacher :
                        <fo:inline th:text="${model.body.classTeacher}"/>
                        </fo:block>
                        <fo:block>Grading Scale :
                        <fo:inline>8</fo:inline>
                        </fo:block>
                    </fo:block>
                </fo:block>

                    <fo:block border-width="1mm" font-size="10pt" space-before="1cm" space-after="4pt" font-family="Times New Roman, serif"
                              th:if="${model.body.orgSlug != null && !(#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}">
                        <fo:table border="none">
                            <fo:table-column column-width="60mm" />
                            <fo:table-column column-width="60mm" />
                            <fo:table-column column-width="50mm" />
                            <fo:table-body font-family="Arial">
                                <fo:table-row >
                                    <fo:table-cell text-align="left" font-weight="bold">
                                        <fo:block>Sr.Principal</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="center" font-weight="bold">
                                        <fo:block>Vice Principal</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell text-align="right" font-weight="bold">
                                        <fo:block>Class Teacher</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>

                <fo:block  text-align="left" font-size="10pt"
                           th:padding-top="${
        (model.body.secondTable.secondTableValues != null && #lists.size(model.body.secondTable.secondTableValues) > 0)
        && (model.body.thirdTable.thirdTableValues != null && #lists.size(model.body.thirdTable.thirdTableValues) > 0) ? '1cm' :
        ((model.body.secondTable.secondTableValues != null && #lists.size(model.body.secondTable.secondTableValues) > 0)
        || (model.body.thirdTable.thirdTableValues != null && #lists.size(model.body.thirdTable.thirdTableValues) > 0)) ? '0.5cm' : '2cm'}">
                    <fo:table border="none">
                        <fo:table-column column-width="23mm"/>
                        <fo:table-column column-width="22.5mm"/>
                        <fo:table-column column-width="22.5mm"/>
                        <fo:table-column column-width="22.5mm"/>
                        <fo:table-column column-width="22.5mm"/>
                        <fo:table-column column-width="22.5mm"/>
                        <fo:table-column column-width="22.5mm"/>
                        <fo:table-column column-width="23mm"/>
                        <fo:table-body>
                            <fo:table-row >
                                <fo:table-cell padding-top="0.1cm" border="1pt solid black" >
                                    <fo:block margin-left="0.2cm">A1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="0.1cm" border="1pt solid black">
                                    <fo:block margin-left="0.2cm">A2</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="0.1cm" border="1pt solid black" >
                                    <fo:block margin-left="0.2cm">B1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="0.1cm" border="1pt solid black" >
                                    <fo:block margin-left="0.2cm">B2</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="0.1cm" border="1pt solid black" >
                                    <fo:block margin-left="0.2cm">C1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="0.1cm" border="1pt solid black" >
                                    <fo:block margin-left="0.2cm">C2</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="0.1cm" border="1pt solid black" >
                                    <fo:block margin-left="0.2cm">D</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="0.1cm" border="1pt solid black" >
                                    <fo:block margin-left="0.2cm">E</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row >
                                <fo:table-cell padding-top="0.1cm" border="1pt solid black">
                                    <fo:block margin-left="0.2cm">91-100 </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="0.1cm" border="1pt solid black" >
                                    <fo:block margin-left="0.2cm">81-90.99 </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="0.1cm" border="1pt solid black" >
                                    <fo:block margin-left="0.2cm">71-80.99 </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="0.1cm" border="1pt solid black" >
                                    <fo:block margin-left="0.2cm">61-70.99 </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="0.1cm" border="1pt solid black" >
                                    <fo:block margin-left="0.2cm">51-60.99 </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="0.1cm" border="1pt solid black" >
                                    <fo:block margin-left="0.2cm">41-50.99 </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="0.1cm" border="1pt solid black">
                                    <fo:block margin-left="0.2cm">33-40.99 </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="0.1cm" border="1pt solid black" >
                                    <fo:block margin-left="0.2cm">0-32.99 </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>
