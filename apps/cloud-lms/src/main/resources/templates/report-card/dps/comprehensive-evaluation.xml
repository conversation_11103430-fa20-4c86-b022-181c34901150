<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="16mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="-0.5cm" border="2pt solid black"  padding="8mm">
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%" padding-top="-1mm">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="300%" content-height="300%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="7"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="7"/>
                                            <feFuncB type="linear" slope="7"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none">
                    <fo:table-column column-width="26mm"/>
                    <fo:table-column column-width="120mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block padding-top="-5mm">
                                    <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg")'
                                                         content-width="75px" content-height="75px"/>
                                </fo:block>
                            </fo:table-cell>

                            <fo:table-cell>
                                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="3pt"
                                          th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block>
                                <fo:block font-size="9pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="6pt"
                                >Survey No. 180, Indraprastha Colony, Road No. 2, Near Hanuman Temple, Saidabad, Hyderabad - 500059
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="900" font-family="Times New Roman, serif" text-align="center" space-after="1pt">
                                    CONTINUOUS COMPREHENSIVE EVALUATION CARD
                                </fo:block>
                                <fo:block font-size="15pt" font-weight="900" font-family="Times New Roman, serif" text-align="center" space-after="2pt"
                                          th:text="${model.body.termSlug == 't1' ? 'Term-1 Observations 2024-25' : 'Final Observations 2024-25'}">
                                </fo:block>

                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block border-width="1mm" font-size="10pt" font-family="Times New Roman, Serif" space-before="7mm" space-after="2pt" >
                    <fo:table border="none">
                        <fo:table-column column-width="35mm"/>
                        <fo:table-column column-width="94mm"/>
                        <fo:table-column column-width="27mm"/>
                        <fo:table-column column-width="24mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block margin-bottom="2mm" font-weight="bold">Name of the Student : </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.name}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block margin-bottom="2mm" font-weight="bold">Class &amp; Section : </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.className}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block margin-bottom="2mm" font-weight="bold">Roll No&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;: </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.rollNumber}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block margin-bottom="2mm" font-weight="bold">Attendance&#160;&#160;&#160;&#160;&#160;&#160;&#160; : </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.attendance}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block font-size="11pt" font-family="Times New Roman, Serif" >
                    <fo:table table-layout="fixed" width="100%" >
                        <fo:table-column column-width="23%"/>
                        <fo:table-column column-width="2%"/>
                        <fo:table-column column-width="23%"/>
                        <fo:table-column column-width="2%"/>
                        <fo:table-column column-width="23%"/>
                        <fo:table-column column-width="2%"/>
                        <fo:table-column column-width="23%"/>
                        <fo:table-column column-width="2%"/>

                        <fo:table-body>
                            <fo:table-row >
                                <fo:table-cell >
                                    <fo:block border="1pt solid black" font-weight="bold" background-color="#9149f5" text-align="center" space-after="1mm">
                                        VERBAL LINGUISTIC
                                    </fo:block>
                                </fo:table-cell>

                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>

                                <fo:table-cell >
                                    <fo:block border="1pt solid black" font-weight="bold" background-color="#FF6666" text-align="center" space-after="1mm">
                                        LOGICAL MATHEMATICAL
                                    </fo:block>
                                </fo:table-cell>

                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>

                                <fo:table-cell >
                                    <fo:block border="1pt solid black" font-weight="bold" background-color="#6fed4c" text-align="center" space-after="1mm">
                                        NATURALISTIC / ENVIRONMENTALIST
                                    </fo:block>
                                </fo:table-cell>

                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>

                                <fo:table-cell >
                                    <fo:block border="1pt solid black" font-weight="bold" background-color="#D2B48C" text-align="center" space-after="1mm">
                                        INTRAPERSONAL/SELF
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row th:if="${model.body.gradeSlug != 'nur'} ">
                                <fo:table-cell>
                                    <fo:block font-size="10pt" font-weight="bold" text-align="left">
                                        <fo:inline th:text="${model.body.firstTable[0].subjects}"/>
                                        <fo:inline> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</fo:inline>
                                        <fo:instream-foreign-object>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                <rect width="10" height="10" th:attr="fill=${model.body.firstTable[0].color}" stroke="black" stroke-width="0.5"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>

                                    <fo:block font-size="10pt" font-weight="bold" text-align="left">
                                        <fo:inline th:text="${model.body.firstTable[1].subjects}"/>
                                        <fo:inline>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; </fo:inline>
                                        <fo:instream-foreign-object>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                <rect width="10" height="10" th:attr="fill=${model.body.firstTable[1].color}" stroke="black" stroke-width="0.5"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" text-align="left">
                                        <fo:inline th:text="${model.body.firstTable[2].subjects}"/>
                                        <fo:inline> </fo:inline>
                                        <fo:inline padding-left="3.4mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[2].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" text-align="left">
                                        <fo:inline th:text="${model.body.firstTable[3].subjects}"/>
                                        <fo:inline> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</fo:inline>
                                        <fo:instream-foreign-object>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                <rect width="10" height="10" th:attr="fill=${model.body.firstTable[3].color}" stroke="black" stroke-width="0.5"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[4].subjects}"/>
                                        <fo:inline> &#160;&#160;&#160;&#160;</fo:inline>
                                        <fo:instream-foreign-object>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                <rect width="10" height="10" th:attr="fill=${model.body.firstTable[4].color}" stroke="black" stroke-width="0.5"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>

                                    </fo:block>
                                </fo:table-cell>

                                <fo:table-cell>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[5].subjects}"/>
                                        <fo:inline> &#160;&#160;&#160;&#160;</fo:inline>
                                        <fo:instream-foreign-object>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                <rect width="10" height="10" th:attr="fill=${model.body.firstTable[5].color}" stroke="black" stroke-width="0.5"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>

                                    </fo:block>
                                </fo:table-cell>

                                <fo:table-cell>
                                    <fo:block font-size="10pt" font-weight="bold" text-align="left">
                                        <fo:inline th:text="${model.body.firstTable[6].subjects}"/>
                                        <fo:inline> </fo:inline>
                                        <fo:inline padding-left="6.6mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[6].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" text-align="left">
                                        <fo:inline th:text="${model.body.firstTable[7].subjects}"/>
                                        <fo:inline> </fo:inline>
                                        <fo:inline padding-left="8.5mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[7].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" text-align="left">
                                        <fo:inline th:text="${model.body.firstTable[8].subjects}"/>
                                        <fo:inline> </fo:inline>
                                        <fo:inline padding-left="4mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[8].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" text-align="left">
                                        <fo:inline th:text="${model.body.firstTable[9].subjects}"/>
                                        <fo:inline> </fo:inline>
                                        <fo:instream-foreign-object>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                <rect width="10" height="10" th:attr="fill=${model.body.firstTable[9].color}" stroke="black" stroke-width="0.5"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>

                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row th:if="${model.body.gradeSlug == 'nur'} ">
                                <fo:table-cell>
                                    <fo:block font-size="10pt" font-weight="bold" text-align="left">
                                        <fo:inline th:text="${model.body.firstTable[0].subjects}"/>
                                        <fo:inline> </fo:inline>
                                        <fo:inline padding-left="16mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[0].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" text-align="left">
                                        <fo:inline th:text="${model.body.firstTable[1].subjects}"/>
                                        <fo:inline> </fo:inline>
                                        <fo:inline padding-left="17.8mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[1].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" text-align="left">
                                        <fo:inline th:text="${model.body.firstTable[2].subjects}"/>
                                        <fo:inline> </fo:inline>
                                        <fo:inline padding-left="4.4mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[2].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>

                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[3].subjects}"/>
                                        <fo:inline></fo:inline>
                                        <fo:inline padding-left="4mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[3].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>

                                    </fo:block>
                                </fo:table-cell>

                                <fo:table-cell>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[4].subjects}"/>
                                        <fo:inline> &#160;&#160;&#160;&#160;</fo:inline>
                                        <fo:instream-foreign-object>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                <rect width="10" height="10" th:attr="fill=${model.body.firstTable[4].color}" stroke="black" stroke-width="0.5"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>

                                    </fo:block>
                                </fo:table-cell>

                                <fo:table-cell>
                                    <fo:block font-size="10pt" font-weight="bold" text-align="left">
                                        <fo:inline th:text="${model.body.firstTable[5].subjects}"/>
                                        <fo:inline padding-left="8.5mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[5].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" text-align="left">
                                        <fo:inline th:text="${model.body.firstTable[6].subjects}"/>
                                        <fo:inline padding-left="10.2mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[6].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" text-align="left">
                                        <fo:inline th:text="${model.body.firstTable[7].subjects}"/>
                                        <fo:inline> </fo:inline>
                                        <fo:inline padding-left="4.8mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[7].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" text-align="left">
                                        <fo:inline th:text="${model.body.firstTable[8].subjects}"/>
                                        <fo:inline> </fo:inline>
                                        <fo:inline >
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[8].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>

                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>




                            <fo:table-row>
                                <fo:table-cell >
                                    <fo:block border="1pt solid black" font-weight="bold" background-color="violet" text-align="center" space-after="1mm">
                                        INTERPERSONAL/SOCIAL
                                    </fo:block>
                                </fo:table-cell>

                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>

                                <fo:table-cell >
                                    <fo:block border="1pt solid black" font-weight="bold" background-color="cyan" text-align="center" space-after="1mm">
                                        VISUAL/SPATIAL &amp; MUSICAL/RHYTHMIC
                                    </fo:block>
                                </fo:table-cell>

                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>

                                <fo:table-cell >
                                    <fo:block border="1pt solid black" font-weight="bold" background-color="#D0021B" text-align="center" space-after="1mm">
                                        BODILY KINESTHETIC
                                    </fo:block>
                                </fo:table-cell>

                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>


                                <fo:table-cell >
                                    <fo:block border="1pt solid black" font-weight="bold" background-color="#FF6347" text-align="center" space-after="1mm">
                                        PERSONALITY DEVELOPMENT
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row th:if="${model.body.gradeSlug != 'nur'}">
                                <fo:table-cell>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[10].subjects}"/>
                                        <fo:inline> &#160;&#160;&#160;&#160;&#160;</fo:inline>
                                        <fo:instream-foreign-object>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                <rect width="10" height="10" th:attr="fill=${model.body.firstTable[10].color}" stroke="black" stroke-width="0.5"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>

                                    </fo:block>
                                </fo:table-cell>

                                <fo:table-cell>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[11].subjects}"/>
                                        <fo:inline> </fo:inline>
                                        <fo:inline padding-left="25.7mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[11].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[12].subjects}"/>
                                        <fo:inline> </fo:inline>
                                        <fo:inline padding-left="21mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[12].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[13].subjects}"/>
                                        <fo:inline> </fo:inline>
                                        <fo:inline padding-left="11mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[13].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[14].subjects}"/>
                                        <fo:inline> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</fo:inline>
                                        <fo:instream-foreign-object>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                <rect width="10" height="10" th:attr="fill=${model.body.firstTable[14].color}" stroke="black" stroke-width="0.5"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>

                                    </fo:block>
                                </fo:table-cell>

                                <fo:table-cell>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[15].subjects}"/>
                                        <fo:inline> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</fo:inline>
                                        <fo:instream-foreign-object>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                <rect width="10" height="10" th:attr="fill=${model.body.firstTable[15].color}" stroke="black" stroke-width="0.5"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[16].subjects}"/>
                                        <fo:inline></fo:inline>
                                        <fo:inline padding-left="25.8mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[16].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[17].subjects}"/>
                                        <fo:inline> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</fo:inline>
                                        <fo:instream-foreign-object>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                <rect width="10" height="10" th:attr="fill=${model.body.firstTable[17].color}" stroke="black" stroke-width="0.5"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>

                                    </fo:block>
                                </fo:table-cell>

                                <fo:table-cell>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[18].subjects}"/>
                                        <fo:inline> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</fo:inline>
                                        <fo:instream-foreign-object>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                <rect width="10" height="10" th:attr="fill=${model.body.firstTable[18].color}" stroke="black" stroke-width="0.5"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[19].subjects}"/>
                                        <fo:inline> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</fo:inline>
                                        <fo:instream-foreign-object>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                <rect width="10" height="10" th:attr="fill=${model.body.firstTable[19].color}" stroke="black" stroke-width="0.5"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>

                                    </fo:block>
                                </fo:table-cell>

                            </fo:table-row>


                            <fo:table-row th:if="${model.body.gradeSlug == 'nur'}">
                                <fo:table-cell>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[9].subjects}"/>
                                        <fo:inline> &#160;&#160;&#160;&#160;&#160;</fo:inline>
                                        <fo:instream-foreign-object>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                <rect width="10" height="10" th:attr="fill=${model.body.firstTable[9].color}" stroke="black" stroke-width="0.5"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>

                                    </fo:block>
                                </fo:table-cell>

                                <fo:table-cell>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[10].subjects}"/>
                                        <fo:inline> </fo:inline>
                                        <fo:inline padding-left="25.8mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[10].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[11].subjects}"/>
                                        <fo:inline> </fo:inline>
                                        <fo:inline padding-left="21mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[11].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[12].subjects}"/>
                                        <fo:inline> </fo:inline>
                                        <fo:inline padding-left="11mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[12].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[13].subjects}"/>
                                        <fo:inline> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</fo:inline>
                                        <fo:instream-foreign-object>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                <rect width="10" height="10" th:attr="fill=${model.body.firstTable[13].color}" stroke="black" stroke-width="0.5"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>

                                    </fo:block>
                                </fo:table-cell>

                                <fo:table-cell>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[14].subjects}"/>
                                        <fo:inline></fo:inline>
                                        <fo:inline padding-left="15.2mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[14].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[15].subjects}"/>
                                        <fo:inline> </fo:inline>
                                        <fo:inline padding-left="26mm">
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                    <rect width="10" height="10" th:attr="fill=${model.body.firstTable[15].color}" stroke="black" stroke-width="0.5"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[16].subjects}"/>
                                        <fo:inline> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</fo:inline>
                                        <fo:instream-foreign-object>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                <rect width="10" height="10" th:attr="fill=${model.body.firstTable[16].color}" stroke="black" stroke-width="0.5"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>

                                    </fo:block>
                                </fo:table-cell>

                                <fo:table-cell>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[17].subjects}"/>
                                        <fo:inline> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</fo:inline>
                                        <fo:instream-foreign-object>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                <rect width="10" height="10" th:attr="fill=${model.body.firstTable[17].color}" stroke="black" stroke-width="0.5"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                    <fo:block font-size="10pt" font-weight="bold" >
                                        <fo:inline th:text="${model.body.firstTable[18].subjects}"/>
                                        <fo:inline> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</fo:inline>
                                        <fo:instream-foreign-object>
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" style="vertical-align:middle;">
                                                <rect width="10" height="10" th:attr="fill=${model.body.firstTable[18].color}" stroke="black" stroke-width="0.5"/>
                                            </svg>
                                        </fo:instream-foreign-object>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>

                                    </fo:block>
                                </fo:table-cell>

                            </fo:table-row>



                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block border-width="1mm" font-size="10pt" font-family="Times New Roman, Serif" space-before="7mm">
                    <fo:table>
                        <fo:table-column column-width="49%"/>
                        <fo:table-column column-width="2%"/>
                        <fo:table-column column-width="49%"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block border="1pt solid black" background-color="#6495ED" text-align="center">PARTICIPATION IN EVENTS / ACHIEVEMENTS </fo:block>
                                    <fo:block border="1pt solid black" text-align="center" th:text="${model.body.achievements}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block border="1pt solid black" background-color="#90EE90" text-align="center">COMMENTS </fo:block>
                                    <fo:block border="1pt solid black" text-align="center" th:text="${model.body.comments}"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block font-weight="bold" border-width="1mm" font-size="10pt" font-family="Times New Roman, Serif" space-before="7mm">Colour Grading Scheme </fo:block>
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, Serif" space-before="3mm">
                    <fo:table>
                        <fo:table-column column-width="15mm"/>
                        <fo:table-column column-width="37mm"/>
                        <fo:table-column column-width="45mm"/>
                        <fo:table-column column-width="37mm"/>
                        <fo:table-column column-width="45mm"/>

                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block>

                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>ALWAYS
                                        <fo:inline>
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="9">
                                                    <rect width="25" height="9" fill="green"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>SOMETIMES
                                        <fo:inline>
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="9">
                                                    <rect width="25" height="9" fill="yellow"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>

                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>RARELY
                                        <fo:inline>
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="9">
                                                    <rect width="25" height="9" fill="#FF9933"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>NOT YET
                                        <fo:inline>
                                            <fo:instream-foreign-object>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="9">
                                                    <rect width="25" height="9" fill="pink"/>
                                                </svg>
                                            </fo:instream-foreign-object>
                                        </fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block font-size="10pt" font-family="Times New Roman, Serif" space-before="18mm" th:if="${model.body.termSlug == 't1'}">
                    <!-- Outer Box for Term 1 Attendance -->
                    <fo:block border="1pt solid black" padding="5pt" margin-bottom="10pt">
                        <fo:block font-weight="bold" text-align="center" background-color="darkorange" border = "1pt solid black">ATTENDANCE:TERM 1 Total No.of Working Days-
                            <!-- Correct use of fo:inline within a fo:block -->
                            <fo:inline th:text="${model.body.term1AttendanceTotal.term1TotalWorkingDays != null ? model.body.term1AttendanceTotal.term1TotalWorkingDays : ''}"></fo:inline>
                        </fo:block>

                        <fo:block padding-before = "-1mm" margin-left = "-1mm">

                            <!-- Single Row Table Inside the Box -->
                            <fo:table table-layout="fixed" width="100%" border-collapse="separate" border-spacing="2mm">
                                <!-- Define Columns -->
                                <fo:table-column column-width="13%"/>
                                <fo:table-column column-width="9.67%"/> <!-- March -->
                                <fo:table-column column-width="9.67%"/> <!-- April -->
                                <fo:table-column column-width="9.67%"/> <!-- May -->
                                <fo:table-column column-width="9.67%"/> <!-- June -->
                                <fo:table-column column-width="9.67%"/> <!-- July -->
                                <fo:table-column column-width="9.67%"/> <!-- August -->
                                <fo:table-column column-width="9.67%"/> <!-- September -->
                                <fo:table-column column-width="9.67%"/> <!-- October -->
                                <fo:table-column column-width="9.67%"/> <!-- Total -->

                                <fo:table-body>
                                    <fo:table-row>
                                        <!-- Each cell combines Month and Present Days -->
                                        <fo:table-cell background-color="lightblue">
                                            <fo:block text-align="center" font-weight="bold">Month</fo:block>
                                            <fo:block text-align="center" font-weight="bold" space-before="1mm">Present Days</fo:block>
                                        </fo:table-cell>
                                        <fo:table-cell>
                                            <fo:block text-align="center" font-weight="bold">March</fo:block>
                                            <fo:block text-align="center" space-before="1mm" th:text="${model.body.attendanceTable.npdMarch}"></fo:block>
                                        </fo:table-cell>
                                        <fo:table-cell>
                                            <fo:block text-align="center" font-weight="bold">April</fo:block>
                                            <fo:block text-align="center" space-before="1mm" th:text="${model.body.attendanceTable.npdApril}"></fo:block>
                                        </fo:table-cell>
                                        <fo:table-cell>
                                            <fo:block text-align="center" font-weight="bold">May</fo:block>
                                            <fo:block text-align="center" space-before="1mm" th:text="${model.body.attendanceTable.npdMay}"></fo:block>
                                        </fo:table-cell>
                                        <fo:table-cell>
                                            <fo:block text-align="center" font-weight="bold">June</fo:block>
                                            <fo:block text-align="center" space-before="1mm" th:text="${model.body.attendanceTable.npdJune}"></fo:block>
                                        </fo:table-cell>
                                        <fo:table-cell>
                                            <fo:block text-align="center" font-weight="bold">July</fo:block>
                                            <fo:block text-align="center" space-before="1mm" th:text="${model.body.attendanceTable.npdJuly}"></fo:block>
                                        </fo:table-cell>
                                        <fo:table-cell>
                                            <fo:block text-align="center" font-weight="bold">August</fo:block>
                                            <fo:block text-align="center" space-before="1mm" th:text="${model.body.attendanceTable.npdAug}"></fo:block>
                                        </fo:table-cell>
                                        <fo:table-cell>
                                            <fo:block text-align="center" font-weight="bold">September</fo:block>
                                            <fo:block text-align="center" space-before="1mm" th:text="${model.body.attendanceTable.npdSept}"></fo:block>
                                        </fo:table-cell>
                                        <fo:table-cell>
                                            <fo:block text-align="center" font-weight="bold">October</fo:block>
                                            <fo:block text-align="center" space-before="1mm" th:text="${model.body.attendanceTable.npdOct}"></fo:block>
                                        </fo:table-cell>
                                        <fo:table-cell>
                                            <fo:block text-align="center" font-weight="bold">Total</fo:block>
                                            <fo:block text-align="center" space-before="1mm" th:text="${model.body.term1AttendanceTotal.term1TotalPresentDays}"></fo:block>
                                        </fo:table-cell>
                                    </fo:table-row>
                                </fo:table-body>
                            </fo:table>
                        </fo:block>
                    </fo:block>
                </fo:block>

                <fo:block font-size="10pt" font-family="Times New Roman, Serif" space-before="18mm" th:if="${model.body.termSlug == 't2'}">
                    <!-- Outer Box for Term 2 Attendance -->
                    <fo:block border="1pt solid black" padding="5pt" margin-bottom="10pt">
                        <fo:block font-weight="bold" text-align="center" background-color="darkorange" border = "1pt solid black">ATTENDANCE:TERM 2 Total No.of Working Days-
                            <!-- Correct use of fo:inline within a fo:block -->
                            <fo:inline th:text="${model.body.term2AttendanceTotal.term2TotalWorkingDays != null ? model.body.term2AttendanceTotal.term2TotalWorkingDays : ''}"></fo:inline>
                        </fo:block>

                        <fo:block padding-top = "-1mm" margin-left ="-1mm">
                            <!-- Single Row Table Inside the Box -->
                            <fo:table table-layout="fixed" width="100%" border-collapse="separate" border-spacing="2mm">
                                <!-- Define Columns -->
                                <fo:table-column column-width="18%"/> <!-- First column -->
                                <fo:table-column column-width="11.9%"/> <!-- October -->
                                <fo:table-column column-width="11.9%"/> <!-- November -->
                                <fo:table-column column-width="11.9%"/> <!-- December -->
                                <fo:table-column column-width="11.9%"/> <!-- January -->
                                <fo:table-column column-width="11.9%"/> <!-- February -->
                                <fo:table-column column-width="11.9%"/> <!-- March -->
                                <fo:table-column column-width="11.9%"/> <!-- Total -->
                                <fo:table-body>
                                    <fo:table-row>
                                        <!-- Each cell combines Month and Present Days -->
                                        <fo:table-cell background-color="lightblue">
                                            <fo:block text-align="center" font-weight="bold">Month</fo:block>
                                            <fo:block text-align="center" font-weight="bold" space-before="1mm">Present Days</fo:block>
                                        </fo:table-cell>
                                        <fo:table-cell>
                                            <fo:block text-align="center" font-weight="bold">October</fo:block>
                                            <fo:block text-align="center" space-before="1mm" th:text="${model.body.attendanceTable.npdOct}"></fo:block>
                                        </fo:table-cell>
                                        <fo:table-cell>
                                            <fo:block text-align="center" font-weight="bold">November</fo:block>
                                            <fo:block text-align="center" space-before="1mm" th:text="${model.body.attendanceTable.npdNov}"></fo:block>
                                        </fo:table-cell>
                                        <fo:table-cell>
                                            <fo:block text-align="center" font-weight="bold">December</fo:block>
                                            <fo:block text-align="center" space-before="1mm" th:text="${model.body.attendanceTable.npdDec}"></fo:block>
                                        </fo:table-cell>
                                        <fo:table-cell>
                                            <fo:block text-align="center" font-weight="bold">January</fo:block>
                                            <fo:block text-align="center" space-before="1mm" th:text="${model.body.attendanceTable.npdJan}"></fo:block>
                                        </fo:table-cell>
                                        <fo:table-cell>
                                            <fo:block text-align="center" font-weight="bold">February</fo:block>
                                            <fo:block text-align="center" space-before="1mm" th:text="${model.body.attendanceTable.npdFeb}"></fo:block>
                                        </fo:table-cell>
                                        <fo:table-cell>
                                            <fo:block text-align="center" font-weight="bold">March</fo:block>
                                            <fo:block text-align="center" space-before="1mm" th:text="${model.body.attendanceTable.npdMarch}"></fo:block>
                                        </fo:table-cell>
                                        <fo:table-cell>
                                            <fo:block text-align="center" font-weight="bold">Total</fo:block>
                                            <fo:block text-align="center" space-before="1mm" th:text="${model.body.term2AttendanceTotal.term2TotalPresentDays}"></fo:block>
                                        </fo:table-cell>
                                    </fo:table-row>
                                </fo:table-body>
                            </fo:table>
                        </fo:block>
                    </fo:block>
                </fo:block>



                <fo:block font-weight="bold" border-width="1mm" font-size="10pt" font-family="Times New Roman, Serif" space-before="15mm">
                    <fo:table>
                        <fo:table-column column-width="43%"/>
                        <fo:table-column column-width="43%"/>
                        <fo:table-column column-width="30%"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block>Principal </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>Vice Principal </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>Class Teacher </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>