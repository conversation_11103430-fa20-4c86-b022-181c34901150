<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="10mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="0cm">
                <fo:table border="none">
                    <fo:table-column column-width="32.4mm" />
                    <fo:table-column column-width="125mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block th:if="${model.body.orgSlug != null && !(#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}">
                                    <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/dps_logo_5.jpeg")'
                                                         content-width="75px"  content-height="75px"  />
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal988947', 'pal233196'}, model.body.orgSlug))}">
                                    <fo:external-graphic src='url("https://images.wexledu.com/live-worksheets/wexl-internal/20240901043131721.png")'
                                                         content-width="75px" scaling="non-uniform"  />
                                </fo:block>
                                <fo:block th:if="${model.body.orgSlug != null && (#lists.contains({'pal174599', 'pal332908', 'pal454783'}, model.body.orgSlug))}">
                                    <fo:external-graphic src='url(https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS.png)'
                                                         content-width="75px" scaling="non-uniform"  />
                                </fo:block>
                                <fo:block margin-left="163mm" padding-top="-28mm" th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}" >
                                    <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/30+Years+Logo+White.png")'
                                                         content-width="75px" scaling="non-uniform"  />
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="3mm">
                                <fo:block font-size="15pt" font-weight="bold" font-family="Arial, sans-serif" text-align="center" space-after="12pt"
                                          th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block>
                                <fo:block th:replace="report-card/dps/fragment.xml :: ${model.body.orgSlug}"></fo:block>

                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${ model.body.orgSlug == 'del189476' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630520 </fo:block>
                                    <fo:block> School Code:56955 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal454783'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630333 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal988947'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630095
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal233196'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :130145
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal174599'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630290
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal556078'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${model.body.orgSlug == 'pal332908'}">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :
                                    </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-before="5pt"
                                          th:if="${ model.body.orgSlug == 'del765517' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630285 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-before="5pt"
                                          th:if="${ model.body.orgSlug == 'del909850' }">
                                    <fo:block>Affiliated to CBSE,New Delhi,Affiliation No :3630448 </fo:block>
                                    <fo:block>  ISO 9001:2005, ISO 45001:2018, ISO 21001:2018 </fo:block>
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:if="${!(model.body.orgSlug matches 'pal556078|del765517|del909850|del189476|pal332908|pal174599|pal233196|pal988947|pal454783')}">
                                    <fo:block th:text="${model.header.isoData}"></fo:block>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}"
                        space-before="-28mm" border="0.3mm solid black"> </fo:block>
                <fo:block th:if="${model.body.orgSlug != null && !(#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196'}, model.body.orgSlug))}" border="0.3mm solid black"></fo:block>
                <fo:block border-width="1mm" font-size="9pt" font-family="Arial, sans-serif" space-after="5pt" space-before="2mm">
                    <fo:block font-size="11pt" font-weight="bold" font-family="Arial, sans-serif" space-after="5pt" text-align="center">
                        STUDENT E - ADMIT CARD
                    </fo:block>
                    <fo:block font-size="9pt" font-family="Arial, sans-serif" space-after="5pt" text-align="center" font-weight="bold">
                        (Card No:
                        <fo:inline th:text="${model.body.eCard}"></fo:inline>
                        <fo:inline>)</fo:inline>
                    </fo:block>
                    <fo:table border="none">
                        <fo:table-column column-width="32mm"/>
                        <fo:table-column column-width="2mm"/>
                        <fo:table-column column-width="60mm"/>
                        <fo:table-column column-width="27mm"/>
                        <fo:table-column column-width="2mm"/>
                        <fo:table-column column-width="65mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding="2mm 0">
                                    <fo:block>Class Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">
                                        <fo:inline th:text="${model.body.sectionName}"></fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding="2mm 0">
                                    <fo:block>Section Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">
                                        <fo:inline th:text="${model.body.className}"></fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding="2mm 0">
                                    <fo:block>Student ID</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">
                                        <fo:inline th:text="${model.body.admissionNumber}"></fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding="2mm 0">
                                    <fo:block>Roll No.</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">
                                        <fo:inline th:text="${model.body.rollNumber}"></fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding="2mm 0">
                                    <fo:block>Student Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">
                                        <fo:inline th:text="${model.body.name}"></fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding="2mm 0">
                                    <fo:block>Father's Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0">
                                    <fo:block font-weight="bold">:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="2mm 0" >
                                    <fo:block font-weight="bold">
                                        <fo:inline th:text="${model.body.fathersName}" ></fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block space-after="5pt">
                    <fo:table border="none" font-size="9pt">
                        <fo:table-column column-width="32mm"/>
                        <fo:table-column column-width="2mm"/>
                        <fo:table-column column-width="auto"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding="0.5mm 0">
                                    <fo:block>Examination Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="0.5mm 0">
                                    <fo:block font-weight="bold">:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="0.5mm 0">
                                    <fo:block font-weight="bold">
                                        <fo:inline th:text="${model.body.examinationName}"></fo:inline>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block border-width="1mm" font-size="9pt" space-before="6" text-align="center" font-family="Arial, sans-serif">
                    <fo:table border="1pt solid black" text-align="left">
                        <fo:table-column column-width="37mm"/>
                        <fo:table-column column-width="50mm"/>
                        <fo:table-column column-width="33mm"/>
                        <fo:table-column column-width="33mm"/>
                        <fo:table-column column-width="36mm"/>
                        <fo:table-header font-size="9pt">
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Date</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Subject Name</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Start Time</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>End Time</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Invigilator Signature</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body font-size="9pt" text-align="left">
                            <fo:table-row th:each="marks : ${model.body.firstTable.marks}" font-weight="bold">
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.date}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" >
                                    <fo:block th:text="${marks.subjectName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.startTime}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.endTime}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.invigilator}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block th:if="${model.body.orgSlug != null && !(#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal454783', 'pal233196', 'del217242'}, model.body.orgSlug))}"
                        border-width="1mm" font-size="10pt" space-before="13mm" font-family="Arial, sans-serif">
                    <fo:table>
                        <fo:table-column/>
                        <fo:table-column/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell padding="0 2mm" font-weight="bold">
                                    <fo:block>Teacher Sign</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-left="58mm" font-weight="bold">
                                    <fo:block>Principal Sign</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block th:if="${model.body.orgSlug != null && (#lists.contains({'pal556078', 'pal174599', 'pal332908', 'pal988947', 'pal233196'}, model.body.orgSlug))}"
                          border-width="1mm" font-size="10pt" space-before="13mm" font-family="Arial, sans-serif">
                    <fo:table>
                        <fo:table-column/>
                        <fo:table-column/>
                        <fo:table-column/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell padding="0 2mm" font-weight="bold">
                                    <fo:block>Teacher Sign</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-left="20mm" font-weight="bold">
                                    <fo:block>Accountant Sign</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-left="42mm" font-weight="bold">
                                    <fo:block>Principal</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block th:if="${model.body.orgSlug == 'del217242'}"
                          border-width="1mm" font-size="10pt" space-before="13mm" font-family="Arial, sans-serif">
                    <fo:table>
                        <fo:table-column/>
                        <fo:table-column/>
                        <fo:table-column/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell padding="0 2mm" font-weight="bold">
                                    <fo:block>Class Teacher</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-left="18mm" font-weight="bold">
                                    <fo:block>Vice Principal</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-left="39mm" font-weight="bold">
                                    <fo:block>Principal</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block th:if="${model.body.orgSlug != null && (#lists.contains({'pal454783'}, model.body.orgSlug))}"
                          border-width="1mm" font-size="10pt" space-after="2mm" font-family="Arial, sans-serif">
                    <fo:table>
                        <fo:table-column/>
                        <fo:table-column/>
                        <fo:table-column/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block>
                                        <fo:block-container padding-left="25mm" padding-top="-7mm" width="100mm" height="35mm" display-align="center" text-align="center">
                                            <fo:block>
                                                <fo:external-graphic src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/signature.png')"
                                                                     scaling="non-uniform"
                                                                     content-width="32mm"
                                                                     content-height="14mm" />
                                            </fo:block>
                                        </fo:block-container>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell padding-top="-14mm" padding="0 2mm" font-weight="bold">
                                    <fo:block>Teacher Sign</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="-14mm" padding-left="19mm" font-weight="bold">
                                    <fo:block>Accountant Sign</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-top="-14mm" padding-left="40mm" font-weight="bold">
                                    <fo:block>Principal</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell number-columns-spanned="3" padding-top="-10mm">
                                    <fo:block border="0.3mm solid black" space-after="5" space-before="2"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block th:if="${model.body.orgSlug != null && !(#lists.contains({'pal454783'}, model.body.orgSlug))}"
                        border="0.3mm solid black" space-after="5" space-before="2"></fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>