<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="report-card" page-width="210mm" page-height="297mm">
            <fo:region-body margin="20mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="report-card">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="0cm" border="2pt solid black" padding="8mm">
                <!-- Logo -->
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="400%" content-height="400%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="8"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="8"/>
                                            <feFuncB type="linear" slope="8"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter)" x="0" y="0" width="100%" height="100%" xlink:href="https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/DPS.svg"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
            <fo:block-container position="absolute" top="0.5mm" left="0.1mm" width="15mm" height="15mm" >
                <fo:block>
                    <fo:external-graphic src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/DPS.svg')" content-width="15%" content-height="auto"/>
                </fo:block>
            </fo:block-container>

            <!-- Header Section -->
            <fo:block font-family="Arial" font-size="12pt" text-align="center" space-after="1cm">
                <fo:block font-size="20pt" font-weight="bold" space-after="0.5cm">Delhi Public School Nacharam</fo:block>
                <fo:block>PLOT NO:44,42A,BEHIND NACHARAM TELEPHONE EXCHANGE </fo:block>
                <fo:block space-after="0.3cm">UPPAL(M), MEDCHAL DISTRICT, HYDERABAD - 500076</fo:block>
                <fo:block>Affiliated to CBSE, New Delhi, Affiliation No : 3630057</fo:block>
                <fo:block space-after="0.3cm">ISO 9001:2005, ISO 45001:2018, ISO 21001:2018</fo:block>
                <fo:block font-size="14pt" font-weight="bold" space-after="0.5cm">QUALITATIVE COMPARATIVE ANALYSIS</fo:block>
                <fo:block font-size="17pt" space-after="0.5cm">Scholastic Assessment</fo:block>
                <fo:block font-weight="bold">(PERCENTAGE ANALYSIS OF TERM - 1 AND TERM - 2)</fo:block>
            </fo:block>

            <!-- Student Information -->
            <fo:block font-family="Arial" font-size="10pt" font-weight="bold" space-after="0.5cm">
                <fo:block space-after="0.3cm">
                    <fo:inline>ACADEMIC YEAR </fo:inline>
                    <fo:inline padding-left="0.2cm">:</fo:inline>
                    <fo:inline th:text="${academicYear}"/>
                    <fo:inline padding-left="6.35cm">CAMPUS </fo:inline>
                    <fo:inline padding-left="0.95cm">:</fo:inline>
                    <fo:inline th:text="${campus}"/>
                </fo:block>
                <fo:block space-after="0.3cm">
                    <fo:inline>STUDENT ID </fo:inline>
                    <fo:inline padding-left="1.1cm">: </fo:inline>
                    <fo:inline th:text="${studentId}"/>
                    <fo:inline padding-left="6.3cm">CURRICULUM </fo:inline>
                    <fo:inline >:</fo:inline>
                    <fo:inline th:text="${curriculum}"/>
                </fo:block>
                <fo:block>
                    <fo:inline>STUDENT NAME </fo:inline>
                    <fo:inline padding-left="0.4cm">: </fo:inline>
                    <fo:inline th:text="${studentName}"/>
                    <fo:inline padding-left="1.1cm">CLASS </fo:inline>
                    <fo:inline padding-left="1.35cm">:</fo:inline>
                    <fo:inline th:text="${class}"/>
                </fo:block>
            </fo:block>

                <!-- Bar Chart -->
                <fo:block text-align="center" space-before="0.5cm">
                    <fo:instream-foreign-object content-width="400%" content-height="400px">
                        <svg xmlns="http://www.w3.org/2000/svg" width="800%" height="800px" viewBox="0 0 850 300">
                            <!-- Y-Axis -->
                            <line x1="70" y1="60" x2="70" y2="370" stroke="black" stroke-width="3"/>
                            <!-- X-Axis -->
                            <line x1="70" y1="370" x2="800" y2="370" stroke="black" stroke-width="3"/>
                            <!-- Bars for Term I -->

                            <rect x="75"  y="246" width="20" height="124" fill="grey"/>
                            <rect x="100" y="185" width="20" height="185" fill="grey"/>
                            <rect x="200" y="130" width="20" height="240" fill="grey"/>
                            <rect x="225" y="130" width="20" height="240" fill="grey"/>
                            <rect x="325" y="90" width="20" height="280" fill="grey"/>
                            <rect x="350" y="90" width="20" height="280" fill="grey"/>
                            <rect x="450" y="140" width="20" height="230" fill="grey"/>
                            <rect x="475" y="140" width="20" height="230" fill="grey"/>
                            <rect x="575" y="110" width="20" height="260" fill="grey"/>
                            <rect x="600" y="110" width="20" height="260" fill="grey"/>
                            <rect x="700" y="120" width="20" height="250" fill="grey"/>
                            <rect x="725" y="120" width="20" height="250" fill="grey"/>
                            <!-- Bars for Term II -->
                            <rect x="125" y="140" width="20" height="230" fill="black"/>
                            <rect x="150" y="140" width="20" height="230" fill="black"/>
                            <rect x="250" y="90" width="20" height="280" fill="black"/>
                            <rect x="275" y="90" width="20" height="280" fill="black"/>
                            <rect x="375" y="70" width="20" height="300" fill="black"/>
                            <rect x="400" y="70" width="20" height="300" fill="black"/>
                            <rect x="500" y="130" width="20" height="240" fill="black"/>
                            <rect x="525" y="130" width="20" height="240" fill="black"/>
                            <rect x="625" y="100" width="20" height="270" fill="black"/>
                            <rect x="650" y="100" width="20" height="270" fill="black"/>
                            <rect x="750" y="110" width="20" height="260" fill="black"/>
                            <rect x="775" y="110" width="20" height="260" fill="black"/>
                            <!--X-Axis Labels -->
                            <text x="100" y="390" font-size="14" text-anchor="inherit">ENGLISH</text>
                            <text x="230" y="390" font-size="14" text-anchor="inherit">II LANG</text>
                            <text x="350" y="390" font-size="14" text-anchor="inherit">III LANG</text>
                            <text x="450" y="390" font-size="14" text-anchor="inherit">MATHEMATICS</text>
                            <text x="590" y="390" font-size="14" text-anchor="inherit">SCIENCE</text>
                            <text x="700" y="390" font-size="14" text-anchor="inherit">SOCIAL SCIENCE</text>
                            <!-- Y-Axis Labels -->
                            <text x="60" y="370" font-size="14" text-anchor="end">0</text>
                            <text x="60" y="340" font-size="14" text-anchor="end">10</text>
                            <text x="60" y="310" font-size="14" text-anchor="end">20</text>
                            <text x="60" y="280" font-size="14" text-anchor="end">30</text>
                            <text x="60" y="250" font-size="14" text-anchor="end">40</text>
                            <text x="60" y="220" font-size="14" text-anchor="end">50</text>
                            <text x="60" y="190" font-size="14" text-anchor="end">60</text>
                            <text x="60" y="160" font-size="14" text-anchor="end">70</text>
                            <text x="60" y="130" font-size="14" text-anchor="end">80</text>
                            <text x="60" y="100" font-size="14" text-anchor="end">90</text>
                            <text x="60" y="70" font-size="14" text-anchor="end">100</text>
                        </svg>
                    </fo:instream-foreign-object>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>
