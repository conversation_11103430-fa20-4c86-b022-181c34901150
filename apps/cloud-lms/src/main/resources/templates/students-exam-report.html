<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Student Exam Results</title>
    <div th:if="${includeMathJax}">
        <script type="text/javascript" async
                src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js">
        </script>
    </div>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        table, th, td {
            border: 1px solid black;
        }

        th, td {
            padding: 8px;
            text-align: left;
        }

        .student-page {
            page-break-after: always;
        }

        @media print {
            /* Additional print styles can go here */
        }
    </style>
</head>
<body>
<div th:each="student : ${students}" class="student-page">
    <!-- Summary Table -->
    <table>
        <tr>
            <th><b>Student Name</b></th>
            <td th:text="${student.name}"></td>
            <th><b>Grade</b></th>
            <td th:text="${student.grade}"></td>
        </tr>
        <tr>
            <th><b>Subject</b></th>
            <td th:text="${student.subject}"></td>
            <th><b>Admission Number</b></th>
            <td th:text="${student.admissionNumber}"></td>
        </tr>
        <tr>
            <th><b>Test Name</b></th>
            <td th:text="${student.testName}"></td>
            <th><b>Total Number of Questions</b></th>
            <td th:text="${student.totalNumberOfQuestions}"></td>
        </tr>
    </table>

    <!-- Detailed Table -->
    <table>
        <tr>
            <th><b>Question Number</b></th>
            <th><b>AI Analysis Performed</b></th>
            <th><b>Total Marks For Each Question</b></th>
            <th><b>AI Suggested Marks </b></th>
            <th><b>Teacher Marks</b></th>
        </tr>
        <tr th:each="answer : ${student.answers}">
            <td th:text="${answer.questionNumber}"></td>
            <td th:utext="${answer.aiAnalysisPerformed}"></td>
            <td th:text="${answer.totalMarksEachQuestion}"></td>
            <td th:text="${answer.marksAttained}"></td>
            <td></td>
        </tr>
        <tr>
            <th><b></b></th>
            <th><b>Total Marks</b></th>
            <td th:text="${student.totalMarks}"></td>
            <td th:text="${student.totalAiAnalysisMarks}"></td>
            <th><b></b></th>
        </tr>
    </table>
</div>
</body>
</html>
