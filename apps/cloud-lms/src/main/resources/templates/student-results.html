<!DOCTYPE html>
<html lang="en">
<meta charset="UTF-8">
<title>Student Results</title>
<meta name="viewport" content="width=device-width,initial-scale=1">
<style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap');
    body {margin:0;font-family: 'Poppins', sans-serif;font-size: 15px;}
    .container {width: 90%;margin:0 auto;}
    .table-responsive {display: block;width: 100%;overflow-x: auto;-webkit-overflow-scrolling: touch}
    .ttl {margin:25px 0 15px;font-weight: 500;font-size: 26px;text-align: center;}
    .table {border: 2px solid #f6b519;width: 100%;border-radius: 5px;min-width: 1000px;overflow-x:auto;}
    .table tr:nth-child(odd) {background-color: #f5f5f5;}
    .table th {padding: 0 10px;background-color: #f6b519;color: #fff;height: 50px;text-align: left;}
    .table td {height: 50px;padding: 0 10px;font-size: 15px;}
    ::-webkit-scrollbar {width: 5px;height:5px}
    ::-webkit-scrollbar-track {background: #f1f1f1; }
    ::-webkit-scrollbar-thumb {background: #888; border-radius: 10px;}
    ::-webkit-scrollbar-thumb:hover {background: #555;}
</style>
<body>
<div class="container">
    <h1 class="ttl">Name : <span th:text="${studentName}"></span></h1>
    <h1 class="ttl"><span th:text="${testName} + ' on ' + ${scheduleDate}"></span></h1>
    <table style="width:80%" border="1"
           class = "table table-striped table-responsive-md">
        <thead>
        <tr>
            <th>Section Name</th>
            <th>Total Marks</th>
            <th>Marks secured</th>
            <th>Attempted</th>
            <th>Not attempted</th>
        </tr>
        </thead>
        <tbody>
        <tr th:each="result:${results}" th:style="'font-weight: normal;'">
            <td th:text="${result.sectionName}"></td>
            <td th:text="${result.totalMarks}"></td>
            <td th:text="${result.securedMarks}"></td>
            <td th:text="${result.attempted}"></td>
            <td th:text="${result.notAttempted}"></td>
        </tr>
        </tbody>
    </table>
</div>
</body>
</html>
